<template>
  <div class="my-profile">
    <div class="item" v-for="(item, index) in profileList" :key="index">
      <div>
        {{ item.name }}
      </div>
      <div>
        <span v-if="!editType[index]" class="cont">{{ item.value }}</span>
        <template v-else>
          <div class="edit-container" v-if="index === 0">
            <el-input class="edit-ipt" v-model="nicknameIpt"></el-input>
            <div class="sure-btn" @click="handleSure(index)">确认</div>
            <div class="cancel-btn" @click="handleExit(index)">取消</div>
          </div>
          <div class="edit-container" v-if="index === 1">
            <el-select class="edit-ipt" v-model="sex" placeholder="请选择性别">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
            <div class="sure-btn" @click="handleSure(index)">确认</div>
            <div class="cancel-btn" @click="handleExit(index)">取消</div>
          </div>
          <div
            class="edit-container"
            style="flex-direction: column"
            v-if="index === 2">
            <el-input
              v-model="intro"
              type="textarea"
              style="width: 200px"
              :rows="3"
              show-word-limit
              maxlength="300"
              placeholder="请输入简介" />
            <div
              style="
                width: 100%;
                display: flex;
                flex-direction: row-reverse;
                margin-top: 5px;
              ">
              <div class="cancel-btn" @click="handleExit(index)">取消</div>
              <div class="sure-btn" @click="handleSure(index)">确认</div>
            </div>
          </div>
          <div class="edit-container" v-if="index === 3" style="display: flex">
            <el-cascader
              v-model="value"
              :options="options1"
              :props="{
                label: 'label',
                value: 'label',
              }"
              style="height: 30px" />
            <div class="sure-btn" @click="handleSure(index)">确认</div>
            <div class="cancel-btn" @click="handleExit(index)">取消</div>
          </div>
        </template>
      </div>
      <div
        class="center edit"
        v-if="!editType[index] && index <= 3"
        @click="handleEdit(index)">
        <img
          style="width: 15px; margin-right: 4px"
          src="@/assets/images/edit.png" />
        <span>编辑</span>
      </div>
      <div
        class="center send-code"
        @click="sendCode()"
        v-if="index === 4 && code === '咕咕客'">
        <img
          style="width: 15px; margin-right: 4px"
          src="@/assets/images/edit.png" />
        <span>发送验证码</span>
      </div>
      <div
        class="center link-btn"
        @click="copyLink"
        v-else-if="index === 4 && code">
        <img
          style="width: 18px; margin-right: 4px"
          src="@/assets/images/copy.png" />
        <span>复制</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { wxCode } from "@/api/broadcast";
import useUserStore from "@/store/modules/user";
import { updateInfo } from "@/api/login.js";
import { ElMessage } from "element-plus";
import ClipboardJS from "clipboard";
import address from "@/constants/address.js";
import dayjs from "dayjs";

const sex = ref("");
const userStore = useUserStore();
const editType = ref([]);
const nicknameIpt = ref("");
const nickname = computed(() => userStore.userInfo.nickname);
const sexComp = computed(() => (sex.value ? sex.value : "未选择"));
const introComp = computed(() => (intro.value ? intro.value : "未填写"));
const valueComp = computed(() => {
  if (Array.isArray(value.value)) {
    return value.value.join(" - ");
  } else {
    return "未选择";
  }
});
const code = ref("咕咕客");
const codeValue = computed(() => code.value);
const intro = ref("");
const options = [
  {
    label: "男",
    value: "男",
  },
  {
    label: "女",
    value: "女",
  },
];
const value = ref("");
const profileList = ref([
  { name: "用户名", value: nickname },
  { name: "性别", value: sexComp },
  { name: "个人简介", value: introComp },
  { name: "所在地区", value: valueComp },
  { name: "关联服务", value: codeValue },
  {
    name: "创建时间",
    value: dayjs(userStore.userInfo.created_at).format("YYYY-MM-DD HH:mm:ss"),
  },
]);

const options1 = address;
const { proxy } = getCurrentInstance();

const handleChange = (value) => {
  console.log(value);
};
onMounted(() => {
  nicknameIpt.value = userStore.userInfo.nickname;
  if (userStore.userInfo.user_info) {
    sex.value = JSON.parse(userStore.userInfo.user_info).sex;
    intro.value = JSON.parse(userStore.userInfo.user_info).intro;
    value.value = JSON.parse(userStore.userInfo.user_info).value;
  }
});

const handleEdit = (index) => {
  editType.value.forEach((item, i) => {
    editType.value[i] = false;
  });
  editType.value[index] = true;
};

const handleSure = (index) => {
  if (index === 0) {
    updateInfo({
      nickname: nicknameIpt.value,
      email: userStore.userInfo.email,
    }).then((res) => {
      editType.value[0] = false;
      if (res.code === 200) {
        userStore.getInfo();
      }
    });
  } else {
    updateInfo({
      user_info: JSON.stringify({
        sex: sex.value,
        intro: intro.value,
        address: value.value,
      }),
    }).then((res) => {
      editType.value[index] = false;
      if (res.code === 200) {
        userStore.getInfo();
      }
    });
  }
};
const handleExit = (index) => {
  editType.value[index] = false;
};

// 复制链接
const copyLink = () => {
  const clipboard = new ClipboardJS(".link-btn", {
    text: () => code.value,
  });
  clipboard.on("success", () => {
    proxy.$modal.msgSuccess("链接复制成功");
    clipboard.destroy();
  });

  clipboard.on("error", (e) => {
    console.error("复制失败:", e);
    clipboard.destroy();
  });
};

const sendCode = () => {
  wxCode().then((res) => {
    if (res.code === 200) {
      code.value = res.data.code;
      ElMessage.success(res.data.message);
    }
  });
};
</script>
<style scoped lang="scss">
.my-profile {
  padding: 20px 20px 0;
  width: 100%;

  .item {
    margin-bottom: 27px;
    display: flex;
    align-items: center;

    & > div:nth-child(1) {
      width: 61px;
      text-align-last: justify;
      margin-right: 13px;
      font-weight: 500;
      font-size: 14px;
      color: #3e3e3e;
    }

    & > div:nth-child(2) {
      .cont {
        font-weight: 400;
        font-size: 14px;
        color: #888888;
        margin-right: 53px;
      }

      .sure-btn {
        width: 46px;
        height: 20px;
        background: #55c4d5;
        border-radius: 8px 8px 8px 8px;
        text-align: center;
        line-height: 20px;
        font-size: 12px;
        color: #ffffff;
        margin: 0 9px;
        cursor: pointer;
      }

      .cancel-btn {
        width: 46px;
        height: 20px;
        background: #ffffff;
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #55c4d5;
        text-align: center;
        line-height: 20px;
        font-size: 12px;
        color: #55c4d5;
        cursor: pointer;
      }

      .edit-container {
        display: flex;
        align-items: center;
        .edit-ipt {
          width: 175px;
          height: 32px;
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #eaeaea;
          box-sizing: border-box;
        }
      }
    }

    .edit,
    .send-code {
      cursor: pointer;
      font-weight: 500;
      font-size: 12px;
      color: #0a78b2;
      visibility: hidden;
      span {
        line-height: 16px;
      }
    }
  }

  .item:hover {
    .edit,
    .send-code {
      visibility: visible;
    }
  }
}

.center {
  display: flex;
}
</style>
