#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文章收藏相关的Schema定义
"""
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field

from app.schemas.article.article import ArticleResponse


class UserArticleCollectCreate(BaseModel):
    """创建用户收藏文章的请求Schema"""
    article_id: str = Field(..., description="文章ID")


class UserArticleCollectResponse(BaseModel):
    """用户收藏文章响应Schema"""
    id: str = Field(..., description="收藏记录ID")
    user_id: str = Field(..., description="用户ID")
    article_id: str = Field(..., description="文章ID")
    created_at: datetime = Field(..., description="收藏时间")
    content_type: str = Field(None, description="文章ID")
    # 包含文章详细信息
    article: Optional[ArticleResponse] = Field(None, description="文章详情")

    class Config:
        from_attributes = True


class UserArticleCollectList(BaseModel):
    """用户收藏文章列表响应Schema"""
    collects: List[UserArticleCollectResponse] = Field(..., description="收藏列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页")
    page_size: int = Field(..., description="每页大小") 