{"index_patterns": ["projects-*"], "priority": 100, "template": {"settings": {"number_of_shards": 2, "number_of_replicas": 1, "refresh_interval": "3s", "max_result_window": 10000, "max_ngram_diff": 10, "mapping": {"total_fields": {"limit": 2000}}, "analysis": {"analyzer": {"ik_analyzer": {"type": "custom", "tokenizer": "ik_max_word", "filter": ["lowercase", "asciifolding", "stop", "synonym_filter"]}, "ik_search_analyzer": {"type": "custom", "tokenizer": "ik_smart", "filter": ["lowercase", "asciifolding", "synonym_filter"]}, "ik_precise_analyzer": {"type": "custom", "tokenizer": "ik_smart", "filter": ["lowercase", "asciifolding"]}, "english_analyzer": {"type": "custom", "tokenizer": "standard", "filter": ["lowercase", "asciifolding", "english_stop", "english_stemmer", "synonym_filter"]}, "english_search_analyzer": {"type": "custom", "tokenizer": "standard", "filter": ["lowercase", "asciifolding", "english_stop", "english_stemmer", "synonym_filter"]}, "ngram_analyzer": {"type": "custom", "tokenizer": "ik_max_word", "filter": ["lowercase", "asciifolding", "ngram_filter"]}, "completion_analyzer": {"type": "custom", "tokenizer": "keyword", "filter": ["lowercase", "asciifolding"]}}, "filter": {"ngram_filter": {"type": "ngram", "min_gram": 3, "max_gram": 8}, "synonym_filter": {"type": "synonym", "synonyms": ["js,javascript,JavaScript,JS", "ts,typescript,TypeScript,TS", "py,python,Python,PY", "react,reactjs,React,ReactJS", "vue,vue<PERSON><PERSON>,<PERSON><PERSON>,VueJS", "api,interface,API", "接口,interface", "db,database,DB", "数据库,database", "前端,frontend,front-end", "后端,backend,back-end", "全栈,fullstack,full-stack", "移动端,mobile,手机端", "网站,website,web,网页", "应用,app,application", "程序,application", "框架,framework", "库,library,lib", "工具,tool,utils,utility", "项目,project,proj", "开源,opensource,open-source", "教程,tutorial,guide,指南", "示例,example,demo,演示", "文档,document,doc,docs"]}, "english_stop": {"type": "stop", "stopwords": "_english_"}, "english_stemmer": {"type": "stemmer", "language": "english"}}}}, "mappings": {"properties": {"id": {"type": "keyword"}, "name": {"type": "text", "analyzer": "ik_analyzer", "search_analyzer": "ik_search_analyzer", "term_vector": "with_positions_offsets_payloads", "fields": {"keyword": {"type": "keyword"}, "ik": {"type": "text", "analyzer": "ik_analyzer", "search_analyzer": "ik_search_analyzer", "term_vector": "with_positions_offsets_payloads"}, "english": {"type": "text", "analyzer": "english_analyzer", "search_analyzer": "english_search_analyzer", "term_vector": "with_positions_offsets_payloads"}, "completion": {"type": "completion", "analyzer": "completion_analyzer"}, "ngram": {"type": "text", "analyzer": "ngram_analyzer"}, "suggest": {"type": "search_as_you_type"}}}, "description_project": {"type": "text", "analyzer": "ik_analyzer", "search_analyzer": "ik_search_analyzer", "term_vector": "with_positions_offsets_payloads", "fields": {"ik": {"type": "text", "analyzer": "ik_analyzer", "search_analyzer": "ik_search_analyzer", "term_vector": "with_positions_offsets_payloads"}, "english": {"type": "text", "analyzer": "english_analyzer", "search_analyzer": "english_search_analyzer", "term_vector": "with_positions_offsets_payloads"}, "ngram": {"type": "text", "analyzer": "ngram_analyzer"}}}, "description_recommend": {"type": "text", "analyzer": "ik_analyzer", "search_analyzer": "ik_search_analyzer", "term_vector": "with_positions_offsets_payloads", "fields": {"ik": {"type": "text", "analyzer": "ik_analyzer", "search_analyzer": "ik_search_analyzer", "term_vector": "with_positions_offsets_payloads"}, "english": {"type": "text", "analyzer": "english_analyzer", "search_analyzer": "english_search_analyzer", "term_vector": "with_positions_offsets_payloads"}, "ngram": {"type": "text", "analyzer": "ngram_analyzer"}}}, "tags": {"type": "keyword", "fields": {"ik": {"type": "text", "analyzer": "ik_analyzer", "search_analyzer": "ik_search_analyzer", "term_vector": "with_positions_offsets_payloads"}, "english": {"type": "text", "analyzer": "english_analyzer", "search_analyzer": "english_search_analyzer", "term_vector": "with_positions_offsets_payloads"}, "suggest": {"type": "completion", "analyzer": "completion_analyzer"}}}, "status": {"type": "keyword"}, "cards_count": {"type": "integer"}, "views_count": {"type": "integer"}, "likes_count": {"type": "integer"}, "created_at": {"type": "date"}, "updated_at": {"type": "date"}, "indexed_at": {"type": "date"}, "repository_url": {"type": "keyword"}, "image_url": {"type": "keyword"}, "icon_url": {"type": "keyword"}, "local_path": {"type": "keyword"}, "background_color": {"type": "keyword"}, "button_color": {"type": "keyword"}, "created_by": {"type": "keyword"}, "updated_by": {"type": "keyword"}, "version": {"type": "keyword"}, "architecture_mermaid": {"type": "text"}, "dependency_mermaid": {"type": "text"}, "cards": {"type": "nested", "properties": {"id": {"type": "keyword"}, "project_id": {"type": "keyword"}, "title": {"type": "text", "term_vector": "with_positions_offsets_payloads", "analyzer": "ik_analyzer", "search_analyzer": "ik_search_analyzer", "fields": {"keyword": {"type": "keyword"}, "ik": {"type": "text", "analyzer": "ik_analyzer", "search_analyzer": "ik_search_analyzer", "term_vector": "with_positions_offsets_payloads"}, "english": {"type": "text", "analyzer": "english_analyzer", "search_analyzer": "english_search_analyzer", "term_vector": "with_positions_offsets_payloads"}, "completion": {"type": "completion", "analyzer": "completion_analyzer"}, "ngram": {"type": "text", "analyzer": "ngram_analyzer"}}}, "content": {"type": "text", "term_vector": "with_positions_offsets_payloads", "analyzer": "ik_analyzer", "search_analyzer": "ik_search_analyzer", "fields": {"ik": {"type": "text", "analyzer": "ik_analyzer", "search_analyzer": "ik_search_analyzer", "term_vector": "with_positions_offsets_payloads"}, "english": {"type": "text", "analyzer": "english_analyzer", "search_analyzer": "english_search_analyzer", "term_vector": "with_positions_offsets_payloads"}, "ngram": {"type": "text", "analyzer": "ngram_analyzer"}}}, "sort_order": {"type": "integer"}, "like": {"type": "integer"}, "collect": {"type": "integer"}, "dislike": {"type": "integer"}, "created_at": {"type": "date"}, "updated_at": {"type": "date"}, "indexed_at": {"type": "date"}, "created_by": {"type": "keyword"}, "updated_by": {"type": "keyword"}, "version": {"type": "keyword"}}}}}}}