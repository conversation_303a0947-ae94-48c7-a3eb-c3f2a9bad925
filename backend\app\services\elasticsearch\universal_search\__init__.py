"""
通用搜索模块

提供统一的搜索接口，支持多种内容类型（文章、项目等）的搜索功能。
可以进行单一类型搜索或跨类型混合搜索。

主要组件：
- UniversalSearchEngine: 主搜索引擎
- ContentTypeAdapter: 内容类型适配器基类
- ArticleAdapter/ProjectAdapter: 具体适配器实现
- SearchCoordinator: 搜索协调器
- UnifiedQueryBuilder: 统一查询构建器
- UnifiedResultProcessor: 统一结果处理器
- ResultAggregator: 结果聚合器
- SearchValidationUtils: 验证工具
- SearchConfigUtils: 配置工具
"""

from .core import UniversalSearchEngine
from .adapters import ArticleAdapter, ProjectAdapter
from .types import (
    SearchRequest, SearchResult, ContentItem, ContentType,
    SortOrder, ContentTypeAdapter
)
from .coordinator import SearchCoordinator
from .query_builder import UnifiedQueryBuilder
from .result_processor import UnifiedResultProcessor
from .aggregator import ResultAggregator
from .utils import SearchValidationUtils, SearchConfigUtils, SearchMetricsUtils
from .config import (
    SearchEngineConfig, DEFAULT_SEARCH_CONFIG, CONTENT_TYPE_MAPPINGS,
    SEARCH_STRATEGIES, AGGREGATION_CONFIGS, SORT_STRATEGIES
)

__all__ = [
    # 核心组件
    "UniversalSearchEngine",
    "SearchCoordinator",
    "UnifiedQueryBuilder",
    "UnifiedResultProcessor",
    "ResultAggregator",

    # 适配器
    "ContentTypeAdapter",
    "ArticleAdapter",
    "ProjectAdapter",

    # 数据类型
    "SearchRequest",
    "SearchResult",
    "ContentItem",
    "ContentType",
    "SortOrder",

    # 工具类
    "SearchValidationUtils",
    "SearchConfigUtils",
    "SearchMetricsUtils",

    # 配置
    "SearchEngineConfig",
    "DEFAULT_SEARCH_CONFIG",
    "CONTENT_TYPE_MAPPINGS",
    "SEARCH_STRATEGIES",
    "AGGREGATION_CONFIGS",
    "SORT_STRATEGIES"
]
