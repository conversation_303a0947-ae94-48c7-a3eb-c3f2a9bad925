"""
文章服务
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
from sqlalchemy import select, and_, or_, desc, asc, func, text
from sqlalchemy.orm import selectinload
import structlog

from app.core.di.providers import SessionProvider, AsyncSessionProvider
from app.models.article.article import ArticleModel
from app.models.article.article_collect import UserArticleCollectModel
from app.models.article.comment import CommentModel
from app.models.rbac.user import UserModel
from app.models.statistics import UserFlowLogModel
from app.schemas.article.article import (
    ArticleCreate,
    ArticleUpdate,
    ArticleResponse,
    ArticleListResponse,
    ArticleLikeRequest,
    ArticleShareRequest
)
from app.schemas.article.article_collect import UserArticleCollectList, UserArticleCollectResponse, \
    UserArticleCollectCreate
from app.schemas.article.comment import (
    CommentCreate,
    CommentResponse,
    CommentTreeResponse,
    CommentAuditRequest, CommentSearchResponse, CommentSearchRequest
)
from app.utils.ai_audit_util import AIAuditUtil
from app.utils.flow_log_utils import record_user_flow_log, FlowLogType, record_system_message

logger = structlog.get_logger(__name__)


class ArticleService:
    """文章服务类"""

    def __init__(
            self,
            session: SessionProvider,
            async_session: AsyncSessionProvider
    ):
        """初始化文章服务

        Args:
            session: 同步数据库会话提供者
            async_session: 异步数据库会话提供者
        """
        self.session = session
        self.async_session = async_session
        logger.debug("文章服务初始化完成")

    async def _get_user_last_tags(self, user_id: str) -> Optional[List[str]]:
        """
        🔍 获取用户历史记录中最后一个项目或文章的标签

        逻辑：
        1. 先查找最后访问的项目标签
        2. 如果没有项目历史，查找最后访问的文章标签
        3. 都没有则返回 None
        """
        try:
            # 首先尝试获取最后访问的项目标签
            last_project = await self.github_project_service.get_last_accessed_project(user_id)
            if last_project and last_project.tags:
                logger.info(f"用户 {user_id} 最后访问项目的标签: {last_project.tags}")
                return last_project.tags

            # 如果没有项目历史，尝试获取最后访问的文章标签
            last_article = await self.article_service.get_last_accessed_article(user_id)
            if last_article and last_article.tags:
                logger.info(f"用户 {user_id} 最后访问文章的标签: {last_article.tags}")
                return last_article.tags

            logger.info(f"用户 {user_id} 没有历史标签记录")
            return None

        except Exception as e:
            logger.error(f"获取用户历史标签失败: {str(e)}")
            return None

    async def get_articles_new(
            self,
            page: int = 1,
            size: int = 10,
            status: Optional[str] = None,
            search: Optional[str] = None,
            is_public: Optional[str] = None,
            user_id: Optional[str] = None,
            recommend_tags: Optional[List[str]] = None
    ):
        """
        🆕 新的文章获取方法 - 实现简化的推荐逻辑

        推荐逻辑与项目相同
        """
        try:
            async with self.article_service.async_session() as session:

                # 🎯 **推荐逻辑核心实现**
                final_recommend_tags = None
                order_by_popularity = False

                if user_id:
                    # 情况1：用户已登录 - 获取历史标签
                    final_recommend_tags = await self._get_user_last_tags(user_id)
                elif recommend_tags:
                    # 情况2：未登录但有推荐标签
                    final_recommend_tags = recommend_tags
                else:
                    # 情况3：未登录且无推荐标签 - 按人气排序
                    order_by_popularity = True

                # 构建查询
                query = select(ArticleModel).where(
                    and_(
                        ArticleModel.status == 'published',
                        ArticleModel.is_public == True
                    )
                )

                # 添加其他过滤条件
                if status:
                    query = query.where(ArticleModel.status == status)
                if search:
                    query = query.where(
                        or_(
                            ArticleModel.title.ilike(f"%{search}%"),
                            ArticleModel.content.ilike(f"%{search}%")
                        )
                    )
                if is_public is not None:
                    query = query.where(ArticleModel.is_public == (is_public.lower() == 'true'))

                # 🔍 **标签推荐逻辑**
                if final_recommend_tags:
                    tag_conditions = []
                    for tag in final_recommend_tags:
                        tag_condition = text(f"""(
                            tags::text ILIKE '%"{tag}"%'
                        )""")
                        tag_conditions.append(
                            tag_condition
                        )

                    if tag_conditions:
                        query = query.where(or_(*tag_conditions))

                # 🌟 **排序逻辑**
                # if order_by_popularity:
                #     # 按人气排序（综合考虑阅读量、点赞数、收藏数）
                #     query = query.order_by(
                #         (ArticleModel.read_count + ArticleModel.like_count * 2 + ArticleModel.collect_count * 3).desc(),
                #         ArticleModel.created_at.desc()
                #     )
                # else:
                    # 默认按创建时间排序
                query = query.order_by(ArticleModel.created_at.desc())

                # 计算总数
                count_query = select(func.count()).select_from(query.subquery())
                total_result = await session.execute(count_query)
                total = total_result.scalar() or 0

                # 分页
                query = query.offset((page - 1) * size).limit(size)

                # 执行查询
                result = await session.execute(query)
                db_articles = result.scalars().all()

                # 转换为响应格式（保持与原有接口兼容）
                from app.schemas.article.article import ArticleListResponse, ArticleResponse

                articles = [ArticleResponse.model_validate(article.__dict__) for article in db_articles]

                response = ArticleListResponse(
                    articles=articles,
                    total=total,
                    page=page,
                    size=size
                )

                logger.info(f"获取到 {len(articles)} 篇文章，总数: {total}")
                return response

        except Exception as e:
            logger.error("获取文章列表失败", error=str(e), exc_info=True)
            # 返回空结果但保持格式一致
            from app.schemas.article.article import ArticleListResponse
            return ArticleListResponse(articles=[], total=0, page=page, size=size)

    async def get_user_article_logs_by_log_type(self, page: int, page_size: int, name: Optional[str] = None,
                                                log_type: Optional[str] = None, user_id: Optional[str] = None) -> \
            Dict[str, Any]:
        """获取用户文章流程日志列表

        专门用于处理 article_history_log 类型的日志，从文章表而不是项目表获取数据

        Args:
            page: 页码
            page_size: 每页大小
            name: 文章名称过滤（可选）
            log_type: 日志类型过滤（可选）
            user_id: 用户ID（可选）

        Returns:
            Dict[str, Any]: 包含日志列表和分页信息的字典
        """
        async with self.async_session() as session:
            try:
                # 构建过滤条件
                filter_conditions = []

                # 🆕 **用户ID过滤（必需）**
                if user_id:
                    filter_conditions.append(UserFlowLogModel.created_by == user_id)

                # 名称过滤（针对文章标题）
                if name:
                    filter_conditions.append(UserFlowLogModel.project_name.ilike(f"%{name}%"))

                # 日志类型过滤 - 专门处理文章历史日志
                if log_type:
                    filter_conditions.append(UserFlowLogModel.log_type == log_type)
                else:
                    # 如果没有指定类型，默认只查询文章历史日志
                    filter_conditions.append(UserFlowLogModel.log_type == FlowLogType.ARTICLE_HISTORY)

                # 🔥 **修改查询逻辑：移除时间限制，获取用户所有文章历史记录**
                query = (
                    select(
                        UserFlowLogModel,
                        ArticleModel
                    )
                        .join(ArticleModel, UserFlowLogModel.project_id == ArticleModel.id)
                        .where(and_(*filter_conditions) if filter_conditions else True)
                        .order_by(UserFlowLogModel.created_at.desc())  # 按时间倒序排列
                )

                # 🔢 **先计算总数**
                count_query = select(func.count()).select_from(query.subquery())
                total_result = await session.execute(count_query)
                total = total_result.scalar() or 0

                # 🔄 **应用分页**
                paginated_query = query.offset((page - 1) * page_size).limit(page_size)
                result = await session.execute(paginated_query)
                paginated_logs = result.all()

                # 转换为响应格式
                log_responses = []

                for log, article in paginated_logs:
                    # 转换文章信息
                    article_info = None
                    if article:
                        article_info = {
                            "id": article.id,
                            "title": article.title,
                            "summary": article.summary,
                            "status": article.status,
                            "is_public": article.is_public,
                            "read_count": article.read_count,
                            "like_count": article.like_count,
                            "collect_count": article.collect_count,
                            "comment_count": article.comment_count,
                            "cover_image": article.cover_image,
                            "content_type": "article",
                            "tags": article.tags,
                            "created_at": article.created_at,
                            "updated_at": article.updated_at
                        }

                        # 检查文章是否被该用户收藏
                        if user_id:
                            article_info["is_collected"] = await self._is_article_collected(session, user_id,
                                                                                            article.id)
                        else:
                            article_info["is_collected"] = False

                    # 确定返回类型
                    content_type = "article" if log_type == FlowLogType.ARTICLE_HISTORY else "article"

                    log_responses.append({
                        "id": log.id,
                        "log_type": log.log_type,
                        "project_id": log.project_id,  # 这里实际上是文章ID
                        "project_name": log.project_name,  # 这里实际上是文章标题
                        "article": article_info,  # 文章信息而不是项目信息
                        "content": log.content,
                        "created_at": log.created_at,
                        "created_by": log.created_by,
                        "title": article.title if article else None,
                        "summary": article.summary if article else None,
                        "status": article.status if article else None,
                        "is_public": article.is_public if article else None,
                        "read_count": article.read_count if article else 0,
                        "like_count": article.like_count if article else 0,
                        "collect_count": article.collect_count if article else 0,
                        "comment_count": article.comment_count if article else 0,
                        "cover_image": article.cover_image if article else None,
                        "tags": article.tags if article else [],
                        "content_type": content_type
                    })

                logger.info(f"用户 {user_id} 的文章历史日志查询结果: {len(log_responses)} 条记录，总数: {total}")

                return {
                    "logs": log_responses,
                    "total": total,
                    "page": page,
                    "page_size": page_size,
                    "content_type": "article"
                }

            except Exception as e:
                logger.error("获取用户文章流程日志失败", error=str(e), exc_info=True)
                raise ValueError(f"获取用户文章流程日志失败: {str(e)}")

    # async def get_user_article_logs_by_log_type(self, page: int, page_size: int, name: Optional[str] = None,
    #                                             log_type: Optional[str] = None, user_id: Optional[str] = None) -> \
    #         Dict[str, Any]:
    #     """获取用户文章流程日志列表
    #
    #     专门用于处理 article_history_log 类型的日志，从文章表而不是项目表获取数据
    #
    #     Args:
    #         page: 页码
    #         page_size: 每页大小
    #         name: 文章名称过滤（可选）
    #         log_type: 日志类型过滤（可选）
    #         user_id: 用户ID（可选）
    #
    #     Returns:
    #         Dict[str, Any]: 包含日志列表和分页信息的字典
    #     """
    #     # 导入必要的模型
    #
    #     async with self.async_session() as session:
    #         try:
    #             # 构建过滤条件
    #             filter_conditions = []
    #
    #             # 名称过滤（针对文章标题）
    #             if name:
    #                 filter_conditions.append(UserFlowLogModel.project_name.ilike(f"%{name}%"))
    #
    #             # 日志类型过滤 - 专门处理文章历史日志
    #             if log_type:
    #                 filter_conditions.append(UserFlowLogModel.log_type == log_type)
    #             else:
    #                 # 如果没有指定类型，默认只查询文章历史日志
    #                 filter_conditions.append(UserFlowLogModel.log_type == FlowLogType.ARTICLE_HISTORY)
    #
    #             # 用户过滤
    #             if user_id:
    #                 filter_conditions.append(UserFlowLogModel.created_by == user_id)
    #
    #             # 构建查询 - 与文章表进行 JOIN
    #             # query = (
    #             #     select(
    #             #         UserFlowLogModel,
    #             #         ArticleModel,
    #             #         func.date(UserFlowLogModel.created_at).label('log_date')
    #             #     )
    #             #         .join(ArticleModel, UserFlowLogModel.project_id == ArticleModel.id)
    #             #         .where(and_(*filter_conditions) if filter_conditions else True)
    #             #         .order_by(func.date(UserFlowLogModel.created_at).desc())
    #             #         .distinct(func.date(UserFlowLogModel.created_at))
    #             # )
    #
    #             query = (
    #                 select(
    #                     UserFlowLogModel,
    #                     ArticleModel
    #                 )
    #                     .join(ArticleModel, UserFlowLogModel.project_id == ArticleModel.id)
    #                     .where(and_(*filter_conditions) if filter_conditions else True)
    #                     .order_by(UserFlowLogModel.created_at.desc())  # 按时间倒序排列
    #             )
    #
    #             result = await session.execute(query)
    #             all_logs = result.all()
    #
    #             # 按日期分组并只保留最近7个不同的日期
    #             # unique_dates = set()
    #             # filtered_logs = []
    #             # for log, article, log_date in all_logs:
    #             #     # if log_date not in unique_dates and len(unique_dates) < 7:
    #             #     unique_dates.add(log_date)
    #             #     filtered_logs.append((log, article))
    #
    #             # 计算总数
    #             total = len(filtered_logs)
    #
    #             # 手动分页
    #             start_idx = (page - 1) * page_size
    #             end_idx = start_idx + page_size
    #             paginated_logs = filtered_logs[start_idx:end_idx]
    #
    #             # 转换为响应格式
    #             log_responses = []
    #
    #             for log, article in paginated_logs:
    #                 # 转换文章信息
    #                 article_info = None
    #                 if article:
    #                     article_info = {
    #                         "id": article.id,
    #                         "title": article.title,
    #                         "summary": article.summary,
    #                         "status": article.status,
    #                         "is_public": article.is_public,
    #                         "read_count": article.read_count,
    #                         "like_count": article.like_count,
    #                         "collect_count": article.collect_count,
    #                         "comment_count": article.comment_count,
    #                         "cover_image": article.cover_image,
    #                         "content_type": "article",
    #                         "tags": article.tags,
    #                         "created_at": article.created_at,
    #                         "updated_at": article.updated_at
    #                     }
    #
    #                     # 如果提供了 user_id，检查文章是否被该用户收藏
    #                     if user_id:
    #                         article_info["is_collected"] = await self._is_article_collected(session, user_id,
    #                                                                                         article.id)
    #                     else:
    #                         article_info["is_collected"] = False
    #                 content_type = None
    #                 if log_type == FlowLogType.ARTICLE_HISTORY:
    #                     content_type = "project"
    #                 elif log_type == FlowLogType.ARTICLE_HISTORY:
    #                     content_type = "article"
    #                 log_responses.append({
    #                     "id": log.id,
    #                     "log_type": log.log_type,
    #                     "project_id": log.project_id,  # 这里实际上是文章ID
    #                     "project_name": log.project_name,  # 这里实际上是文章标题
    #                     "article": article_info,  # 文章信息而不是项目信息
    #                     "content": log.content,
    #                     "created_at": log.created_at,
    #                     "created_by": log.created_by,
    #                     "title": article.title,
    #                     "summary": article.summary,
    #                     "status": article.status,
    #                     "is_public": article.is_public,
    #                     "read_count": article.read_count,
    #                     "like_count": article.like_count,
    #                     "collect_count": article.collect_count,
    #                     "comment_count": article.comment_count,
    #                     "cover_image": article.cover_image,
    #                     "tags": article.tags,
    #                     "content_type": content_type
    #                 })
    #
    #             return {
    #                 "logs": log_responses,
    #                 "total": total,
    #                 "page": page,
    #                 "page_size": page_size
    #             }
    #
    #         except Exception as e:
    #             logger.error("获取用户文章流程日志失败", error=str(e))
    #             raise ValueError(f"获取用户文章流程日志失败: {str(e)}")

    async def _is_article_collected(self, session, user_id: str, article_id: str) -> bool:
        """检查文章是否被用户收藏

        Args:
            session: 数据库会话
            user_id: 用户ID
            article_id: 文章ID

        Returns:
            bool: 是否收藏
        """
        query = select(UserArticleCollectModel).where(
            and_(
                UserArticleCollectModel.user_id == user_id,
                UserArticleCollectModel.article_id == article_id
            )
        )
        result = await session.execute(query)
        collect = result.scalar_one_or_none()
        return collect is not None

    async def add_collect(self, user_id: str, collect_data: UserArticleCollectCreate) -> UserArticleCollectResponse:
        """添加收藏文章

        Args:
            user_id: 用户ID
            collect_data: 收藏数据

        Returns:
            UserArticleCollectResponse: 收藏响应数据

        Raises:
            ValueError: 如果文章不存在或已经收藏
        """
        async with self.async_session() as session:
            # 检查文章是否存在
            article_query = select(ArticleModel).where(
                ArticleModel.id == collect_data.article_id
            )
            article_result = await session.execute(article_query)
            article = article_result.scalar_one_or_none()

            if not article:
                raise ValueError(f"文章不存在: {collect_data.article_id}")

            # 检查是否已经收藏
            existing_query = select(UserArticleCollectModel).where(
                and_(
                    UserArticleCollectModel.user_id == user_id,
                    UserArticleCollectModel.article_id == collect_data.article_id
                )
            )
            existing_result = await session.execute(existing_query)
            existing = existing_result.scalar_one_or_none()

            if existing:
                raise ValueError("您已经收藏过这篇文章了")

            # 收藏数+1
            article.collect_count = article.collect_count + 1

            # 创建收藏记录
            collect_model = UserArticleCollectModel(
                user_id=user_id,
                article_id=collect_data.article_id,
                created_by=user_id
            )

            try:
                session.add(collect_model)
                await session.commit()
                # await session.refresh(collect_model)

                # 构造响应数据
                return UserArticleCollectResponse(
                    id=collect_model.id,
                    user_id=collect_model.user_id,
                    article_id=collect_model.article_id,
                    created_at=collect_model.created_at
                )

            except Exception as e:
                await session.rollback()
                logger.error("添加文章收藏失败", error=str(e), user_id=user_id, article_id=collect_data.article_id)
                raise ValueError(f"添加收藏失败: {str(e)}")

    async def remove_collect(self, user_id: str, article_id: str) -> bool:
        """取消收藏文章

        Args:
            user_id: 用户ID
            article_id: 文章ID

        Returns:
            bool: 是否成功取消收藏

        Raises:
            ValueError: 如果收藏记录不存在
        """
        async with self.async_session() as session:
            # 查找收藏记录
            collect_query = select(UserArticleCollectModel).where(
                and_(
                    UserArticleCollectModel.user_id == user_id,
                    UserArticleCollectModel.article_id == article_id
                )
            )
            collect_result = await session.execute(collect_query)
            collect = collect_result.scalar_one_or_none()

            if not collect:
                raise ValueError("收藏记录不存在")

            # 获取文章并减少收藏数
            article_query = select(ArticleModel).where(ArticleModel.id == article_id)
            article_result = await session.execute(article_query)
            article = article_result.scalar_one_or_none()

            if article and article.collect_count > 0:
                article.collect_count = article.collect_count - 1

            try:
                await session.delete(collect)
                await session.commit()
                return True

            except Exception as e:
                await session.rollback()
                logger.error("取消文章收藏失败", error=str(e), user_id=user_id, article_id=article_id)
                raise ValueError(f"取消收藏失败: {str(e)}")

    async def get_user_collects(
            self,
            user_id: str,
            search: Optional[str] = None,
            page: int = 1,
            page_size: int = 10
    ) -> UserArticleCollectList:
        """获取用户收藏的文章列表

        Args:
            user_id: 用户ID
            search: 搜索关键词（可选）
            page: 页码
            page_size: 每页大小

        Returns:
            UserArticleCollectList: 收藏列表响应
        """
        async with self.async_session() as session:
            # 构建查询
            query = select(UserArticleCollectModel).options(
                selectinload(UserArticleCollectModel.article)
            ).where(UserArticleCollectModel.user_id == user_id)

            # 添加搜索条件
            if search:
                query = query.join(ArticleModel).where(
                    ArticleModel.title.contains(search)
                )

            # 获取总数
            count_query = select(func.count(UserArticleCollectModel.id)).where(
                UserArticleCollectModel.user_id == user_id
            )
            if search:
                count_query = count_query.join(ArticleModel).where(
                    ArticleModel.title.contains(search)
                )

            total_result = await session.execute(count_query)
            total = total_result.scalar()

            # 分页查询
            query = query.order_by(desc(UserArticleCollectModel.created_at))
            query = query.offset((page - 1) * page_size).limit(page_size)

            result = await session.execute(query)
            collects = result.scalars().all()

            # 转换为响应格式
            collect_responses = []
            for collect in collects:
                article_data = None
                if collect.article:
                    article_data = ArticleResponse(
                        id=collect.article.id,
                        title=collect.article.title,
                        content=collect.article.content,
                        summary=collect.article.summary,
                        content_type="article",
                        tags=collect.article.tags or [],
                        related_projects=collect.article.related_projects or [],
                        image_urls=collect.article.image_urls or [],
                        cover_image=collect.article.cover_image,
                        keywords=collect.article.keywords,
                        is_public=collect.article.is_public,
                        sort_order=collect.article.sort_order,
                        is_top=collect.article.is_top,
                        read_count=collect.article.read_count,
                        comment_count=collect.article.comment_count,
                        like_count=collect.article.like_count,
                        collect_count=collect.article.collect_count,
                        shared_count_link=collect.article.shared_count_link,
                        shared_link=collect.article.shared_link,
                        status=collect.article.status,
                        created_at=collect.article.created_at,
                        updated_at=collect.article.updated_at,
                        published_at=collect.article.published_at,
                        created_by=collect.article.created_by
                    )

                collect_responses.append(UserArticleCollectResponse(
                    id=collect.id,
                    user_id=collect.user_id,
                    article_id=collect.article_id,
                    created_at=collect.created_at,
                    article=article_data,
                    content_type="article"
                ))

            return UserArticleCollectList(
                collects=collect_responses,
                total=total,
                page=page,
                page_size=page_size
            )

    async def check_is_collected(self, user_id: str, article_id: str) -> bool:
        """检查用户是否已收藏文章

        Args:
            user_id: 用户ID
            article_id: 文章ID

        Returns:
            bool: 是否已收藏
        """
        async with self.async_session() as session:
            query = select(UserArticleCollectModel).where(
                and_(
                    UserArticleCollectModel.user_id == user_id,
                    UserArticleCollectModel.article_id == article_id
                )
            )
            result = await session.execute(query)
            collect = result.scalar_one_or_none()
            return collect is not None

    async def is_collected_batch(self, user_id: str, article_ids: list[str]) -> dict[str, bool]:
        """批量检查用户是否已收藏指定文章列表

        Args:
            user_id: 用户ID
            article_ids: 文章ID列表

        Returns:
            dict[str, bool]: 文章ID到收藏状态的映射
        """
        async with self.async_session() as session:
            query = select(UserArticleCollectModel).where(
                and_(
                    UserArticleCollectModel.user_id == user_id,
                    UserArticleCollectModel.article_id.in_(article_ids)
                )
            )
            result = await session.execute(query)
            collected_articles = result.scalars().all()

            # 创建一个字典，默认所有文章都未收藏
            collection_status = {article_id: False for article_id in article_ids}
            # 更新已收藏的文章状态
            for collect in collected_articles:
                collection_status[collect.article_id] = True

            return collection_status

    async def simple_search_comments(self, project_id: str, search_query: str, page: int = 1, page_size: int = 10):
        """
        简单的评论搜索

        Args:
            project_id: 项目ID
            search_query: 搜索关键词
            page: 页码
            page_size: 每页大小

        Returns:
            dict: 搜索结果
        """
        try:
            async with self.async_session() as session:
                # 构建简单查询 - 只搜索内容包含关键词的评论
                search_term = f"%{search_query}%"

                # 基础查询
                base_query = select(CommentModel).where(
                    CommentModel.project_id == project_id,
                    CommentModel.content.like(search_term)  # 直接用like，不区分大小写
                )

                # 计算总数
                count_query = select(func.count(CommentModel.id)).where(
                    CommentModel.project_id == project_id,
                    CommentModel.content.like(search_term)
                )
                count_result = await session.execute(count_query)
                total = count_result.scalar()

                # 分页和排序
                offset = (page - 1) * page_size
                query = base_query.order_by(CommentModel.created_at.desc()).offset(offset).limit(page_size)

                # 执行查询
                result = await session.execute(query)
                comments = result.scalars().all()

                # 转换结果
                comment_list = []
                for comment in comments:
                    comment_list.append({
                        'id': comment.id,
                        'content': comment.content,
                        'user_name': comment.user_name,
                        'created_at': comment.created_at.isoformat() if comment.created_at else None,
                        'like_count': comment.like_count,
                        'reply_count': comment.reply_count
                    })

                total_pages = (total + page_size - 1) // page_size if total > 0 else 0

                return {
                    'comments': comment_list,
                    'total': total,
                    'page': page,
                    'page_size': page_size,
                    'total_pages': total_pages,
                    'query': search_query
                }

        except Exception as e:
            logger.error(f"简单搜索评论失败: {str(e)}")
            raise

    async def search_comments(self, search_request: CommentSearchRequest) -> CommentSearchResponse:
        """
        搜索项目评论

        Args:
            search_request: 搜索请求参数

        Returns:
            CommentSearchResponse: 搜索结果
        """
        try:
            async with self.async_session() as session:
                # 构建基础查询
                query = select(CommentModel).where(
                    CommentModel.project_id == search_request.project_id,
                    CommentModel.status == search_request.status,
                    CommentModel.is_public == search_request.is_public
                )

                # 添加文本搜索条件
                # if search_request.query:
                #     search_term = f"%{search_request.query}%"
                #     query = query.where(CommentModel.content.ilike(search_term))

                if search_request.query:
                    search_term = f"%{search_request.query}%"
                    # Check if query contains non-ASCII characters (like Chinese)
                    if any(ord(char) > 127 for char in search_request.query):
                        # Use regular LIKE for Chinese characters
                        query = query.where(CommentModel.content.like(search_term))
                    else:
                        # Use case-insensitive LIKE for ASCII characters
                        query = query.where(CommentModel.content.ilike(search_term))

                # 添加用户名搜索条件
                if search_request.user_name:
                    user_term = f"%{search_request.user_name}%"
                    query = query.where(CommentModel.user_name.ilike(user_term))

                # 添加父评论过滤
                if search_request.parent_id is not None:
                    if search_request.parent_id == "null":
                        query = query.where(CommentModel.parent_id.is_(None))
                    else:
                        query = query.where(CommentModel.parent_id == search_request.parent_id)

                # 添加日期范围过滤
                if search_request.date_from:
                    query = query.where(CommentModel.created_at >= search_request.date_from)
                if search_request.date_to:
                    query = query.where(CommentModel.created_at <= search_request.date_to)

                # 计算总数
                count_query = select(func.count(CommentModel.id)).select_from(query.alias())
                count_result = await session.execute(count_query)
                total = count_result.scalar()

                # 添加排序
                if search_request.sort_by == "like_count":
                    if search_request.sort_order == "asc":
                        query = query.order_by(CommentModel.like_count.asc())
                    else:
                        query = query.order_by(CommentModel.like_count.desc())
                elif search_request.sort_by == "reply_count":
                    if search_request.sort_order == "asc":
                        query = query.order_by(CommentModel.reply_count.asc())
                    else:
                        query = query.order_by(CommentModel.reply_count.desc())
                elif search_request.sort_by == "user_name":
                    if search_request.sort_order == "asc":
                        query = query.order_by(CommentModel.user_name.asc())
                    else:
                        query = query.order_by(CommentModel.user_name.desc())
                else:  # 默认按创建时间排序
                    if search_request.sort_order == "asc":
                        query = query.order_by(CommentModel.created_at.asc())
                    else:
                        query = query.order_by(CommentModel.created_at.desc())

                # 分页
                offset = (search_request.page - 1) * search_request.page_size
                query = query.offset(offset).limit(search_request.page_size)

                # 执行查询
                result = await session.execute(query)
                comments = result.scalars().all()

                # 转换为响应模型
                comment_responses = []
                for comment in comments:
                    comment_response = await self._to_comment_response(comment)
                    comment_responses.append(comment_response)

                # 计算总页数
                total_pages = (total + search_request.page_size - 1) // search_request.page_size

                return CommentSearchResponse(
                    comments=comment_responses,
                    total=total,
                    page=search_request.page,
                    page_size=search_request.page_size,
                    total_pages=total_pages,
                    query=search_request.query
                )

        except Exception as e:
            logger.error("搜索评论失败", error=str(e), exc_info=True)
            raise

    async def create_article(self, article_data: ArticleCreate, user_id: str) -> ArticleResponse:
        """创建文章"""
        try:
            async with self.async_session() as session:
                # 创建文章对象
                article = ArticleModel(
                    **article_data.dict(),
                    created_by=user_id,
                    updated_by=user_id
                )

                # 保存到数据库
                session.add(article)
                await session.commit()
                await session.refresh(article)

                # 转换为响应格式
                return ArticleResponse(
                    id=article.id,
                    title=article.title,
                    content=article.content,
                    summary=article.summary,
                    tags=article.tags or [],
                    related_projects=article.related_projects or [],
                    image_urls=article.image_urls or [],
                    cover_image=article.cover_image,
                    keywords=article.keywords,
                    is_public=article.is_public,
                    sort_order=article.sort_order,
                    is_top=article.is_top,
                    read_count=article.read_count,
                    comment_count=article.comment_count,
                    like_count=article.like_count,
                    collect_count=article.collect_count,
                    shared_count_link=article.shared_count_link,
                    shared_link=article.shared_link,
                    status=article.status,
                    created_at=article.created_at,
                    updated_at=article.updated_at,
                    published_at=article.published_at,
                    created_by=article.created_by
                )
        except Exception as e:
            logger.error("创建文章失败", error=str(e), exc_info=True)
            raise

    async def get_article_by_id(self, article_id: str, user_id: str) -> Optional[ArticleResponse]:
        """根据ID获取文章"""
        try:
            async with self.async_session() as session:
                query = select(ArticleModel).where(ArticleModel.id == article_id)
                result = await session.execute(query)
                article = result.scalar_one_or_none()

                if not article:
                    return None


                collection_status = await self.is_collected_batch(user_id, [article_id])
                is_collected = collection_status[article_id]
                return ArticleResponse(
                    id=article.id,
                    title=article.title,
                    content=article.content,
                    summary=article.summary,
                    tags=article.tags or [],
                    related_projects=article.related_projects or [],
                    image_urls=article.image_urls or [],
                    cover_image=article.cover_image,
                    keywords=article.keywords,
                    is_public=article.is_public,
                    sort_order=article.sort_order,
                    is_top=article.is_top,
                    read_count=article.read_count,
                    comment_count=article.comment_count,
                    like_count=article.like_count,
                    collect_count=article.collect_count,
                    shared_count_link=article.shared_count_link,
                    shared_link=article.shared_link,
                    status=article.status,
                    created_at=article.created_at,
                    updated_at=article.updated_at,
                    published_at=article.published_at,
                    created_by=article.created_by,
                    is_collected=is_collected
                )
        except Exception as e:
            logger.error("获取文章失败", error=str(e), exc_info=True)
            raise

    async def update_article(self, article_id: str, article_data: ArticleUpdate, user_id: str) -> Optional[
        ArticleResponse]:
        """更新文章"""
        try:
            async with self.async_session() as session:
                query = select(ArticleModel).where(ArticleModel.id == article_id)
                result = await session.execute(query)
                article = result.scalar_one_or_none()

                if not article:
                    return None

                # 更新字段
                update_data = article_data.dict(exclude_unset=True)
                update_data['updated_by'] = user_id

                for field, value in update_data.items():
                    setattr(article, field, value)

                # 如果状态改为已发布，设置发布时间
                if update_data.get('status') == 'published' and not article.published_at:
                    article.published_at = datetime.now(timezone.utc)

                await session.commit()
                await session.refresh(article)

                return await self.get_article_by_id(article_id,user_id)
        except Exception as e:
            logger.error("更新文章失败", error=str(e), exc_info=True)
            raise

    async def delete_article(self, article_id: str) -> bool:
        """删除文章"""
        try:
            async with self.async_session() as session:
                query = select(ArticleModel).where(ArticleModel.id == article_id)
                result = await session.execute(query)
                article = result.scalar_one_or_none()

                if not article:
                    return False

                await session.delete(article)
                await session.commit()

                return True
        except Exception as e:
            logger.error("删除文章失败", error=str(e), exc_info=True)
            raise

    async def get_articles_new(
            self,
            page: int = 1,
            size: int = 10,
            status: Optional[str] = None,
            tags: Optional[List[str]] = None,
            search: Optional[str] = None,
            is_public: Optional[bool] = None,
            user_id: Optional[str] = None,
            need_recommend: Optional[bool] = True,
            recommend_tags: Optional[List] = None
    ) -> ArticleListResponse:
        """获取文章列表，支持基于标签的推荐系统"""
        try:
            async with self.async_session() as session:
                # 推荐模式
                    return await self._get_articles_normal(
                        session, page, size, status, tags, search,
                        is_public, user_id
                    )
        except Exception as e:
            logger.error("获取文章列表失败", error=str(e), exc_info=True)
            raise

    async def _get_articles_with_recommendation(
            self, session, page: int, size: int, status: Optional[str],
            tags: Optional[List[str]], search: Optional[str],
            is_public: Optional[bool], user_id: str, recommend_tags: Optional[List]
    ) -> ArticleListResponse:
        """推荐模式获取文章，包含补全逻辑"""

        # 获取推荐标签
        tags_to_recommend = recommend_tags
        last_article = None

        if not tags_to_recommend:
            # 获取用户最后一次访问的文章
            last_article = await self.get_last_accessed_article(user_id)
            if last_article and last_article.tags:
                tags_to_recommend = last_article.tags
                logger.info(f"用户 {user_id} 基于最后访问文章的标签进行推荐: {tags_to_recommend}")

        # 第一步：获取推荐文章
        recommended_articles = []
        recommended_article_ids = set()

        if tags_to_recommend:
            recommended_articles = await self._get_articles_by_tags(
                session, tags_to_recommend, last_article, size, page
            )
            recommended_article_ids = {a.id for a in recommended_articles}
            logger.info(f"推荐逻辑返回 {len(recommended_articles)} 篇文章")

        # 第二步：如果推荐文章不足一页，用普通逻辑补全
        final_articles = recommended_articles

        if len(recommended_articles) < size:
            needed_count = size - len(recommended_articles)
            logger.info(f"推荐文章不足，需要补充 {needed_count} 篇文章")

            # 获取普通逻辑的文章，排除已推荐的文章
            supplement_articles = await self._get_articles_normal_exclude_ids(
                session, status, tags, search, is_public,
                recommended_article_ids, needed_count
            )

            final_articles.extend(supplement_articles)
            logger.info(f"补充了 {len(supplement_articles)} 篇文章")

        # 分页处理
        paginated_articles = final_articles

        # 批量检查收藏状态
        if user_id and paginated_articles:
            article_ids = [article.id for article in paginated_articles]
            collection_status = await self.is_collected_batch(user_id, article_ids)

            for article in paginated_articles:
                article.is_collected = collection_status[article.id]

        # 获取总数（推荐模式下的总数基于实际获取的文章数）
        total = len(final_articles)

        logger.info(f"推荐模式获取文章成功，返回 {len(paginated_articles)} 篇文章，总数: {total}")

        # 转换为响应格式
        article_responses = []
        for article in paginated_articles:
            article_responses.append(ArticleResponse(
                id=article.id,
                title=article.title,
                content=article.content,
                summary=article.summary,
                tags=article.tags or [],
                related_projects=[str(proj) for proj in (article.related_projects or [])],
                image_urls=article.image_urls or [],
                cover_image=article.cover_image,
                keywords=article.keywords,
                is_public=article.is_public,
                sort_order=article.sort_order,
                is_top=article.is_top,
                read_count=article.read_count,
                comment_count=article.comment_count,
                like_count=article.like_count,
                collect_count=article.collect_count,
                shared_count_link=article.shared_count_link,
                shared_link=article.shared_link,
                status=article.status,
                created_at=article.created_at,
                updated_at=article.updated_at,
                published_at=article.published_at,
                created_by=article.created_by,
                is_collected=getattr(article, 'is_collected', False)
            ))

        return ArticleListResponse(
            articles=article_responses,
            total=total,
            # total= 20,
            page=page,
            size=size
        )

    async def get_last_accessed_article(self, user_id: str) -> Optional[ArticleModel]:
        """获取用户最后一次访问的文章

        Args:
            user_id: 用户ID

        Returns:
            Optional[ArticleModel]: 最后访问的文章信息，不存在时返回None
        """
        try:
            async with self.async_session() as session:
                # 查询用户的文章访问日志，筛选文章历史记录类型，按创建时间倒序排列
                stmt = select(UserFlowLogModel.project_id).where(
                    and_(
                        UserFlowLogModel.created_by == user_id,
                        UserFlowLogModel.log_type == FlowLogType.ARTICLE_HISTORY
                    )
                ).order_by(UserFlowLogModel.created_at.desc()).limit(1)

                result = await session.execute(stmt)
                last_accessed_article_id = result.scalar_one_or_none()

                if not last_accessed_article_id:
                    return None

                # 获取对应文章信息
                article_stmt = select(ArticleModel).where(
                    ArticleModel.id == last_accessed_article_id
                )
                article_result = await session.execute(article_stmt)
                db_article = article_result.scalar_one_or_none()

                if db_article:
                    return db_article
                return None

        except Exception as e:
            logger.error(f"获取用户最后访问的文章时发生错误: {str(e)}")
            return None

    async def _get_articles_by_tags(
            self, session, tags_to_recommend: List, last_article, limit: int, page: int = 1
    ) -> List[ArticleModel]:
        """根据标签获取推荐文章"""

        # 构建基础查询
        stmt = select(ArticleModel)
        tag_sql_conditions = []

        for tag in tags_to_recommend:
            # 转义单引号，防止 SQL 注入
            # escaped_tag = tag.replace("'", "''")
            #
            # # 将中文转换为 Unicode 编码形式，并进行正确的转义
            # unicode_tag = tag.encode('unicode_escape').decode('ascii')
            # escaped_unicode_tag = unicode_tag.replace('\\', '\\\\').replace("'", "''")

            # 同时搜索原始中文和 Unicode 编码
            tag_condition = text(f"""(
                tags::text ILIKE '%"{tag}"%' 
            )""")
            tag_sql_conditions.append(tag_condition)
            # logger.info(f"搜索文章标签: 原始='{escaped_tag}', Unicode='{escaped_unicode_tag}'")

        conditions = []
        if tag_sql_conditions:
            # 将所有标签条件用 OR 连接
            combined_tag_condition = or_(*tag_sql_conditions)
            conditions.append(combined_tag_condition)

        # 如果有推荐标签，排除用户最后访问的文章本身
        if last_article:
            conditions.append(ArticleModel.id != last_article.id)

        # 只显示已发布的文章
        conditions.append(ArticleModel.status == 'published')
        conditions.append(ArticleModel.is_public == True)

        # 应用查询条件
        if conditions:
            stmt = stmt.where(and_(*conditions))

        # 计算分页偏移量
        skip = (page - 1) * limit
        stmt = stmt.offset(skip).limit(limit)

        # 推荐模式排序：优先按阅读量排序，然后按点赞数、创建时间
        stmt = stmt.order_by(
            desc(ArticleModel.read_count),
            desc(ArticleModel.like_count),
            desc(ArticleModel.created_at),
            desc(ArticleModel.updated_at)
        ).limit(limit)

        result = await session.execute(stmt)
        db_articles = result.scalars().all()

        return list(db_articles)

    async def _get_articles_normal_exclude_ids(
            self, session, status: Optional[str], tags: Optional[List[str]],
            search: Optional[str], is_public: Optional[bool],
            exclude_ids: set, limit: int
    ) -> List[ArticleModel]:
        """获取普通逻辑的文章，排除指定的文章ID"""

        # 构建基础查询
        stmt = select(ArticleModel)
        conditions = []

        # 普通筛选条件
        if status:
            conditions.append(ArticleModel.status == status)

        # 标签过滤 - 使用与推荐系统相同的 LIKE 匹配逻辑
        if tags:
            tag_conditions = []
            for tag in tags:
                # 转义单引号，防止 SQL 注入
                # escaped_tag = tag.replace("'", "''")
                #
                # # 将中文转换为 Unicode 编码形式，并进行正确的转义
                # unicode_tag = tag.encode('unicode_escape').decode('ascii')
                # escaped_unicode_tag = unicode_tag.replace('\\', '\\\\').replace("'", "''")

                # 同时搜索原始中文和 Unicode 编码
                tag_condition = text(f"""(
                    tags::text ILIKE '%"{tag}"%' 
                  
                )""")
                tag_conditions.append(tag_condition)

            if tag_conditions:
                # 所有标签条件用 OR 连接
                combined_tag_condition = or_(*tag_conditions)
                conditions.append(combined_tag_condition)

        if search:
            conditions.append(
                or_(
                    ArticleModel.title.contains(search),
                    ArticleModel.content.contains(search),
                    ArticleModel.summary.contains(search)
                )
            )

        if is_public is not None:
            conditions.append(ArticleModel.is_public == is_public)

        # 排除已推荐的文章
        if exclude_ids:
            conditions.append(~ArticleModel.id.in_(exclude_ids))

        # 应用查询条件
        if conditions:
            stmt = stmt.where(and_(*conditions))

        # 默认排序
        stmt = stmt.order_by(
            desc(ArticleModel.is_top),
            desc(ArticleModel.sort_order),
            desc(ArticleModel.created_at)
        ).limit(limit)

        result = await session.execute(stmt)
        db_articles = result.scalars().all()

        return list(db_articles)

    async def _get_articles_normal(
            self, session, page: int, size: int, status: Optional[str],
            tags: Optional[List[str]], search: Optional[str],
            is_public: Optional[bool], user_id: Optional[str]
    ) -> ArticleListResponse:
        """非推荐模式的原始逻辑"""

        # 构建基础查询
        query = select(ArticleModel)

        # 应用过滤条件
        conditions = []

        # 状态过滤
        if status:
            conditions.append(ArticleModel.status == status)
        # 搜索过滤
        if search:
            conditions.append(
                or_(
                    ArticleModel.title.contains(search),
                    ArticleModel.content.contains(search),
                    ArticleModel.summary.contains(search)
                )
            )

        # 公开性过滤
        if is_public is not None:
            conditions.append(ArticleModel.is_public == is_public)

        # 应用所有条件
        if conditions:
            query = query.where(and_(*conditions))

        # 计算总数 - 使用相同的条件
        count_query = select(func.count(ArticleModel.id))
        if conditions:
            count_query = count_query.where(and_(*conditions))

        count_result = await session.execute(count_query)
        total = count_result.scalar()

        # 排序：置顶文章在前，然后按排序权重和创建时间排序
        query = query.order_by(
            desc(ArticleModel.is_top),
            desc(ArticleModel.sort_order),
            desc(ArticleModel.created_at)
        )

        # 分页
        query = query.offset((page - 1) * size).limit(size)
        result = await session.execute(query)
        articles = result.scalars().all()
        article_responses = []
        for article in articles:
            article_responses.append(ArticleResponse(
                id=article.id,
                title=article.title,
                content=article.content,
                summary=article.summary,
                tags=article.tags or [],
                # related_projects=article.related_projects or [],
                related_projects=[str(proj) for proj in (article.related_projects or [])],
                image_urls=article.image_urls or [],
                cover_image=article.cover_image,
                keywords=article.keywords,
                is_public=article.is_public,
                sort_order=article.sort_order,
                is_top=article.is_top,
                read_count=article.read_count,
                comment_count=article.comment_count,
                like_count=article.like_count,
                collect_count=article.collect_count,
                shared_count_link=article.shared_count_link,
                shared_link=article.shared_link,
                status=article.status,
                created_at=article.created_at,
                updated_at=article.updated_at,
                published_at=article.published_at,
                created_by=article.created_by,
                is_collected=False  # 默认值，如果有用户ID会更新
            ))

        # 如果提供了 user_id，批量检查文章的收藏状态
        if user_id and article_responses:
            article_ids = [article.id for article in article_responses]
            collection_status = await self.is_collected_batch(user_id, article_ids)

            # 更新每个文章的收藏状态
            for article_response in article_responses:
                article_response.is_collected = collection_status[article_response.id]

        return ArticleListResponse(
            articles=article_responses,
            total=total,
            page=page,
            size=size
        )


    # async def get_articles(
    #         self,
    #         page: int = 1,
    #         size: int = 10,
    #         status: Optional[str] = None,
    #         tags: Optional[List[str]] = None,
    #         search: Optional[str] = None,
    #         is_public: Optional[bool] = None,
    #         user_id: Optional[str] = None
    # ) -> ArticleListResponse:
    #     """获取文章列表"""
    #     try:
    #         async with self.async_session() as session:
    #             # 构建基础查询
    #             query = select(ArticleModel)
    #
    #             # 应用过滤条件
    #             conditions = []
    #
    #             # 状态过滤
    #             if status:
    #                 conditions.append(ArticleModel.status == status)
    #
    #             # 标签过滤
    #             if tags:
    #                 for tag in tags:
    #                     conditions.append(ArticleModel.tags.contains([tag]))
    #
    #             # 搜索过滤
    #             if search:
    #                 conditions.append(
    #                     or_(
    #                         ArticleModel.title.contains(search),
    #                         ArticleModel.content.contains(search),
    #                         ArticleModel.summary.contains(search)
    #                     )
    #                 )
    #
    #             # 公开性过滤
    #             if is_public is not None:
    #                 conditions.append(ArticleModel.is_public == is_public)
    #
    #             # 用户过滤
    #             if user_id:
    #                 conditions.append(ArticleModel.created_by == user_id)
    #
    #             # 应用所有条件
    #             if conditions:
    #                 query = query.where(and_(*conditions))
    #
    #             # 计算总数 - 使用相同的条件
    #             count_query = select(func.count(ArticleModel.id))
    #             if conditions:
    #                 count_query = count_query.where(and_(*conditions))
    #
    #             count_result = await session.execute(count_query)
    #             total = count_result.scalar()
    #
    #             # 排序：置顶文章在前，然后按排序权重和创建时间排序
    #             query = query.order_by(
    #                 desc(ArticleModel.is_top),
    #                 desc(ArticleModel.sort_order),
    #                 desc(ArticleModel.created_at)
    #             )
    #
    #             # 分页
    #             query = query.offset((page - 1) * size).limit(size)
    #             result = await session.execute(query)
    #             articles = result.scalars().all()
    #
    #             # 转换为响应格式
    #             article_responses = []
    #             for article in articles:
    #                 article_responses.append(ArticleResponse(
    #                     id=article.id,
    #                     title=article.title,
    #                     content=article.content,
    #                     summary=article.summary,
    #                     tags=article.tags or [],
    #                     # related_projects=article.related_projects or [],
    #                     related_projects=[str(proj) for proj in (article.related_projects or [])],
    #                     image_urls=article.image_urls or [],
    #                     cover_image=article.cover_image,
    #                     keywords=article.keywords,
    #                     is_public=article.is_public,
    #                     sort_order=article.sort_order,
    #                     is_top=article.is_top,
    #                     read_count=article.read_count,
    #                     comment_count=article.comment_count,
    #                     like_count=article.like_count,
    #                     collect_count=article.collect_count,
    #                     shared_count_link=article.shared_count_link,
    #                     shared_link=article.shared_link,
    #                     status=article.status,
    #                     created_at=article.created_at,
    #                     updated_at=article.updated_at,
    #                     published_at=article.published_at,
    #                     created_by=article.created_by,
    #                     is_collected=False  # 默认值，如果有用户ID会更新
    #                 ))
    #
    #             # 如果提供了 user_id，批量检查文章的收藏状态
    #             if user_id and article_responses:
    #                 article_ids = [article.id for article in article_responses]
    #                 collection_status = await self.is_collected_batch(user_id, article_ids)
    #
    #                 # 更新每个文章的收藏状态
    #                 for article_response in article_responses:
    #                     article_response.is_collected = collection_status[article_response.id]
    #
    #             return ArticleListResponse(
    #                 articles=article_responses,
    #                 total=total,
    #                 page=page,
    #                 size=size
    #             )
    #     except Exception as e:
    #         logger.error("获取文章列表失败", error=str(e), exc_info=True)
    #         raise

    async def increment_read_count_and_record_history(self, article_id: str, user_id: str) -> bool:
        """增加文章阅读量"""
        try:
            async with self.async_session() as session:
                query = select(ArticleModel).where(ArticleModel.id == article_id)
                result = await session.execute(query)
                article = result.scalar_one_or_none()
                if not article:
                    return False
                article.read_count += 1

                await record_user_flow_log(
                    session=session,
                    log_type=FlowLogType.ARTICLE_HISTORY,
                    project_id=article_id,
                    project_name=article.title,
                    # content=f"{project.repository_url}",
                    created_by=user_id
                )

                await session.commit()

                return True
        except Exception as e:
            logger.error("增加阅读量失败", error=str(e), exc_info=True)
            raise

    async def like_article(self, article_id: str, user_id: str, action: str) -> bool:
        """点赞/取消点赞文章"""
        try:
            async with self.async_session() as session:
                query = select(ArticleModel).where(ArticleModel.id == article_id)
                result = await session.execute(query)
                article = result.scalar_one_or_none()

                if not article:
                    return False

                if action == 'like':
                    article.like_count += 1
                elif action == 'unlike':
                    article.like_count = max(0, article.like_count - 1)
                elif action == 'collect':
                    article.collect_count += 1
                elif action == 'uncollect':
                    article.collect_count = max(0, article.like_count - 1)
                await session.commit()

                return True
        except Exception as e:
            logger.error("点赞操作失败", error=str(e), exc_info=True)
            raise

    async def share_article(self, article_id: str, share_type: str) -> Optional[str]:
        """分享文章"""
        try:
            async with self.async_session() as session:
                query = select(ArticleModel).where(ArticleModel.id == article_id)
                result = await session.execute(query)
                article = result.scalar_one_or_none()

                if not article:
                    return None

                # 增加分享次数
                article.shared_count_link += 1

                # 生成分享链接
                if not article.shared_link:
                    article.shared_link = f"/articles/{article_id}"

                await session.commit()

                return article.shared_link
        except Exception as e:
            logger.error("分享文章失败", error=str(e), exc_info=True)
            raise

    async def get_article_comments(
            self,
            article_id: str,
            page: int = 1,
            size: int = 20,
            depth: int = 3,
            comment_id: Optional[str] = None,
            search: Optional[str] = None,
    ) -> CommentTreeResponse:
        """获取一个项目的评论树

        Args:
            article_id: 项目ID
            page: 页码
            size: 每页大小
            depth: 评论树深度
            comment_id: 指定的评论ID，如果提供则获取该评论的子树
            search: 搜索关键词，如果提供则搜索包含关键词的评论并返回其子树
        """
        try:
            async with self.async_session() as session:
                if comment_id:
                    # 如果指定了评论ID，获取该评论及其子回复
                    query = select(CommentModel).where(
                        CommentModel.id == comment_id,
                        CommentModel.status == "approved",
                        CommentModel.is_public == True
                    )

                    result = await session.execute(query)
                    target_comment = result.scalar_one_or_none()

                    if not target_comment:
                        return CommentTreeResponse(
                            article_id=article_id,
                            comments=[],
                            total=0,
                            page=page,
                            size=size,
                            depth=depth,
                            comment_id=comment_id
                        )

                    # 转换目标评论并获取其子回复
                    comment_response = await self._to_comment_response(target_comment)
                    comment_response.replies = await self._get_comment_replies(session, target_comment.id, depth - 1)

                    return CommentTreeResponse(
                        article_id=article_id,
                        comments=[comment_response],
                        total=1,
                        page=1,
                        size=1,
                        depth=depth,
                        comment_id=comment_id
                    )
                elif search:
                    # 如果提供了搜索关键词，搜索包含关键词的评论并返回其子树
                    search_term = f"%{search}%"

                    # 基础查询：搜索包含关键词的评论
                    base_query = select(CommentModel).where(
                        CommentModel.project_id == article_id,
                        CommentModel.content.like(search_term),
                        CommentModel.status == "approved",
                        CommentModel.is_public == True
                    ).order_by(desc(CommentModel.created_at))

                    # 计算总数
                    count_query = select(func.count(CommentModel.id)).where(
                        CommentModel.project_id == article_id,
                        CommentModel.content.like(search_term),
                        CommentModel.status == "approved",
                        CommentModel.is_public == True
                    )
                    count_result = await session.execute(count_query)
                    total = count_result.scalar()

                    # 分页
                    query = base_query.offset((page - 1) * size).limit(size)
                    result = await session.execute(query)
                    search_comments = result.scalars().all()

                    # 构建评论子树
                    comment_responses = []
                    for comment in search_comments:
                        comment_response = await self._to_comment_response(comment)
                        # 获取该评论的回复，使用指定的深度
                        comment_response.replies = await self._get_comment_replies(session, comment.id, depth - 1)
                        comment_responses.append(comment_response)

                    return CommentTreeResponse(
                        article_id=article_id,
                        comments=comment_responses,
                        total=total,
                        page=page,
                        size=size,
                        depth=depth,
                        comment_id=comment_id
                    )
                else:
                    # 原有逻辑：查询根评论（没有parent_id的评论）
                    query = select(CommentModel).where(
                        CommentModel.project_id == article_id,
                        CommentModel.parent_id.is_(None),
                        CommentModel.status == "approved",
                        CommentModel.is_public == True
                    ).order_by(desc(CommentModel.created_at))

                    # 计算总数
                    count_query = select(func.count(CommentModel.id)).where(
                        CommentModel.project_id == article_id,
                        CommentModel.parent_id.is_(None),
                        CommentModel.status == "approved",
                        CommentModel.is_public == True
                    )
                    count_result = await session.execute(count_query)
                    total = count_result.scalar()

                    # 分页
                    query = query.offset((page - 1) * size).limit(size)
                    result = await session.execute(query)
                    root_comments = result.scalars().all()

                    # 构建评论树
                    comment_responses = []
                    for root_comment in root_comments:
                        comment_response = await self._to_comment_response(root_comment)
                        # 获取回复，使用指定的深度
                        comment_response.replies = await self._get_comment_replies(session, root_comment.id, depth - 1)
                        comment_responses.append(comment_response)

                    return CommentTreeResponse(
                        article_id=article_id,
                        comments=comment_responses,
                        total=total,
                        page=page,
                        size=size,
                        depth=depth,
                        comment_id=comment_id
                    )

        except Exception as e:
            logger.error("获取文章评论树失败", error=str(e), exc_info=True)
            raise

    # async def get_article_comments(
    #     self,
    #     article_id: str,
    #     page: int = 1,
    #     size: int = 20,
    #     depth: int = 3,
    #     comment_id: Optional[str] = None,
    #     search: Optional[str] = None
    # ) -> CommentTreeResponse:
    #     """获取一个项目的评论树
    #
    #     Args:
    #         article_id: 项目ID
    #         page: 页码
    #         size: 每页大小
    #         depth: 评论树深度
    #         comment_id: 指定的评论ID，如果提供则获取该评论的子树
    #     """
    #     try:
    #         async with self.async_session() as session:
    #             if comment_id:
    #                 # 如果指定了评论ID，获取该评论及其子回复
    #                 query = select(CommentModel).where(
    #                     CommentModel.id == comment_id,
    #                     CommentModel.status == "approved",
    #                     CommentModel.is_public == True
    #                 )
    #
    #                 result = await session.execute(query)
    #                 target_comment = result.scalar_one_or_none()
    #
    #                 if not target_comment:
    #                     return CommentTreeResponse(
    #                         article_id=article_id,
    #                         comments=[],
    #                         total=0,
    #                         page=page,
    #                         size=size,
    #                         depth=depth,
    #                         comment_id=comment_id
    #                     )
    #
    #                 # 转换目标评论并获取其子回复
    #                 comment_response = await self._to_comment_response(target_comment)
    #                 comment_response.replies = await self._get_comment_replies(session, target_comment.id, depth - 1)
    #
    #                 return CommentTreeResponse(
    #                     article_id=article_id,
    #                     comments=[comment_response],
    #                     total=1,
    #                     page=1,
    #                     size=1,
    #                     depth=depth,
    #                     comment_id=comment_id
    #                 )
    #             else:
    #                 # 原有逻辑：查询根评论（没有parent_id的评论）
    #                 query = select(CommentModel).where(
    #                     CommentModel.project_id == article_id,
    #                     CommentModel.parent_id.is_(None),
    #                     CommentModel.status == "approved",
    #                     CommentModel.is_public == True
    #                 ).order_by(desc(CommentModel.created_at))
    #
    #                 # 计算总数
    #                 count_query = select(func.count(CommentModel.id)).where(
    #                     CommentModel.project_id == article_id,
    #                     CommentModel.parent_id.is_(None),
    #                     CommentModel.status == "approved",
    #                     CommentModel.is_public == True
    #                 )
    #                 count_result = await session.execute(count_query)
    #                 total = count_result.scalar()
    #
    #                 # 分页
    #                 query = query.offset((page - 1) * size).limit(size)
    #                 result = await session.execute(query)
    #                 root_comments = result.scalars().all()
    #
    #                 # 构建评论树
    #                 comment_responses = []
    #                 for root_comment in root_comments:
    #                     comment_response = await self._to_comment_response(root_comment)
    #                     # 获取回复，使用指定的深度
    #                     comment_response.replies = await self._get_comment_replies(session, root_comment.id, depth - 1)
    #                     comment_responses.append(comment_response)
    #
    #                 return CommentTreeResponse(
    #                     article_id=article_id,
    #                     comments=comment_responses,
    #                     total=total,
    #                     page=page,
    #                     size=size,
    #                     depth=depth,
    #                     comment_id=comment_id
    #                 )
    #
    #     except Exception as e:
    #         logger.error("获取文章评论树失败", error=str(e), exc_info=True)
    #         raise

    async def create_article_comment(self, article_id: str, comment_data: CommentCreate, user_id: str,
                                     user_name: str = None, user_avatar: str = None) -> CommentResponse:
        """对一个项目进行评论"""
        try:
            async with self.async_session() as session:
                # AI审核
                is_approved = AIAuditUtil.check(comment_data.content)

                # 确定评论状态
                status = "approved" if is_approved else "pending"

                # 确定root_id和父评论用户信息
                root_id = None
                parent_user_id = None
                parent_user_name = None
                parent_id = comment_data.parent_id if comment_data.parent_id and comment_data.parent_id.strip() else None

                if parent_id:
                    # 查询父评论的信息
                    parent_query = select(CommentModel).where(CommentModel.id == comment_data.parent_id)
                    parent_result = await session.execute(parent_query)
                    parent_comment = parent_result.scalar_one_or_none()

                    if parent_comment:
                        root_id = parent_comment.root_id or parent_comment.id
                        # 设置父评论的用户信息
                        parent_user_id = parent_comment.user_id
                        parent_user_name = parent_comment.user_name
                    else:
                        # 父评论不存在，作为根评论
                        root_id = None

                # 创建评论对象
                comment = CommentModel(
                    content=comment_data.content,
                    project_id=article_id,
                    parent_id=comment_data.parent_id,
                    root_id=root_id,
                    user_id=user_id,
                    user_name=user_name,
                    user_avatar=user_avatar,
                    # 新增的父评论用户信息
                    parent_user_id=parent_user_id,
                    parent_user_name=parent_user_name,
                    status=status,
                    metadata=comment_data.metadata or {},
                    created_by=user_id,
                    updated_by=user_id
                )
                record_system_message(session,
                                      "您有一条新回复",
                                      message_type=FlowLogType.PROJECT_GENERATE,
                                      created_by=parent_id,
                                      project_id=article_id
                                      )
                # 保存到数据库
                session.add(comment)
                await session.commit()
                await session.refresh(comment)

                # 如果没有父评论，root_id就是评论自身的id
                if not comment_data.parent_id:
                    comment.root_id = comment.id
                    await session.commit()
                    await session.refresh(comment)

                # 更新父评论的回复数
                if comment_data.parent_id:
                    await self._update_comment_reply_count(session, comment_data.parent_id, 1)

                # 更新文章的评论数
                await self._update_article_comment_count(session, article_id, 1)

                # 转换为响应格式
                # 转换为响应格式
                comment_response = await self._to_comment_response(comment)

                # 🆕 异步检测@gugu并触发AI回复（不等待完成）
                import asyncio
                asyncio.create_task(self._handle_gugu_mention(comment, article_id))

                return comment_response
        except Exception as e:
            logger.error("创建文章评论失败", error=str(e), exc_info=True)
            raise

    async def _handle_gugu_mention(self, comment: CommentModel, article_id: str) -> None:
        """
        处理@gugu提及，触发AI智能回复

        Args:
            comment: 新创建的评论
            article_id: 文章ID
        """
        try:
            # 导入智能评论处理器
            from app.services.reasoning.intelligent_comment.comment_processor import SmartCommentProcessor

            # 检测是否包含@gugu
            processor = SmartCommentProcessor()
            if not processor.detect_gugu_mention(comment.content):
                return

            logger.info(f"检测到@咕咕答提及，准备生成AI回复，评论ID: {comment.id}")

            # 准备数据
            comment_data = {
                "id": comment.id,
                "content": comment.content,
                "user_id": comment.user_id,
                "user_name": comment.user_name,
                "created_at": comment.created_at.isoformat() if comment.created_at else None
            }

            # 获取文章数据
            article_data = await self._get_article_data_for_ai(article_id)
            if not article_data:
                logger.warning(f"无法获取文章数据，跳过AI回复，文章ID: {article_id}")
                return

            # 获取评论树（上下文）
            comment_tree = await self._get_comment_tree_for_ai(article_id)

            # 调用AI处理
            ai_response = await processor.process_gugu_comment(
                comment_data=comment_data,
                article_data=article_data,
                comment_tree=comment_tree
            )

            if ai_response and ai_response.generated_reply:
                # 获取gugu机器人用户
                gugu_user = await self._get_gugu_bot_user()
                if not gugu_user:
                    logger.error("未找到gugu机器人用户，无法发布AI回复")
                    return

                # 创建AI回复评论
                await self._create_gugu_reply(
                    article_id=article_id,
                    parent_comment_id=comment.id,
                    root_comment_id = comment.root_id,
                    ai_content=ai_response.generated_reply.content,
                    gugu_user=gugu_user
                )

                logger.info(f"AI回复创建成功，原评论ID: {comment.id}")

        except Exception as e:
            logger.error(f"处理@gugu提及失败: {str(e)}", exc_info=True)
            # 不抛出异常，避免影响原始评论创建

    async def _get_article_data_for_ai(self, article_id: str) -> Optional[Dict[str, Any]]:
        """获取文章数据供AI使用"""
        try:
            async with self.async_session() as session:
                # 查询文章
                query = select(ArticleModel).where(ArticleModel.id == article_id)
                result = await session.execute(query)
                article = result.scalar_one_or_none()

                if not article:
                    return None

                return {
                    "id": article.id,
                    "title": article.title,
                    "content": article.content,
                    "summary": article.summary,
                    "tags": article.tags or []
                }
        except Exception as e:
            logger.error(f"获取文章数据失败: {str(e)}")
            return None

    async def _get_comment_tree_for_ai(self, article_id: str) -> List[Dict[str, Any]]:
        """获取评论树数据供AI使用"""
        try:
            async with self.async_session() as session:
                # 查询文章的所有评论（限制数量）
                query = (select(CommentModel)
                         .where(CommentModel.project_id == article_id)
                         .where(CommentModel.status == "approved")
                         .order_by(CommentModel.created_at.desc())
                         .limit(20))  # 限制评论数量

                result = await session.execute(query)
                comments = result.scalars().all()

                return [
                    {
                        "id": comment.id,
                        "content": comment.content,
                        "user_name": comment.user_name,
                        "created_at": comment.created_at.isoformat() if comment.created_at else None,
                        "replies": []  # 简化处理，暂不包含嵌套回复
                    }
                    for comment in comments
                ]
        except Exception as e:
            logger.error(f"获取评论树失败: {str(e)}")
            return []

    async def _get_gugu_bot_user(self) -> Optional[UserModel]:
        """获取gugu机器人用户"""
        try:
            async with self.async_session() as session:
                query = select(UserModel).where(UserModel.username == "gugu")
                result = await session.execute(query)
                return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取gugu机器人用户失败: {str(e)}")
            return None

    async def _create_gugu_reply(self, article_id: str, parent_comment_id: str, root_comment_id: str,
                                 ai_content: str, gugu_user: UserModel) -> None:
        """创建gugu机器人的AI回复"""
        try:
            async with self.async_session() as session:
                # 创建AI回复评论
                ai_comment = CommentModel(
                    content=ai_content,
                    project_id=article_id,
                    parent_id=parent_comment_id,
                    root_id=root_comment_id,
                    user_id=gugu_user.id,
                    user_name=gugu_user.nickname,
                    user_avatar=None,  # 机器人暂时没有头像
                    status="approved",  # AI回复直接通过审核
                    metadata={"ai_generated": True, "source": "smart_comment"},
                    created_by=gugu_user.id,
                    updated_by=gugu_user.id
                )

                session.add(ai_comment)
                await session.commit()

                # 更新父评论的回复数
                await self._update_comment_reply_count(session, parent_comment_id, 1)

                logger.info(f"gugu AI回复创建成功: {ai_comment.id}")

        except Exception as e:
            logger.error(f"创建gugu AI回复失败: {str(e)}")
            raise

    async def audit_article_comment(self, comment_id: str, audit_data: CommentAuditRequest, auditor_id: str) -> \
    Optional[CommentResponse]:
        """改变一个项目中某一条评论的状态（审核评论）"""
        try:
            async with self.async_session() as session:
                query = select(CommentModel).where(CommentModel.id == comment_id)
                result = await session.execute(query)
                comment = result.scalar_one_or_none()

                if not comment:
                    return None

                # 更新评论状态
                comment.status = audit_data.status
                comment.audit_result = audit_data.status
                comment.audit_reason = audit_data.audit_reason
                comment.audit_time = datetime.now(timezone.utc)
                comment.audit_by = auditor_id
                comment.updated_by = auditor_id

                await session.commit()
                await session.refresh(comment)

                return await self._to_comment_response(comment)

        except Exception as e:
            logger.error("审核文章评论失败", error=str(e), exc_info=True)
            raise

    async def _get_comment_replies(self, session, comment_id: str, max_depth: int = 3) -> List[CommentResponse]:
        """获取评论的回复
        
        Args:
            session: 数据库会话
            comment_id: 评论ID
            max_depth: 最大递归深度
        """
        # 如果深度为0或负数，不再获取回复
        if max_depth <= 0:
            return []
            
        replies = []

        query = select(CommentModel).where(
            CommentModel.parent_id == comment_id,
            CommentModel.status == "approved",
            CommentModel.is_public == True
        ).order_by(CommentModel.created_at)

        result = await session.execute(query)
        comment_replies = result.scalars().all()

        for reply in comment_replies:
            reply_response = await self._to_comment_response(reply)
            # 递归获取子回复（限制深度）
            if max_depth > 1:
                reply_response.replies = await self._get_comment_replies(session, reply.id, max_depth - 1)
            replies.append(reply_response)

        return replies

    async def _update_comment_reply_count(self, session, comment_id: str, delta: int):
        """更新评论的回复数"""
        query = select(CommentModel).where(CommentModel.id == comment_id)
        result = await session.execute(query)
        comment = result.scalar_one_or_none()

        if comment:
            comment.reply_count = max(0, comment.reply_count + delta)
            await session.commit()

    async def _update_article_comment_count(self, session, article_id: str, delta: int):
        """更新文章的评论数"""
        query = select(ArticleModel).where(ArticleModel.id == article_id)
        result = await session.execute(query)
        article = result.scalar_one_or_none()

        if article:
            article.comment_count = max(0, article.comment_count + delta)
            await session.commit()

    async def _to_comment_response(self, comment: CommentModel) -> CommentResponse:
        """转换为评论响应格式"""
        return CommentResponse(
            id=comment.id,
            content=comment.content,
            article_id=comment.project_id,
            parent_id=comment.parent_id,
            root_id=comment.root_id,
            user_id=comment.user_id,
            user_name=comment.user_name,
            user_avatar=comment.user_avatar,
            # 新增的父评论用户信息
            parent_user_id=comment.parent_user_id,
            parent_user_name=comment.parent_user_name,
            like_count=comment.like_count,
            reply_count=comment.reply_count,
            status=comment.status,
            is_public=comment.is_public,
            audit_result=comment.audit_result,
            audit_reason=comment.audit_reason,
            audit_time=comment.audit_time,
            audit_by=comment.audit_by,
            metadata=dict(comment.comment_metadata) if comment.comment_metadata else {},
            created_at=comment.created_at,
            updated_at=comment.updated_at,
            created_by=comment.created_by,
            replies=[]
        )

    async def delete_article_comment(self, comment_id: str, user_id: str, cascade: bool = True) -> bool:
        """删除文章评论
        
        Args:
            comment_id: 评论ID
            user_id: 当前用户ID
            cascade: 是否级联删除子评论
            
        Returns:
            bool: 删除是否成功
            
        Raises:
            ValueError: 当评论不存在或无权删除时
            Exception: 其他数据库错误
        """
        try:
            async with self.async_session() as session:
                # 查找要删除的评论
                query = select(CommentModel).where(CommentModel.id == comment_id)
                result = await session.execute(query)
                comment = result.scalar_one_or_none()
                
                if not comment:
                    raise ValueError("评论不存在")
                
                # 权限检查：只有评论作者可以删除自己的评论
                if comment.user_id != user_id:
                    raise ValueError("无权删除此评论")
                
                # 记录要更新统计的信息
                article_id = comment.project_id
                parent_id = comment.parent_id
                
                # 获取要删除的评论数量（包括子评论）
                deleted_count = 0
                
                if cascade:
                    # 级联删除：先获取所有子评论
                    child_comments = await self._get_all_child_comments(session, comment_id)
                    deleted_count = len(child_comments) + 1  # 包括根评论
                    
                    # 删除所有子评论
                    for child_comment in child_comments:
                        await session.delete(child_comment)
                else:
                    # 检查是否有子评论
                    child_query = select(CommentModel).where(CommentModel.parent_id == comment_id)
                    child_result = await session.execute(child_query)
                    child_comments = child_result.scalars().all()
                    
                    if child_comments:
                        raise ValueError("该评论有回复，请先删除回复或选择级联删除")
                    
                    deleted_count = 1
                
                # 删除主评论
                await session.delete(comment)
                
                # 更新父评论的回复数（如果有父评论）
                if parent_id:
                    await self._update_comment_reply_count(session, parent_id, -deleted_count)
                
                # 更新文章的评论数
                await self._update_article_comment_count(session, article_id, -deleted_count)
                
                await session.commit()
                
                logger.info(
                    "评论删除成功",
                    comment_id=comment_id,
                    user_id=user_id,
                    cascade=cascade,
                    deleted_count=deleted_count
                )
                
                return True
                
        except ValueError:
            # 重新抛出业务逻辑错误
            raise
        except Exception as e:
            logger.error("删除评论失败", error=str(e), comment_id=comment_id, exc_info=True)
            raise

    async def _get_all_child_comments(self, session, parent_comment_id: str) -> List[CommentModel]:
        """递归获取所有子评论
        
        Args:
            session: 数据库会话
            parent_comment_id: 父评论ID
            
        Returns:
            List[CommentModel]: 所有子评论列表
        """
        try:
            # 查找直接子评论
            query = select(CommentModel).where(CommentModel.parent_id == parent_comment_id)
            result = await session.execute(query)
            direct_children = result.scalars().all()
            
            all_children = list(direct_children)
            
            # 递归查找每个子评论的子评论
            for child in direct_children:
                grandchildren = await self._get_all_child_comments(session, child.id)
                all_children.extend(grandchildren)
            
            return all_children
            
        except Exception as e:
            logger.error("获取子评论失败", error=str(e), parent_id=parent_comment_id, exc_info=True)
            raise