<template>
  <div class="home-footer" :class="[props.theme === 'light' ? 'light' : '']">
    <span class="copyright">
      Copyright © 2025 上海鸪鸪信息技术有限公司 版权所有
    </span>
    <a
      class="beian"
      href="https://beian.miit.gov.cn"
      target="_blank"
      rel="noopener noreferrer">
      沪ICP备2025138230号-1
    </a>
  </div>
</template>

<script setup>
const props = defineProps({
  color: {
    type: String,
    default: "#8a8a8a",
  },
  theme: {
    type: String,
    default: "gray",
  },
});
</script>

<style scoped lang="scss">
.home-footer {
  height: 40px;
  // line-height: 40px;
  width: 100%;
  text-align: center;
  color: #8a8a8a;
  background-color: #fff;
  font-size: 14px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 0 10px;
  .copyright {
    white-space: nowrap;
    overflow: hidden;
  }
  .beian {
    &:hover {
      color: #5a5a5a;
    }
  }
  &.light {
    color: #fff;
    .beian {
      &:hover {
        color: #ddd;
      }
    }
  }
}
</style>
