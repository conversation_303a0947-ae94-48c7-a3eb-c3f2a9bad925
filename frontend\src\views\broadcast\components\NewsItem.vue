<template>
  <div
    class="news-item"
    :style="`width: ${width};height: ${height};`"
    v-preventLongPress="() => handlePreventLongPress()">
    <div class="left" :class="{ mobile: device === 'mobile', fill: !imageUrl }">
      <div class="title" :class="{ gray: gray }">
        <img
          v-if="project.content_type === 'article'"
          class="markicon"
          src="@/assets/images/icon30.png"
          alt="" />
        {{ project[getPropKey.title] }}
      </div>
      <div class="content">{{ project[getPropKey.summary] }}</div>
      <div class="meta">
        <div class="meta-item">
          <img class="meta-item-icon" src="@/assets/images/icon26.png" alt="" />
          {{ formatStar(project[getPropKey.read_count]) || "--" }}
        </div>
        <div
          class="meta-item cursor"
          @click.stop="handleCollect"
          @mousedown.stop
          @mouseup.stop>
          <Button1
            gray
            class="meta-item-icon"
            :filled="project[getPropKey.is_collected]"></Button1>
          {{ formatStar(project[getPropKey.collect_count]) || "--" }}
        </div>
        <div class="meta-item" v-if="project.content_type === 'article'">
          <img class="meta-item-icon" src="@/assets/images/icon27.png" alt="" />
          {{ formatStar(project[getPropKey.comment_count]) || "--" }}
        </div>
      </div>
    </div>
    <div class="right" :class="{ mobile: device === 'mobile' }">
      <img v-if="imageUrl" :src="imageUrl" alt="" />
    </div>
  </div>
</template>

<script setup>
import { formatStar } from "@/utils";
import { addUserCollect, deleteUserCollect } from "@/api/broadcast.js";
import preventLongPress from "@/directive/common/preventLongPress";
import useUserStore from "@/store/modules/user";
import Button1 from "@/components/Button/index1.vue";
const { proxy } = getCurrentInstance();
const userStore = useUserStore();
import useAppStore from "@/store/modules/app";
const device = computed(() => useAppStore().device);
defineOptions({
  directives: {
    preventLongPress,
  },
});
const props = defineProps({
  project: {
    type: Object,
    default: () => ({}),
  },
  width: {
    type: [Number, String],
    default: "100%",
  },
  height: {
    type: [Number, String],
    default: "auto",
  },
  // 是否使用灰色样式
  gray: {
    type: Boolean,
    default: false,
  },
});
const getPropKey = computed(() => {
  if (props.project.content_type === "project") {
    return {
      id: "id",
      title: "name",
      summary: "description_recommend",
      read_count: "read_count",
      is_collected: "is_collected",
      collect_count: "collect_count",
      comment_count: "comment_count",
      cover_image: "image_url",
    };
  } else {
    return {
      id: "id",
      title: "title",
      summary: "summary",
      read_count: "read_count",
      is_collected: "is_collected",
      collect_count: "collect_count",
      comment_count: "comment_count",
      cover_image: "cover_image",
    };
  }
});
const emit = defineEmits();
const handlePreventLongPress = () => {
  emit("toDetails", props.project);
};
// 图片预加载函数，设置超时时间
const preloadImage = (url, timeout = 5000) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const timer = setTimeout(() => {
      reject(new Error("Image load timeout"));
    }, timeout);

    img.onload = () => {
      clearTimeout(timer);
      resolve(url);
    };

    img.onerror = () => {
      clearTimeout(timer);
      reject(new Error("Image load failed"));
    };

    img.src = url;
  });
};
let imageUrl = ref("");
// 监听 cover_image 变化
watch(
  () => props.project[getPropKey.value.cover_image],
  async (newUrl) => {
    if (newUrl) {
      try {
        let url = await preloadImage(newUrl, 3000); // 3秒超时
        console.warn("url failed:", url);
        imageUrl.value = url;
      } catch (e) {
        console.warn("Image load failed:", e);
        imageUrl.value = "";
      }
    }
  },
  { immediate: true }
);
const handleCollect = async () => {
  // 判断用户是否登录，如果未登录，则提示用户去登录
  if (!userStore?.name) {
    proxy.$modal.msgWarning("登录后即可收藏");
    return;
  }
  let res = {};
  if (props.project[getPropKey.value.is_collected]) {
    let params = {};
    if (props.project.content_type === "article") {
      params.article_id = props.project[getPropKey.value.id];
    } else {
      params.project_id = props.project[getPropKey.value.id];
    }
    res = await deleteUserCollect(params);
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已取消收藏");
      props.project.is_collected =
        !props.project[getPropKey.value.is_collected];
      props.project[getPropKey.value.collect_count]--;
      // 通知父元素收藏操作成功
      emit("collectSuccess", "delete");
    }
  } else {
    let params = {};
    if (props.project.content_type === "article") {
      params.article_id = props.project[getPropKey.value.id];
    } else {
      params.project_id = props.project[getPropKey.value.id];
    }
    res = await addUserCollect(params);
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已收藏");
      props.project[getPropKey.value.is_collected] =
        !props.project[getPropKey.value.is_collected];
      props.project[getPropKey.value.collect_count]++;
      // 通知父元素收藏操作成功
      emit("collectSuccess", "add");
    }
  }
};
</script>

<style scoped lang="scss">
.news-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: solid 1px #ddd;
  padding-bottom: 8px;
  cursor: pointer;
}
.left {
  width: calc(100% - 170px);
  &.mobile {
    width: calc(100% - 110px);
  }
  &.fill {
    width: 100%;
  }
}
.right {
  width: 136px;
  height: 85px;
  border-radius: 8px;
  overflow: hidden;
  margin-left: 10px;
  &.mobile {
    width: 100px;
    height: 62px;
  }
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
    transition: transform 0.7s;
    &:hover {
      transform: scale(1.2);
    }
  }
}
.title {
  font-size: 16px;
  font-weight: bold;
  color: #4c6d7d;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  display: flex;
  align-items: center;
  &.gray {
    color: #16c0da;
  }
  .markicon {
    width: 24px;
    height: 24px;
    margin-right: 4px;
  }
}
.content {
  font-size: 14px;
  color: #8a8a8a;
  margin-top: 10px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.meta {
  font-size: 14px;
  color: #8a8a8a;
  margin-top: 10px;
  display: flex;
  align-items: center;
  gap: 20px;
  .meta-item {
    display: flex;
    align-items: center;
    &.cursor {
      cursor: pointer;
    }
    .meta-item-icon {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
  }
}

// .meta-favorite {
//   padding: 0 4px;
//   display: flex;
//   align-items: center;
//   justify-content: flex-end;
//   font-size: 12px;
//   color: #fff;
//   height: 100%;
//   width: fit-content;
//   .meta-favorite-icon {
//     width: 16px;
//     height: 16px;
//     margin-right: 4px;
//   }
// }
</style>
