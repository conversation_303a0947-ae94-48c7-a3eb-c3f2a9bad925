"""
内容类型适配器

为不同内容类型提供特定的搜索逻辑和字段映射。
"""
import logging
from typing import Dict, Any, List
from app.services.elasticsearch.client import ElasticsearchClient
from .types import ContentType, SearchRequest, ContentTypeAdapter
from .query_builder import UnifiedQueryBuilder

logger = logging.getLogger(__name__)


class ArticleAdapter(ContentTypeAdapter):
    """文章内容适配器"""
    
    def __init__(self, es_client: ElasticsearchClient):
        """
        初始化文章适配器

        Args:
            es_client: Elasticsearch客户端
        """
        self.es_client = es_client
        self.query_builder = UnifiedQueryBuilder()
        self._index_name = "articles-*"
        self._min_score_threshold = 30
    
    @property
    def content_type(self) -> ContentType:
        """返回内容类型"""
        return ContentType.ARTICLE
    
    @property
    def index_name(self) -> str:
        """返回索引名称"""
        return self._index_name
    
    async def search(self, request: SearchRequest) -> Dict[str, Any]:
        """
        执行文章搜索
        
        Args:
            request: 搜索请求
            
        Returns:
            Dict[str, Any]: 搜索结果
        """
        try:
            # 构建查询
            query = self.build_query(request)
            
            # 构建搜索体
            search_body = {
                "query": query,
                "from": (request.page - 1) * request.page_size,
                "size": request.page_size,
                "track_total_hits": True,
                "track_scores": True,
                "min_score": self._min_score_threshold
            }
            
            # 添加排序
            sort_conditions = self.query_builder.build_sort_conditions(
                request.sort_by, request.sort_order, self.get_sort_fields()
            )
            if sort_conditions:
                search_body["sort"] = sort_conditions
            
            # 添加高亮
            if request.highlight and request.query:
                highlight_fields = ["title", "content", "summary", "tags", "keywords"]
                search_body["highlight"] = self.query_builder.build_highlight_config(
                    highlight_fields, request.query
                )
            
            # 添加聚合
            search_body["aggs"] = self._build_aggregations()
            
            # 执行搜索
            result = self.es_client.client.search(
                index=self._index_name,
                body=search_body
            )
            
            return result
            
        except Exception as e:
            logger.error(f"文章搜索失败: {str(e)}")
            return {"hits": {"hits": [], "total": {"value": 0}}, "took": 0, "aggregations": {}}
    
    def build_query(self, request: SearchRequest) -> Dict[str, Any]:
        """
        构建文章查询
        
        Args:
            request: 搜索请求
            
        Returns:
            Dict[str, Any]: 查询条件
        """
        must_conditions = []
        should_conditions = []
        filter_conditions = []
        
        # 处理文本查询
        if request.query:
            text_fields = ["title", "content", "summary", "tags", "keywords"]
            boost_config = {
                "title": 3.0,
                "summary": 2.5,
                "content": 2.0,
                "tags": 2.5,
                "keywords": 1.5
            }
            
            text_query = self.query_builder.build_base_text_query(
                request.query, text_fields, boost_config
            )
            should_conditions.append(text_query)
        
        # 处理过滤条件
        if request.filters:
            validated_filters = self.validate_filters(request.filters)
            filter_conditions.extend(
                self.query_builder.build_filter_conditions(
                    validated_filters, self._get_field_mappings()
                )
            )
        
        # 组合查询条件
        if not must_conditions and not should_conditions and not filter_conditions:
            return {"match_all": {}}
        
        query_dict = {"bool": {}}
        
        if must_conditions:
            query_dict["bool"]["must"] = must_conditions
        
        if should_conditions:
            query_dict["bool"]["should"] = should_conditions
            if not must_conditions:
                query_dict["bool"]["minimum_should_match"] = 1
        
        if filter_conditions:
            query_dict["bool"]["filter"] = filter_conditions
        
        return query_dict
    
    def process_results(self, es_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        处理文章搜索结果
        
        Args:
            es_result: Elasticsearch搜索结果
            
        Returns:
            List[Dict[str, Any]]: 处理后的结果列表
        """
        hits = es_result.get("hits", {}).get("hits", [])
        results = []
        
        for hit in hits:
            article_data = hit["_source"]
            article_data["_score"] = hit.get("_score", 0)
            
            # 处理高亮
            if "highlight" in hit:
                article_data["highlights"] = self._process_highlights(hit["highlight"])
            
            results.append(article_data)
        
        return results
    
    def get_sort_fields(self) -> List[str]:
        """获取支持的排序字段"""
        return [
            "_score", "title", "created_at", "updated_at", "published_at",
            "read_count", "like_count", "comment_count", "collect_count",
            "popularity_score", "interaction_rate", "word_count", "estimated_reading_time"
        ]
    
    def validate_filters(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证文章过滤条件
        
        Args:
            filters: 原始过滤条件
            
        Returns:
            Dict[str, Any]: 验证后的过滤条件
        """
        validated_filters = {}
        
        # 状态过滤
        if "status" in filters and filters["status"]:
            valid_statuses = ["draft", "published", "archived"]
            if filters["status"] in valid_statuses:
                validated_filters["status"] = filters["status"]
        
        # 标签过滤
        if "tags" in filters and isinstance(filters["tags"], list):
            validated_filters["tags"] = [tag for tag in filters["tags"] if tag and isinstance(tag, str)]
        
        # 布尔值过滤
        for bool_field in ["is_public", "is_top"]:
            if bool_field in filters and isinstance(filters[bool_field], bool):
                validated_filters[bool_field] = filters[bool_field]
        
        # 日期范围过滤
        for date_field in ["date_range", "published_date_range"]:
            if date_field in filters and isinstance(filters[date_field], dict):
                validated_filters[date_field] = filters[date_field]
        
        # 数值范围过滤
        numeric_range_fields = [
            "read_count_range", "like_count_range", "comment_count_range",
            "collect_count_range", "popularity_score_range", "word_count_range",
            "reading_time_range"
        ]
        
        for field in numeric_range_fields:
            if field in filters and isinstance(filters[field], dict):
                validated_filters[field] = filters[field]
        
        # 关联项目过滤
        if "related_projects" in filters and isinstance(filters["related_projects"], list):
            validated_filters["related_projects"] = [
                project for project in filters["related_projects"] 
                if project and isinstance(project, str)
            ]
        
        return validated_filters
    
    def _get_field_mappings(self) -> Dict[str, str]:
        """获取字段映射"""
        return {
            "date_range": "created_at",
            "published_date_range": "published_at",
            "read_count_range": "read_count",
            "like_count_range": "like_count",
            "comment_count_range": "comment_count",
            "collect_count_range": "collect_count",
            "popularity_score_range": "popularity_score",
            "word_count_range": "word_count",
            "reading_time_range": "estimated_reading_time"
        }
    
    def _build_aggregations(self) -> Dict[str, Any]:
        """构建聚合查询"""
        return {
            "popular_tags": {"terms": {"field": "tags", "size": 20}},
            "status_distribution": {"terms": {"field": "status", "size": 10}},
            "read_count_stats": {"stats": {"field": "read_count"}},
            "like_count_stats": {"stats": {"field": "like_count"}},
            "comment_count_stats": {"stats": {"field": "comment_count"}},
            "collect_count_stats": {"stats": {"field": "collect_count"}},
            "popularity_score_stats": {"stats": {"field": "popularity_score"}},
            "word_count_stats": {"stats": {"field": "word_count"}},
            "reading_time_stats": {"stats": {"field": "estimated_reading_time"}},
            "interaction_rate_stats": {"stats": {"field": "interaction_rate"}},
            "public_articles": {"filter": {"term": {"is_public": True}}},
            "top_articles": {"filter": {"term": {"is_top": True}}},
            "published_articles": {"filter": {"term": {"status": "published"}}},
            "monthly_distribution": {
                "date_histogram": {
                    "field": "published_at",
                    "calendar_interval": "month",
                    "format": "yyyy-MM"
                }
            }
        }
    
    def _process_highlights(self, highlight_data: Dict[str, List[str]]) -> Dict[str, Any]:
        """处理高亮信息"""
        processed_highlights = []
        
        field_type_mapping = {
            "title": "文章标题",
            "title.ik": "文章标题",
            "title.english": "文章标题",
            "content": "文章内容",
            "content.ik": "文章内容",
            "content.english": "文章内容",
            "summary": "文章摘要",
            "summary.ik": "文章摘要",
            "summary.english": "文章摘要",
            "tags": "标签",
            "tags.ik": "标签",
            "tags.english": "标签",
            "keywords": "关键词"
        }
        
        for field, fragments in highlight_data.items():
            if field.endswith(".ngram"):
                continue
            
            field_type = field_type_mapping.get(field, field)
            
            highlight_item = {
                "field_type": field_type,
                "field_name": field,
                "source_type": "article",
                "fragments": fragments,
                "fragment_count": len(fragments)
            }
            
            processed_highlights.append(highlight_item)
        
        return {
            "highlights": processed_highlights,
            "total_matches": len(processed_highlights),
            "total_fragments": sum(h["fragment_count"] for h in processed_highlights)
        }


class ProjectAdapter(ContentTypeAdapter):
    """项目内容适配器"""

    def __init__(self, es_client: ElasticsearchClient):
        """
        初始化项目适配器

        Args:
            es_client: Elasticsearch客户端
        """
        self.es_client = es_client
        self.query_builder = UnifiedQueryBuilder()
        self._index_name = "projects-*"
        self._min_score_threshold = 0.1

    @property
    def content_type(self) -> ContentType:
        """返回内容类型"""
        return ContentType.PROJECT

    @property
    def index_name(self) -> str:
        """返回索引名称"""
        return self._index_name

    async def search(self, request: SearchRequest) -> Dict[str, Any]:
        """
        执行项目搜索

        Args:
            request: 搜索请求

        Returns:
            Dict[str, Any]: 搜索结果
        """
        try:
            # 构建查询
            query = self.build_query(request)

            # 构建搜索体
            search_body = {
                "query": query,
                "from": (request.page - 1) * request.page_size,
                "size": request.page_size,
                "track_total_hits": True,
                "track_scores": True,
                "min_score": self._min_score_threshold
            }

            # 添加排序
            sort_conditions = self.query_builder.build_sort_conditions(
                request.sort_by, request.sort_order, self.get_sort_fields()
            )
            if sort_conditions:
                search_body["sort"] = sort_conditions

            # 添加高亮
            if request.highlight and request.query:
                highlight_fields = ["name", "description_project", "description_recommend", "tags"]
                search_body["highlight"] = self.query_builder.build_highlight_config(
                    highlight_fields, request.query
                )

            # 添加聚合
            search_body["aggs"] = self._build_aggregations()

            # 执行搜索
            result = self.es_client.client.search(
                index=self._index_name,
                body=search_body
            )

            return result

        except Exception as e:
            logger.error(f"项目搜索失败: {str(e)}")
            return {"hits": {"hits": [], "total": {"value": 0}}, "took": 0, "aggregations": {}}

    def build_query(self, request: SearchRequest) -> Dict[str, Any]:
        """
        构建项目查询

        Args:
            request: 搜索请求

        Returns:
            Dict[str, Any]: 查询条件
        """
        must_conditions = []
        should_conditions = []
        filter_conditions = []

        # 处理文本查询
        if request.query:
            query = request.query.strip()
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in query)
            is_short_query = len(query) <= 3

            # 项目级别查询
            project_fields = ["name", "description_project", "description_recommend", "tags"]
            boost_config = {
                "name": 3.0,
                "description_project": 2.0,
                "description_recommend": 2.0,
                "tags": 2.5
            }

            project_query = self.query_builder.build_base_text_query(
                query, project_fields, boost_config
            )
            should_conditions.append(project_query)

            # 嵌套卡片查询
            card_query = {
                "nested": {
                    "path": "cards",
                    "query": {
                        "bool": {
                            "should": [
                                # 精确匹配
                                {"match_phrase": {"cards.title": {"query": query, "boost": 8.0}}},
                                {"match_phrase": {"cards.content": {"query": query, "boost": 5.0}}},

                                # 智能匹配
                                {"match": {
                                    "cards.title.ik" if has_chinese else "cards.title.english": {
                                        "query": query,
                                        "boost": 5.0,
                                        "minimum_should_match": "90%" if is_short_query else "80%",
                                        "operator": "and" if is_short_query else "or",
                                        **({
                                            "fuzziness": "1" if len(query) <= 6 else "AUTO"
                                        } if not has_chinese and len(query) >= 2 else {})
                                    }
                                }},
                                {"match": {
                                    "cards.content.ik" if has_chinese else "cards.content.english": {
                                        "query": query,
                                        "boost": 3.0,
                                        "minimum_should_match": "90%" if is_short_query else "80%",
                                        "operator": "and" if is_short_query else "or",
                                        **({
                                            "fuzziness": "1" if len(query) <= 6 else "AUTO"
                                        } if not has_chinese and len(query) >= 2 else {})
                                    }
                                }},

                                # 前缀匹配
                                {"match_phrase_prefix": {
                                    "cards.title.ik" if has_chinese else "cards.title.english": {
                                        "query": query, "boost": 4.0
                                    }
                                }}
                            ],
                            "minimum_should_match": 1
                        }
                    },
                    "inner_hits": {
                        "highlight": self.query_builder.build_highlight_config(
                            ["cards.title", "cards.content"], query
                        )
                    }
                }
            }
            should_conditions.append(card_query)

        # 处理过滤条件
        if request.filters:
            validated_filters = self.validate_filters(request.filters)

            # 分离项目和卡片过滤条件
            project_filters = {k: v for k, v in validated_filters.items() if not k.startswith('card_')}
            card_filters = {k[5:]: v for k, v in validated_filters.items() if k.startswith('card_')}

            # 项目过滤条件
            if project_filters:
                filter_conditions.extend(
                    self.query_builder.build_filter_conditions(
                        project_filters, self._get_field_mappings()
                    )
                )

            # 卡片过滤条件
            if card_filters:
                nested_filters = []
                for field, value in card_filters.items():
                    card_field = f"cards.{field}"
                    if isinstance(value, list):
                        nested_filters.append({"terms": {card_field: value}})
                    elif isinstance(value, dict):
                        nested_filters.append({"range": {card_field: value}})
                    else:
                        nested_filters.append({"term": {card_field: value}})

                if nested_filters:
                    filter_conditions.append({
                        "nested": {
                            "path": "cards",
                            "query": {
                                "bool": {
                                    "filter": nested_filters
                                }
                            }
                        }
                    })

        # 组合查询条件
        if not must_conditions and not should_conditions and not filter_conditions:
            return {"match_all": {}}

        query_dict = {"bool": {}}

        if must_conditions:
            query_dict["bool"]["must"] = must_conditions

        if should_conditions:
            query_dict["bool"]["should"] = should_conditions
            if not must_conditions:
                query_dict["bool"]["minimum_should_match"] = 1

        if filter_conditions:
            query_dict["bool"]["filter"] = filter_conditions

        return query_dict

    def process_results(self, es_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        处理项目搜索结果

        Args:
            es_result: Elasticsearch搜索结果

        Returns:
            List[Dict[str, Any]]: 处理后的结果列表
        """
        hits = es_result.get("hits", {}).get("hits", [])
        results = []

        for hit in hits:
            project_data = hit["_source"]
            project_data["_score"] = hit.get("_score", 0)

            # 处理高亮
            if "highlight" in hit:
                project_data["highlights"] = self._process_highlights(hit["highlight"])

            # 处理嵌套卡片
            nested_cards = self._process_nested_card_hits(hit)
            if nested_cards:
                project_data["matching_cards"] = nested_cards

            results.append(project_data)

        return results

    def get_sort_fields(self) -> List[str]:
        """获取支持的排序字段"""
        return [
            "_score", "name", "created_at", "updated_at", "cards_count",
            "likes_count", "views_count"
        ]

    def validate_filters(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证项目过滤条件

        Args:
            filters: 原始过滤条件

        Returns:
            Dict[str, Any]: 验证后的过滤条件
        """
        validated_filters = {}

        # 状态过滤
        if "status" in filters and filters["status"]:
            validated_filters["status"] = filters["status"]

        # 标签过滤
        if "tags" in filters and isinstance(filters["tags"], list):
            validated_filters["tags"] = [tag for tag in filters["tags"] if tag and isinstance(tag, str)]

        # 仓库URL过滤（支持单个字符串或列表）
        if "repository_url" in filters:
            if isinstance(filters["repository_url"], str) and filters["repository_url"].strip():
                validated_filters["repository_url"] = filters["repository_url"].strip()
            elif isinstance(filters["repository_url"], list):
                validated_urls = [url for url in filters["repository_url"] if url and isinstance(url, str)]
                if validated_urls:
                    validated_filters["repository_url"] = validated_urls

        # 日期范围过滤
        if "date_range" in filters and isinstance(filters["date_range"], dict):
            validated_filters["date_range"] = filters["date_range"]

        # 数值范围过滤
        numeric_range_fields = ["cards_count_range", "likes_count_range", "views_count_range"]
        for field in numeric_range_fields:
            if field in filters and isinstance(filters[field], dict):
                validated_filters[field] = filters[field]

        # 卡片相关过滤条件（添加card_前缀）
        card_filter_fields = ["card_tags", "card_type", "card_status"]
        for field in card_filter_fields:
            if field in filters:
                validated_filters[field] = filters[field]

        return validated_filters

    def _get_field_mappings(self) -> Dict[str, str]:
        """获取字段映射"""
        return {
            "date_range": "created_at",
            "cards_count_range": "cards_count",
            "likes_count_range": "likes_count",
            "views_count_range": "views_count"
        }

    def _build_aggregations(self) -> Dict[str, Any]:
        """构建聚合查询"""
        return {
            "popular_tags": {"terms": {"field": "tags", "size": 20}},
            "status_distribution": {"terms": {"field": "status", "size": 10}},
            "cards_count_stats": {"stats": {"field": "cards_count"}},
            "likes_count_stats": {"stats": {"field": "likes_count"}},
            "views_count_stats": {"stats": {"field": "views_count"}},
            "nested_cards_stats": {
                "nested": {"path": "cards"},
                "aggs": {
                    "likes": {"sum": {"field": "cards.like"}},
                    "collects": {"sum": {"field": "cards.collect"}},
                    "dislikes": {"sum": {"field": "cards.dislike"}},
                    "types_distribution": {"terms": {"field": "cards.card_type", "size": 10}}
                }
            }
        }

    def _process_nested_card_hits(self, hit: Dict[str, Any]) -> List[Dict[str, Any]]:
        """处理嵌套卡片的匹配结果"""
        matching_cards = []

        inner_hits = hit.get("inner_hits", {})
        if not inner_hits or "cards" not in inner_hits:
            return matching_cards

        card_hits = inner_hits.get("cards", {}).get("hits", {}).get("hits", [])

        for card_hit in card_hits:
            card_source = card_hit.get("_source", {})
            card_data = {
                "id": card_source.get("id", ""),
                "title": card_source.get("title", ""),
                "content": card_source.get("content", ""),
                "sort_order": card_source.get("sort_order", 0),
                "like": card_source.get("like", 0),
                "collect": card_source.get("collect", 0),
                "dislike": card_source.get("dislike", 0),
                "card_type": card_source.get("card_type", ""),
                "created_at": card_source.get("created_at", ""),
                "updated_at": card_source.get("updated_at", ""),
                "_score": card_hit.get("_score", 0)
            }

            # 处理卡片高亮
            if "highlight" in card_hit:
                card_data["highlights"] = self._process_card_highlights(card_hit["highlight"])

            matching_cards.append(card_data)

        return matching_cards

    def _process_highlights(self, highlight_data: Dict[str, List[str]]) -> Dict[str, Any]:
        """处理项目高亮信息"""
        processed_highlights = []

        field_type_mapping = {
            "name": "项目名称",
            "name.ik": "项目名称",
            "name.english": "项目名称",
            "description_project": "项目描述",
            "description_project.ik": "项目描述",
            "description_project.english": "项目描述",
            "description_recommend": "推荐描述",
            "description_recommend.ik": "推荐描述",
            "description_recommend.english": "推荐描述",
            "tags": "标签",
            "tags.ik": "标签",
            "tags.english": "标签"
        }

        for field, fragments in highlight_data.items():
            if field.endswith(".ngram"):
                continue

            field_type = field_type_mapping.get(field, field)

            highlight_item = {
                "field_type": field_type,
                "field_name": field,
                "source_type": "project",
                "fragments": fragments,
                "fragment_count": len(fragments)
            }

            processed_highlights.append(highlight_item)

        return {
            "highlights": processed_highlights,
            "total_matches": len(processed_highlights),
            "total_fragments": sum(h["fragment_count"] for h in processed_highlights)
        }

    def _process_card_highlights(self, highlight_data: Dict[str, List[str]]) -> Dict[str, Any]:
        """处理卡片高亮信息"""
        processed_highlights = []

        field_type_mapping = {
            "cards.title": "卡片标题",
            "cards.title.ik": "卡片标题",
            "cards.title.english": "卡片标题",
            "cards.content": "卡片内容",
            "cards.content.ik": "卡片内容",
            "cards.content.english": "卡片内容"
        }

        for field, fragments in highlight_data.items():
            if field.endswith(".ngram"):
                continue

            field_type = field_type_mapping.get(field, field)

            highlight_item = {
                "field_type": field_type,
                "field_name": field,
                "source_type": "card",
                "fragments": fragments,
                "fragment_count": len(fragments)
            }

            processed_highlights.append(highlight_item)

        return {
            "highlights": processed_highlights,
            "total_matches": len(processed_highlights),
            "total_fragments": sum(h["fragment_count"] for h in processed_highlights)
        }
