input {
  jdbc {
    jdbc_driver_library => "/usr/share/logstash/logstash-core/lib/jars/postgresql-42.7.3.jar"
    jdbc_driver_class => "org.postgresql.Driver"
    jdbc_connection_string => "*****************************************"
    jdbc_user => "gugu_apex"
    jdbc_password => "gugu_apex_pass"
    jdbc_validate_connection => true
    jdbc_paging_enabled => true
    jdbc_page_size => 100
    jdbc_default_timezone => "UTC"
    schedule => "*/2 * * * *"  # 每2分钟执行一次
    target => "article_data"
    statement => "
      SELECT 
        a.id, a.title, a.content, a.summary, a.keywords,
        a.tags::text as tags, a.status, a.is_public, a.is_top,
        a.read_count, a.comment_count, a.like_count, a.collect_count,
        a.shared_count_link, a.shared_link, a.sort_order,
        a.related_projects::text as related_projects,
        a.image_urls::text as image_urls, a.cover_image,
        a.published_at, a.created_at, a.updated_at, 
        a.created_by, a.updated_by
      FROM articles a
      WHERE a.updated_at > :sql_last_value
      ORDER BY a.updated_at ASC
    "
    last_run_metadata_path => "/usr/share/logstash/data/article_last_run.temp"
    use_column_value => true
    tracking_column => "updated_at"
    tracking_column_type => "timestamp"
    clean_run => true
  }
}

filter {
  # 移除timestamp和version字段
  mutate {
    remove_field => [ "@timestamp", "@version" ]
  }
  
  # 转换时间格式
  date {
    match => ["[article_data][created_at]", "ISO8601"]
    target => "[article_data][created_at]"
    tag_on_failure => ["_date_created_at_parsefailure"]
  }
  date {
    match => ["[article_data][updated_at]", "ISO8601"]
    target => "[article_data][updated_at]"
    tag_on_failure => ["_date_updated_at_parsefailure"]
  }

  # 处理发布时间
  if [article_data][published_at] {
    date {
      match => ["[article_data][published_at]", "ISO8601"]
      target => "[article_data][published_at]"
      tag_on_failure => ["_date_published_at_parsefailure"]
    }
  }
  
  # 添加索引时间
  ruby {
    code => "event.set('indexed_at', Time.now.utc)"
  }
  
  # 处理标签字段
  if [article_data][tags] {
    ruby {
      code => "
        require 'json'
        begin
          tags = event.get('[article_data][tags]')
          clean_tags = []

          # 首先检查tags的类型
          if tags.is_a?(Array)
            # 如果是数组，过滤掉错误标记和空标签
            tags.each do |tag|
              # 跳过以下开头的标签项，这些是错误标签
              next if tag.is_a?(String) && (
                tag.start_with?('_') ||
                tag.empty? ||
                tag == '[]'
              )

              # 如果是JSON字符串，尝试解析
              if tag.is_a?(String) && (
                tag.start_with?('[') ||
                tag.start_with?('{')
              )
                begin
                  parsed = JSON.parse(tag)
                  if parsed.is_a?(Array)
                    clean_tags.concat(parsed)
                  else
                    clean_tags << parsed.to_s
                  end
                rescue
                  clean_tags << tag
                end
              else
                clean_tags << tag
              end
            end
          elsif tags.is_a?(String)
            # 如果是字符串，尝试解析为JSON
            tags = tags.strip
            if !tags.empty?
              if tags.start_with?('[') || tags.start_with?('{')
                begin
                  parsed = JSON.parse(tags)
                  if parsed.is_a?(Array)
                    clean_tags = parsed
                  else
                    clean_tags << parsed.to_s
                  end
                rescue
                  clean_tags << tags
                end
              else
                clean_tags << tags
              end
            end
          end

          # 去重并过滤空值
          clean_tags = clean_tags.compact.uniq.reject(&:empty?)

          event.set('[article_data][tags]', clean_tags)
        rescue => e
          # 发生错误时，设置为空数组
          event.set('[article_data][tags]', [])
          event.set('_tag_parse_error', e.message)
        end
      "
    }
  } else {
    mutate {
      add_field => { "[article_data][tags]" => [] }
    }
  }
  
  # 处理关联项目字段
  if [article_data][related_projects] {
    ruby {
      code => "
        require 'json'
        begin
          related_projects = event.get('[article_data][related_projects]')
          if related_projects && !related_projects.empty?
            if related_projects.is_a?(String)
              parsed = JSON.parse(related_projects)
            else
              parsed = related_projects
            end

            if parsed && parsed.is_a?(Array)
              event.set('[article_data][related_projects]', parsed)
              event.set('[article_data][related_projects_count]', parsed.size)
            else
              event.set('[article_data][related_projects]', [])
              event.set('[article_data][related_projects_count]', 0)
            end
          else
            event.set('[article_data][related_projects]', [])
            event.set('[article_data][related_projects_count]', 0)
          end
        rescue => e
          event.set('[article_data][related_projects]', [])
          event.set('[article_data][related_projects_count]', 0)
          event.set('_related_projects_error', e.message)
        end
      "
    }
  } else {
    mutate {
      add_field => { "[article_data][related_projects]" => [] }
      add_field => { "[article_data][related_projects_count]" => 0 }
    }
  }
  
  # 处理图片URL字段
  if [article_data][image_urls] {
    ruby {
      code => "
        require 'json'
        begin
          image_urls = event.get('[article_data][image_urls]')
          if image_urls && !image_urls.empty?
            if image_urls.is_a?(String)
              parsed = JSON.parse(image_urls)
            else
              parsed = image_urls
            end

            if parsed && parsed.is_a?(Array)
              event.set('[article_data][image_urls]', parsed)
              event.set('[article_data][image_count]', parsed.size)
            else
              event.set('[article_data][image_urls]', [])
              event.set('[article_data][image_count]', 0)
            end
          else
            event.set('[article_data][image_urls]', [])
            event.set('[article_data][image_count]', 0)
          end
        rescue => e
          event.set('[article_data][image_urls]', [])
          event.set('[article_data][image_count]', 0)
          event.set('_image_urls_error', e.message)
        end
      "
    }
  } else {
    mutate {
      add_field => { "[article_data][image_urls]" => [] }
      add_field => { "[article_data][image_count]" => 0 }
    }
  }
  
  # 添加文章长度统计
  if [article_data][content] {
    ruby {
      code => "
        content = event.get('[article_data][content]')
        if content && content.is_a?(String)
          # 计算字符数
          event.set('[article_data][content_length]', content.length)
          # 计算字数（中文按字符计算，英文按单词计算）
          chinese_chars = content.scan(/[\u4e00-\u9fa5]/).length
          english_words = content.scan(/[a-zA-Z]+/).length
          event.set('[article_data][word_count]', chinese_chars + english_words)
          # 估算阅读时间（分钟）
          reading_time = [(chinese_chars + english_words * 2) / 200.0, 1].max.round
          event.set('[article_data][estimated_reading_time]', reading_time)
        else
          event.set('[article_data][content_length]', 0)
          event.set('[article_data][word_count]', 0)
          event.set('[article_data][estimated_reading_time]', 0)
        end
      "
    }
  }
  
  # 添加文章热度评分
  ruby {
    code => "
      read_count = event.get('[article_data][read_count]') || 0
      like_count = event.get('[article_data][like_count]') || 0
      comment_count = event.get('[article_data][comment_count]') || 0
      collect_count = event.get('[article_data][collect_count]') || 0
      shared_count = event.get('[article_data][shared_count_link]') || 0

      # 计算热度评分（权重：阅读1分，点赞3分，评论5分，收藏8分，分享10分）
      popularity_score = read_count * 1 + like_count * 3 + comment_count * 5 + collect_count * 8 + shared_count * 10
      event.set('[article_data][popularity_score]', popularity_score)

      # 计算互动率
      total_interactions = like_count + comment_count + collect_count + shared_count
      interaction_rate = read_count > 0 ? (total_interactions.to_f / read_count * 100).round(2) : 0
      event.set('[article_data][interaction_rate]', interaction_rate)
    "
  }
  
  # 设置搜索建议字段
  if [article_data][title] {
    mutate {
      add_field => { "[article_data][title_suggest]" => "%{[article_data][title]}" }
    }
  }

  # 在 filter 末尾提取 article_data 下的数据作为文档内容
  mutate {
    rename => { "[article_data]" => "[@metadata][temp_data]" }
  }

  ruby {
    code => "
      temp_data = event.get('[@metadata][temp_data]')
      if temp_data && temp_data.is_a?(Hash)
        temp_data.each do |key, value|
          event.set(key, value)
        end
      end

      # 添加当前时间戳用于索引命名
      require 'time'
      event.set('[@metadata][index_date]', Time.now.strftime('%Y.%m.%d'))
    "
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "articles-%{[@metadata][index_date]}"
    document_id => "%{id}"
    template => "/usr/share/logstash/templates/article_template.json"
    template_name => "article_template"
    template_overwrite => true
    ilm_enabled => false
    action => "index"
    manage_template => true
    # 允许在发送失败时重试
    retry_on_conflict => 5
  }
  # 调试输出
  stdout { codec => rubydebug }
}
