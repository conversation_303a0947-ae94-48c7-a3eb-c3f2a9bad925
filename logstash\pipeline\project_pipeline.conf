input {
  jdbc {
    jdbc_driver_library => "/usr/share/logstash/logstash-core/lib/jars/postgresql-42.7.3.jar"
    jdbc_driver_class => "org.postgresql.Driver"
    jdbc_connection_string => "*****************************************"
    jdbc_user => "gugu_apex"
    jdbc_password => "gugu_apex_pass"
    jdbc_validate_connection => true
    jdbc_paging_enabled => true
    jdbc_page_size => 100
    jdbc_default_timezone => "UTC"
    schedule => "*/2 * * * *"  # 每2分钟执行一次
    target => "project_data"
    statement => "
      SELECT 
        p.id, p.name, p.repository_url, p.description_project, p.description_recommend,
        p.tags::text as tags, p.status, p.background_color, p.button_color, p.local_path,
        p.image_url, p.icon_url, p.architecture_mermaid, p.dependency_mermaid,
        p.created_at, p.updated_at, p.created_by, p.updated_by,
        (
          SELECT json_agg(card_data)::text
          FROM (
            SELECT 
              c.id, c.project_id, c.title, c.content, 
              c.like, c.dislike, c.collect, c.sort_order,
              c.created_at, c.updated_at, c.created_by, c.updated_by
            FROM github_projects_cards c
            WHERE c.project_id = p.id
            ORDER BY c.sort_order ASC
          ) card_data
        ) AS cards
      FROM github_projects p
      WHERE p.updated_at > :sql_last_value
      ORDER BY p.updated_at ASC
    "
    last_run_metadata_path => "/usr/share/logstash/data/project_last_run.temp"
    use_column_value => true
    tracking_column => "updated_at"
    tracking_column_type => "timestamp"
    clean_run => true
  }
}

filter {
  # 移除timestamp和version字段
  mutate {
    remove_field => [ "@timestamp", "@version" ]
  }
  
  # 转换时间格式
  date {
    match => ["[project_data][created_at]", "ISO8601"]
    target => "[project_data][created_at]"
    tag_on_failure => ["_date_created_at_parsefailure"]
  }
  date {
    match => ["[project_data][updated_at]", "ISO8601"]
    target => "[project_data][updated_at]"
    tag_on_failure => ["_date_updated_at_parsefailure"]
  }
  
  # 添加索引时间
  ruby {
    code => "event.set('indexed_at', Time.now.utc)"
  }
  
  # 处理卡片数据
  if [project_data][cards] {
    mutate {
      add_field => { "[project_data][cards_count]" => 0 }
    }
    ruby {
      code => "
        require 'json'
        begin
          cards_str = event.get('[project_data][cards]')
          if cards_str && !cards_str.empty?
            # 解析JSON字符串为数组
            if cards_str.is_a?(String)
              cards = JSON.parse(cards_str)
            else
              cards = cards_str
            end

            if cards && cards.is_a?(Array)
              event.set('[project_data][cards_count]', cards.size)

              # 处理每个卡片中的时间字段
              cards.each do |card|
                if card['created_at']
                  card['created_at'] = card['created_at'].is_a?(Time) ? card['created_at'] : Time.parse(card['created_at'].to_s)
                end
                if card['updated_at']
                  card['updated_at'] = card['updated_at'].is_a?(Time) ? card['updated_at'] : Time.parse(card['updated_at'].to_s)
                end
              end
              event.set('[project_data][cards]', cards)
            else
              event.set('[project_data][cards]', [])
            end
          else
            event.set('[project_data][cards]', [])
          end
        rescue => e
          event.set('_card_error', e.message)
        end
      "
    }
  } else {
    mutate {
      add_field => { "[project_data][cards]" => [] }
      add_field => { "[project_data][cards_count]" => 0 }
    }
  }
  
  # 添加初始统计数据
  if ![project_data][views_count] {
    mutate {
      add_field => { "[project_data][views_count]" => 0 }
    }
  }

  if ![project_data][likes_count] {
    mutate {
      add_field => { "[project_data][likes_count]" => 0 }
    }
  }
  
  # 处理标签字段，根据不同数据类型进行处理
  if [project_data][tags] {
    ruby {
      code => "
        begin
          tags = event.get('[project_data][tags]')
          clean_tags = []

          # 首先检查tags的类型
          if tags.is_a?(Array)
            # 如果是数组，过滤掉错误标记和空标签
            tags.each do |tag|
              # 跳过以下开头的标签项，这些是错误标签
              next if tag.is_a?(String) && (
                tag.start_with?('_') ||
                tag.empty? ||
                tag == '[]'
              )

              # 如果是JSON字符串，尝试解析
              if tag.is_a?(String) && (
                tag.start_with?('[') ||
                tag.start_with?('{')
              )
                begin
                  parsed = JSON.parse(tag)
                  if parsed.is_a?(Array)
                    clean_tags.concat(parsed)
                  else
                    clean_tags << parsed.to_s
                  end
                rescue
                  clean_tags << tag
                end
              else
                clean_tags << tag
              end
            end
          elsif tags.is_a?(String)
            # 如果是字符串，尝试解析为JSON
            tags = tags.strip
            if !tags.empty?
              if tags.start_with?('[') || tags.start_with?('{')
                begin
                  parsed = JSON.parse(tags)
                  if parsed.is_a?(Array)
                    clean_tags = parsed
                  else
                    clean_tags << parsed.to_s
                  end
                rescue
                  clean_tags << tags
                end
              else
                clean_tags << tags
              end
            end
          end

          # 去重
          clean_tags.uniq!

          event.set('[project_data][tags]', clean_tags)
        rescue => e
          # 发生错误时，设置为空数组
          event.set('[project_data][tags]', [])
          event.set('_tag_parse_error', e.message)
        end
      "
    }
  } else {
    mutate {
      add_field => { "[project_data][tags]" => [] }
    }
  }

  # 在 filter 末尾提取 project_data 下的数据作为文档内容
  mutate {
    rename => { "[project_data]" => "[@metadata][temp_data]" }
  }

  ruby {
    code => "
      temp_data = event.get('[@metadata][temp_data]')
      if temp_data && temp_data.is_a?(Hash)
        temp_data.each do |key, value|
          event.set(key, value)
        end
      end

      # 添加当前时间戳用于索引命名
      require 'time'
      event.set('[@metadata][index_date]', Time.now.strftime('%Y.%m.%d'))
    "
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "projects-%{[@metadata][index_date]}"
    document_id => "%{id}"
    template => "/usr/share/logstash/templates/project_template.json"
    template_name => "project_template"
    template_overwrite => true
    ilm_enabled => false
    action => "index"
    manage_template => true
    # 允许在发送失败时重试
    retry_on_conflict => 5
  }
  # 调试输出
  stdout { codec => rubydebug }
}
