"""
通用搜索工具函数

提供搜索相关的验证、配置和辅助函数。
"""
import re
import logging
from typing import Dict, Any, List, Optional, Tuple
from .types import SearchRequest, ContentType, SortOrder

logger = logging.getLogger(__name__)


class SearchValidationUtils:
    """搜索验证工具类"""
    
    @staticmethod
    def validate_search_request(request: SearchRequest) -> SearchRequest:
        """
        验证并修正搜索请求
        
        Args:
            request: 原始搜索请求
            
        Returns:
            SearchRequest: 验证后的搜索请求
        """
        # 验证查询字符串
        if request.query:
            request.query = SearchValidationUtils.sanitize_query(request.query)
            if not request.query:
                request.query = None
        
        # 验证内容类型
        if not request.content_types:
            request.content_types = [ContentType.ALL]
        
        # 验证分页参数
        request.page = max(1, request.page)
        request.page_size = max(1, min(100, request.page_size))
        
        # 验证排序参数
        if request.sort_by:
            request.sort_by = SearchValidationUtils.validate_sort_field(request.sort_by)
        
        # 验证过滤条件
        if request.filters:
            request.filters = SearchValidationUtils.validate_filters(request.filters)
        
        return request
    
    @staticmethod
    def sanitize_query(query: str) -> str:
        """
        清理查询字符串
        
        Args:
            query: 原始查询字符串
            
        Returns:
            str: 清理后的查询字符串
        """
        if not query:
            return ""
        
        # 移除特殊字符，保留中文、英文、数字、空格和常用标点
        cleaned_query = re.sub(r'[^\u4e00-\u9fff\w\s\-_.,!?()[\]{}"]', ' ', query)
        
        # 压缩多个空格为单个空格
        cleaned_query = re.sub(r'\s+', ' ', cleaned_query).strip()
        
        # 限制查询长度
        if len(cleaned_query) > 200:
            cleaned_query = cleaned_query[:200]
        
        return cleaned_query
    
    @staticmethod
    def validate_sort_field(sort_by: str) -> str:
        """
        验证排序字段
        
        Args:
            sort_by: 排序字段
            
        Returns:
            str: 验证后的排序字段
        """
        valid_sort_fields = [
            "_score", "title", "name", "created_at", "updated_at", "published_at",
            "read_count", "like_count", "comment_count", "collect_count",
            "popularity_score", "interaction_rate", "word_count", "estimated_reading_time",
            "cards_count", "likes_count", "views_count", "popularity", "relevance"
        ]
        
        if sort_by and sort_by in valid_sort_fields:
            return sort_by
        else:
            return "_score"
    
    @staticmethod
    def validate_filters(filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证过滤条件
        
        Args:
            filters: 原始过滤条件
            
        Returns:
            Dict[str, Any]: 验证后的过滤条件
        """
        if not filters:
            return {}
        
        validated_filters = {}
        
        # 验证字符串过滤
        string_fields = ["status", "content_type", "repository_url"]
        for field in string_fields:
            if field in filters and isinstance(filters[field], str) and filters[field].strip():
                validated_filters[field] = filters[field].strip()
        
        # 验证列表过滤
        list_fields = ["tags", "related_projects", "card_tags", "repository_url"]
        for field in list_fields:
            if field in filters and isinstance(filters[field], list):
                validated_list = [item for item in filters[field] if item and isinstance(item, str)]
                if validated_list:
                    validated_filters[field] = validated_list
        
        # 验证布尔值过滤
        bool_fields = ["is_public", "is_top"]
        for field in bool_fields:
            if field in filters and isinstance(filters[field], bool):
                validated_filters[field] = filters[field]
        
        # 验证日期范围过滤
        date_range_fields = ["date_range", "published_date_range"]
        for field in date_range_fields:
            if field in filters and isinstance(filters[field], dict):
                validated_filters[field] = SearchValidationUtils._validate_date_range(filters[field])
        
        # 验证数值范围过滤
        numeric_range_fields = [
            "read_count_range", "like_count_range", "comment_count_range",
            "collect_count_range", "popularity_score_range", "word_count_range",
            "reading_time_range", "cards_count_range", "likes_count_range", "views_count_range"
        ]
        for field in numeric_range_fields:
            if field in filters and isinstance(filters[field], dict):
                validated_filters[field] = SearchValidationUtils._validate_numeric_range(filters[field])
        
        return validated_filters
    
    @staticmethod
    def _validate_date_range(date_range: Dict[str, Any]) -> Dict[str, Any]:
        """验证日期范围"""
        validated_range = {}
        
        if "gte" in date_range:
            validated_range["gte"] = date_range["gte"]
        if "lte" in date_range:
            validated_range["lte"] = date_range["lte"]
        if "gt" in date_range:
            validated_range["gt"] = date_range["gt"]
        if "lt" in date_range:
            validated_range["lt"] = date_range["lt"]
        
        return validated_range if validated_range else {}
    
    @staticmethod
    def _validate_numeric_range(numeric_range: Dict[str, Any]) -> Dict[str, Any]:
        """验证数值范围"""
        validated_range = {}
        
        for key in ["gte", "lte", "gt", "lt"]:
            if key in numeric_range:
                try:
                    value = float(numeric_range[key])
                    if value >= 0:  # 确保非负数
                        validated_range[key] = value
                except (ValueError, TypeError):
                    continue
        
        return validated_range if validated_range else {}


class SearchConfigUtils:
    """搜索配置工具类"""
    
    # 默认配置
    DEFAULT_CONFIG = {
        "max_page_size": 100,
        "default_page_size": 10,
        "max_query_length": 200,
        "min_score_threshold": {
            "article": 30,
            "project": 0.1
        },
        "highlight_config": {
            "fragment_size": 150,
            "number_of_fragments": 3,
            "pre_tags": ["<mark>"],
            "post_tags": ["</mark>"]
        },
        "boost_config": {
            "article": {
                "title": 3.0,
                "summary": 2.5,
                "content": 2.0,
                "tags": 2.5,
                "keywords": 1.5
            },
            "project": {
                "name": 3.0,
                "description_project": 2.0,
                "description_recommend": 2.0,
                "tags": 2.5
            }
        }
    }
    
    def __init__(self, custom_config: Dict[str, Any] = None):
        """
        初始化配置工具
        
        Args:
            custom_config: 自定义配置
        """
        self.config = self.DEFAULT_CONFIG.copy()
        if custom_config:
            self._merge_config(custom_config)
    
    def _merge_config(self, custom_config: Dict[str, Any]):
        """合并自定义配置"""
        def deep_merge(base_dict: Dict, custom_dict: Dict):
            for key, value in custom_dict.items():
                if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                    deep_merge(base_dict[key], value)
                else:
                    base_dict[key] = value
        
        deep_merge(self.config, custom_config)
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_boost_config(self, content_type: str) -> Dict[str, float]:
        """
        获取内容类型的权重配置
        
        Args:
            content_type: 内容类型
            
        Returns:
            Dict[str, float]: 权重配置
        """
        return self.get_config(f"boost_config.{content_type}", {})
    
    def get_min_score_threshold(self, content_type: str) -> float:
        """
        获取最小分数阈值
        
        Args:
            content_type: 内容类型
            
        Returns:
            float: 最小分数阈值
        """
        return self.get_config(f"min_score_threshold.{content_type}", 0.1)


class SearchMetricsUtils:
    """搜索指标工具类"""
    
    @staticmethod
    def calculate_search_quality_score(items: List[Dict[str, Any]], 
                                     query: str) -> Dict[str, Any]:
        """
        计算搜索质量分数
        
        Args:
            items: 搜索结果项列表
            query: 搜索查询
            
        Returns:
            Dict[str, Any]: 质量分数指标
        """
        if not items:
            return {
                "quality_score": 0.0,
                "relevance_score": 0.0,
                "diversity_score": 0.0,
                "coverage_score": 0.0
            }
        
        # 计算相关性分数（基于平均分数）
        avg_score = sum(item.get("score", 0) for item in items) / len(items)
        relevance_score = min(avg_score / 100, 1.0)  # 归一化到0-1
        
        # 计算多样性分数（基于内容类型分布）
        content_types = set(item.get("content_type", "") for item in items)
        diversity_score = min(len(content_types) / 2, 1.0)  # 假设最多2种类型
        
        # 计算覆盖度分数（基于高亮匹配）
        highlighted_items = sum(1 for item in items if item.get("highlights"))
        coverage_score = highlighted_items / len(items) if items else 0
        
        # 综合质量分数
        quality_score = (relevance_score * 0.5 + diversity_score * 0.3 + coverage_score * 0.2)
        
        return {
            "quality_score": round(quality_score, 3),
            "relevance_score": round(relevance_score, 3),
            "diversity_score": round(diversity_score, 3),
            "coverage_score": round(coverage_score, 3),
            "total_items": len(items),
            "highlighted_items": highlighted_items
        }
    
    @staticmethod
    def extract_search_keywords(query: str, max_keywords: int = 10) -> List[str]:
        """
        从查询中提取关键词
        
        Args:
            query: 搜索查询
            max_keywords: 最大关键词数量
            
        Returns:
            List[str]: 关键词列表
        """
        if not query:
            return []
        
        # 分词（简单按空格和标点分割）
        words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', query.lower())
        
        # 过滤停用词和短词
        stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', 
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这',
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'
        }
        keywords = [word for word in words if len(word) > 1 and word not in stop_words]
        
        # 统计词频并返回最常见的关键词
        from collections import Counter
        word_counts = Counter(keywords)
        
        return [word for word, count in word_counts.most_common(max_keywords)]
