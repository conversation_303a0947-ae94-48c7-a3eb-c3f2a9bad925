"""
通用搜索类型定义

定义搜索相关的数据类型、枚举和接口。
"""
from enum import Enum
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from abc import ABC, abstractmethod


class ContentType(Enum):
    """内容类型枚举"""
    ARTICLE = "article"
    PROJECT = "project"
    ALL = "all"


class SortOrder(Enum):
    """排序方向枚举"""
    ASC = "asc"
    DESC = "desc"


@dataclass
class SearchRequest:
    """搜索请求数据类"""
    query: Optional[str] = None
    content_types: List[ContentType] = None
    filters: Optional[Dict[str, Any]] = None
    sort_by: Optional[str] = None
    sort_order: SortOrder = SortOrder.DESC
    page: int = 1
    page_size: int = 10
    highlight: bool = True
    
    def __post_init__(self):
        if self.content_types is None:
            self.content_types = [ContentType.ALL]


@dataclass
class ContentItem:
    """内容项数据类"""
    id: str
    content_type: ContentType
    title: str
    content: str
    score: float
    highlights: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "content_type": self.content_type.value,
            "title": self.title,
            "content": self.content,
            "score": self.score,
            "highlights": self.highlights,
            "metadata": self.metadata or {}
        }


@dataclass
class SearchResult:
    """搜索结果数据类"""
    items: List[ContentItem]
    total: int
    page: int
    page_size: int
    total_pages: int
    search_time: int
    aggregations: Dict[str, Any]
    query: str
    content_types: List[ContentType]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "items": [item.to_dict() for item in self.items],
            "total": self.total,
            "page": self.page,
            "page_size": self.page_size,
            "total_pages": self.total_pages,
            "search_time": self.search_time,
            "aggregations": self.aggregations,
            "query": self.query,
            "content_types": [ct.value for ct in self.content_types]
        }


class ContentTypeAdapter(ABC):
    """内容类型适配器抽象基类"""
    
    @property
    @abstractmethod
    def content_type(self) -> ContentType:
        """返回内容类型"""
        pass
    
    @property
    @abstractmethod
    def index_name(self) -> str:
        """返回索引名称"""
        pass
    
    @abstractmethod
    async def search(self, request: SearchRequest) -> Dict[str, Any]:
        """执行搜索"""
        pass
    
    @abstractmethod
    def build_query(self, request: SearchRequest) -> Dict[str, Any]:
        """构建查询"""
        pass
    
    @abstractmethod
    def process_results(self, es_result: Dict[str, Any]) -> List[ContentItem]:
        """处理搜索结果"""
        pass
    
    @abstractmethod
    def get_sort_fields(self) -> List[str]:
        """获取支持的排序字段"""
        pass
    
    @abstractmethod
    def validate_filters(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """验证过滤条件"""
        pass
