# ======================== Elasticsearch Configuration =========================

# ---------------------------------- Cluster -----------------------------------
# 集群名称设置
cluster.name: "gugu-apex-cluster"

# ------------------------------------ Node ------------------------------------
# 节点名称
node.name: "gugu-apex-node-1"

# 添加节点的自定义属性
# node.attr.rack: r1

# 节点角色设置（单节点包含所有角色）
node.roles: [master, data, data_content, data_hot, data_warm, data_cold, data_frozen, ingest, ml, remote_cluster_client, transform]

# ----------------------------------- Paths ------------------------------------
# 数据存储路径
path.data: /usr/share/elasticsearch/data

# 日志存储路径
path.logs: /usr/share/elasticsearch/logs

# ----------------------------------- Memory -----------------------------------
# 锁定进程地址空间到RAM中，防止任何Elasticsearch堆内存被交换出去
bootstrap.memory_lock: true

# ---------------------------------- Network -----------------------------------
# 绑定到所有可用的IP地址
network.host: 0.0.0.0

# HTTP端口设置
http.port: 9200

# Transport端口设置
transport.port: 9300

# --------------------------------- Discovery ----------------------------------
# 单节点发现类型
discovery.type: single-node

# 在单节点模式下，以下设置会被忽略
# discovery.seed_hosts: ["host1", "host2"]
# cluster.initial_master_nodes: ["node-1", "node-2"]

# ---------------------------------- Gateway -----------------------------------
# 在整个集群重启后，阻止数据恢复，直到N个节点启动
# gateway.recover_after_nodes: 1

# ---------------------------------- Various -----------------------------------
# 要求显式命名来删除索引
# action.destructive_requires_name: true

# 自动创建索引设置
action.auto_create_index: projects-*,articles-*,+apm-*-metric,+apm-*-span,+apm-*-transaction,+apm-*-error,+apm-*-profile,+apm-*-sourcemap,+apm-*-onboarding,*

# ---------------------------------- Security ----------------------------------
# 禁用X-Pack安全功能（开发环境）
xpack.security.enabled: false

# 如果启用安全功能，取消注释以下设置
# xpack.security.enrollment.enabled: true
# xpack.security.http.ssl.enabled: false
# xpack.security.transport.ssl.enabled: false

# ---------------------------------- Monitoring --------------------------------
# 启用监控数据收集
xpack.monitoring.collection.enabled: true

# ---------------------------------- Watcher -----------------------------------
# 禁用Watcher（在开发环境中通常不需要）
xpack.watcher.enabled: false

# ---------------------------------- Machine Learning --------------------------
# 机器学习设置
xpack.ml.enabled: true

# ---------------------------------- Graph --------------------------------------
# 图形分析功能
xpack.graph.enabled: true

# ---------------------------------- License -----------------------------------
# 许可证类型
xpack.license.self_generated.type: basic

# ---------------------------------- HTTP --------------------------------------
# 启用HTTP压缩
http.compression: true

# HTTP最大内容长度
http.max_content_length: 100mb

# CORS设置（如果需要跨域访问）
http.cors.enabled: true
http.cors.allow-origin: "*"
http.cors.allow-methods: OPTIONS, HEAD, GET, POST, PUT, DELETE
http.cors.allow-headers: X-Requested-With, X-Auth-Token, Content-Type, Content-Length, Authorization

# ---------------------------------- Logging -----------------------------------
# 日志级别设置
logger.level: INFO

# 根日志级别
logger.org.elasticsearch: INFO

# ---------------------------------- Thread Pool -------------------------------
# 搜索线程池设置
thread_pool.search.size: 4
thread_pool.search.queue_size: 1000

# 写入线程池设置
thread_pool.write.size: 4
thread_pool.write.queue_size: 1000

# ---------------------------------- Circuit Breaker --------------------------
# 断路器设置
indices.breaker.total.limit: 70%
indices.breaker.fielddata.limit: 40%
indices.breaker.request.limit: 40%