<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      v-show="showSearch"
      :inline="true"
      @submit.native.prevent
      label-width="80px">
      <el-form-item label="文章标题" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="文章标题"
          clearable
          @keyup.enter="handleQuery"
          style="width: 240px" />
      </el-form-item>
      <el-form-item label="文章状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="文章状态"
          clearable
          style="width: 240px">
          <el-option
            v-for="dict in article_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="Search"
          @click="handleQuery"
          v-hasPermi="['broadcastmanage:analysis_queue:list']">
          搜索
        </el-button>
        <el-button
          icon="Refresh"
          @click="resetQuery"
          v-hasPermi="['broadcastmanage:analysis_queue:list']">
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['broadcastmanage:analysis_queue:add']">
          AI创作
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList()"></right-toolbar>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-radio-group v-model="category" @change="handleCategoryChange">
          <el-radio-button label="主站" value="main" />
          <el-radio-button label="服务号" value="oa" />
          <el-radio-button label="公众号" value="mp" />
        </el-radio-group>
      </el-col>
    </el-row>

    <!-- 表格数据 -->
    <el-table v-if="category === 'main'" v-loading="loading" :data="list">
      <el-table-column type="index" width="50" />
      <el-table-column
        label="文章标题"
        prop="title"
        :show-overflow-tooltip="true"
        min-width="150" />
      <!-- <el-table-column
        label="仓库地址"
        prop="repository_url"
        :show-overflow-tooltip="true"
        min-width="200" /> -->
      <el-table-column label="文章状态" align="center" min-width="100">
        <template #default="scope">
          <div
            style="display: flex; align-items: center; justify-content: center">
            <span>
              {{ getDictLabel(article_status, scope.row.status) }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="创建者"
        align="center"
        prop="created_by"
        :show-overflow-tooltip="true"
        min-width="100" />
      <el-table-column label="创建时间" align="center" min-width="160">
        <template #default="scope">
          <span>
            {{ dayjs(scope.row.created_at).format("YYYY-MM-DD HH:mm:ss") }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="260">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            class="optButton"
            @click="handleUpdateCard(scope.row)"
            v-hasPermi="['broadcastmanage:content:edit']">
            编辑卡片
          </el-button>
          <el-button
            link
            type="primary"
            icon="Edit"
            class="optButton"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['broadcastmanage:content:edit']">
            编辑内容
          </el-button>
          <el-button
            link
            type="primary"
            icon="ChatLineSquare"
            class="optButton"
            @click="handleUpdateComment(scope.row)"
            v-hasPermi="['broadcastmanage:content:edit']">
            评论管理
          </el-button>
          <el-button
            link
            type="primary"
            icon="Paperclip"
            class="optButton"
            @click="handleUpdateRelated(scope.row)"
            v-hasPermi="['broadcastmanage:content:edit']">
            关联项目
          </el-button>
          <el-button
            link
            type="danger"
            icon="Delete"
            class="optButton"
            @click="handleDelete(scope.row)"
            v-hasPermi="['broadcastmanage:content:edit']">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-table v-if="category === 'oa'" v-loading="loading" :data="list_oa">
      <el-table-column type="index" width="50" />
      <el-table-column
        label="文章标题"
        prop="title"
        :show-overflow-tooltip="true"
        min-width="150" />
      <el-table-column label="文章状态" align="center" min-width="100">
        <template #default="scope">
          <div
            style="display: flex; align-items: center; justify-content: center">
            <span>
              {{ getDictLabel(article_status, scope.row.type) }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="创建者"
        align="center"
        prop="author"
        :show-overflow-tooltip="true"
        min-width="100" />
      <el-table-column label="更新时间" align="center" min-width="160">
        <template #default="scope">
          <span>
            {{ dayjs(scope.row.update_time).format("YYYY-MM-DD HH:mm:ss") }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            class="optButton"
            @click="handleView(scope.row)"
            v-hasPermi="['broadcastmanage:analysis_queue:fast']">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-table v-if="category === 'mp'" v-loading="loading" :data="list_mp">
      <el-table-column type="index" width="50" />
      <el-table-column
        label="文章标题"
        prop="title"
        :show-overflow-tooltip="true"
        min-width="150" />
      <el-table-column label="文章状态" align="center" min-width="100">
        <template #default="scope">
          <div
            style="display: flex; align-items: center; justify-content: center">
            <span>
              {{ getDictLabel(article_status, scope.row.type) }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="创建者"
        align="center"
        prop="author"
        :show-overflow-tooltip="true"
        min-width="100" />
      <el-table-column label="更新时间" align="center" min-width="160">
        <template #default="scope">
          <span>
            {{ dayjs(scope.row.update_time).format("YYYY-MM-DD HH:mm:ss") }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            class="optButton"
            @click="handleView(scope.row)"
            v-hasPermi="['broadcastmanage:analysis_queue:fast']">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList" />
    <!-- 新增或修改项目配置对话框 -->
    <el-dialog
      title="编辑卡片"
      v-model="openEditCard"
      width="700px"
      append-to-body
      align-center>
      <el-form
        ref="editCardRef"
        :model="formEditCard"
        :rules="rulesEditCard"
        label-width="120px">
        <el-form-item label="文章标题" prop="title">
          <el-input v-model="formEditCard.title" placeholder="请输入文章标题" />
        </el-form-item>
        <el-form-item label="封面图" prop="cover_image">
          <template #label>
            <span>
              <el-tooltip content="封面图最多一张" placement="top">
                <el-icon><question-filled /></el-icon>
              </el-tooltip>
              封面图
            </span>
          </template>
          <div class="image-select">
            <div class="image-select-item">
              <img :src="formEditCard.cover_image" />
            </div>
            <el-upload
              class="avatar-uploader"
              accept="image/png, image/jpeg"
              name="image"
              :action="uploadImageUrl"
              :show-file-list="false"
              :on-success="handleAvatarSuccess2"
              :before-upload="beforeAvatarUpload2">
              <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
            </el-upload>
          </div>
        </el-form-item>
        <el-form-item label="文章概述" prop="summary">
          <el-input
            v-model="formEditCard.summary"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 5 }"
            placeholder="请输入文章概述"></el-input>
        </el-form-item>
        <el-form-item label="文章状态" prop="status">
          <el-radio-group v-model="formEditCard.status">
            <el-radio
              v-for="dict in article_status"
              :key="dict.value"
              :value="dict.value">
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="文章标签" prop="tagsString">
          <template #label>
            <span>
              <el-tooltip content="多个标签使用英文逗号分隔" placement="top">
                <el-icon><question-filled /></el-icon>
              </el-tooltip>
              文章标签
            </span>
          </template>
          <el-input
            v-model="formEditCard.tagsString"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 5 }"
            placeholder="请输入文章标签，多个标签使用英文逗号分隔"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFormEditCard">
            确 定
          </el-button>
          <el-button @click="openEditCard = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 关联项目 -->
    <el-dialog
      title="关联项目"
      v-model="openRelated"
      width="90vw"
      append-to-body
      align-center>
      <el-button
        type="primary"
        @click="handleAddRelated"
        style="margin-bottom: 10px">
        添加
      </el-button>
      <el-table
        height="calc(100vh - 120px - 48px - 48px)"
        :data="currentRelated">
        <el-table-column type="index" width="50" />
        <el-table-column
          label="标题"
          prop="title"
          :show-overflow-tooltip="true"
          min-width="150" />
        <el-table-column
          label="链接"
          :show-overflow-tooltip="true"
          min-width="100">
          <template #default="scope">
            <a class="alink" :href="scope.row.url" target="_blank">
              {{ scope.row.url }}
            </a>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100">
          <template #default="scope">
            <el-tooltip content="编辑" placement="top">
              <el-button
                link
                type="primary"
                icon="Edit"
                @click="updateRelated(scope.row, scope.$index)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button
                link
                type="danger"
                icon="Delete"
                @click="deleteRelated(scope.row, scope.$index)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="saveRelated">保 存</el-button>
          <el-button @click="openRelated = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      title="添加关联项目"
      v-model="openAddRelated"
      width="500px"
      append-to-body
      align-center>
      <el-form
        ref="relatedRef"
        :model="formRelated"
        :rules="rulesRelated"
        label-width="60px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="formRelated.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="链接" prop="url">
          <el-input v-model="formRelated.url" placeholder="请输入链接" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="addRelated">确 定</el-button>
          <el-button @click="openAddRelated = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 评论管理 -->
    <el-dialog
      :title="'评论管理--' + currentRow.title"
      v-model="openComment"
      width="90vw"
      append-to-body
      align-center>
      <div style="display: flex; align-items: center; margin-bottom: 10px">
        <el-input
          v-model="commentQuery"
          placeholder="关键词搜索"
          clearable
          @keyup.enter="getCommentList"
          style="width: 240px" />
        <el-button
          type="primary"
          icon="Search"
          @click="getCommentList"
          style="margin-left: 10px">
          搜索
        </el-button>
      </div>
      <el-table
        height="calc(100vh - 120px - 48px)"
        v-loading="commentListLoading"
        :data="commentList"
        border
        row-key="id"
        :indent="10"
        :tree-props="{ children: 'replies', hasChildren: 'reply_count' }">
        <el-table-column type="index" width="50" />
        <el-table-column
          label="评论人"
          prop="user_name"
          min-width="160"
          :show-overflow-tooltip="true" />
        <el-table-column
          label="评论内容"
          prop="content"
          min-width="160"
          :show-overflow-tooltip="true" />
        <el-table-column
          label="被评论人"
          prop="parent_user_name"
          min-width="160"
          :show-overflow-tooltip="true" />
        <el-table-column label="更新时间" align="center" min-width="160">
          <template #default="scope">
            <span>
              {{ dayjs(scope.row.updated_at).format("YYYY-MM-DD HH:mm:ss") }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100">
          <template #default="scope">
            <el-tooltip content="回复" placement="top">
              <el-button
                link
                type="primary"
                icon="ChatDotSquare"
                @click="handleReplyComment(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button
                link
                type="danger"
                icon="Delete"
                @click="handleDeleteComment(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <!-- <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="saveRelated">保 存</el-button>
          <el-button @click="openRelated = false">取 消</el-button>
        </div>
      </template> -->
    </el-dialog>
    <el-dialog
      title="回复评论"
      v-model="openReplyComment"
      width="500px"
      append-to-body
      align-center>
      <el-form
        ref="commentRef"
        :model="formComment"
        :rules="rulesComment"
        label-width="60px">
        <el-form-item label="内容" prop="content">
          <el-input
            v-model="formComment.content"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 5 }"
            placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="replyComment">确 定</el-button>
          <el-button @click="openReplyComment = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- AI创作 -->
    <AiWriteDialog
      v-model="open"
      v-if="open"
      :article="currentRow"
      @refresh="getList"></AiWriteDialog>
  </div>
</template>

<script setup name="BroadcastManageArticle">
import dayjs from "dayjs";
import {
  getArticleList,
  getWechatArticleList,
  updateArticle,
  deleteArticle,
  commentTree,
  commentCreat,
  commentAudit,
} from "@/api/article.js";
import { getDictLabel, article_status } from "@/constants/common";
import AiWriteDialog from "./AiWriteDialog.vue";

import auth from "@/plugins/auth";

const { proxy } = getCurrentInstance();
let list = ref([]);
let loading = ref(true);
let showSearch = ref(true);
let total = ref(0);
let list_oa = ref([]);
let list_mp = ref([]);

let open = ref(false);
let openRelated = ref(false);
let openComment = ref(false);
let openAddRelated = ref(false);
let openEditCard = ref(false);
let formEditCard = ref({});
let rulesEditCard = {
  title: [
    { required: true, message: "文章标题不能为空", trigger: "blur" },
    { min: 2, max: 50, message: "文章标题长度为2-50", trigger: "blur" },
  ],
  summary: [{ required: true, message: "文章概述不能为空", trigger: "blur" }],
  tagsString: [
    { required: true, message: "文章标签不能为空", trigger: "blur" },
  ],
};
const uploadImageUrl =
  import.meta.env.VITE_APP_BASE_API + "/github/upload/image";
let addRelatedType = ref("add"); // add edit
let addRelatedIndex = ref(-1);
let formRelated = ref({
  title: "",
  url: "",
});
let rulesRelated = {
  title: [{ required: true, message: "标题不能为空", trigger: "blur" }],
  url: [{ required: true, message: "链接不能为空", trigger: "blur" }],
};

let openReplyComment = ref(false);
let formComment = ref({
  content: "",
});
let rulesComment = {
  content: [{ required: true, message: "内容不能为空", trigger: "blur" }],
};
let commentListLoading = ref(false);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    status: "published",
  },
});
let category = ref("main");
let currentRelated = ref([]);
let currentRow = ref({});
let commentQuery = ref("");
const commentList = ref([]);

const { queryParams } = toRefs(data);
const handleCategoryChange = (item) => {
  queryParams.value.pageSize = 10;
  queryParams.value.pageNum = 1;

  getList();
};

const handleView = (item) => {
  if (category.value === "main") {
  } else {
    window.open(item.url, "_blank");
  }
};
/** 查询列表 */
function getList() {
  loading.value = true;
  if (category.value === "main") {
    let params = {
      size: queryParams.value.pageSize,
      page: queryParams.value.pageNum,
      status: queryParams.value.status,
      search: queryParams.value.name,
    };
    getArticleList(params).then((res) => {
      if (res.code === 200) {
        list.value = res.data.articles || [];
        total.value = res.data.total;
        loading.value = false;
      }
    });
  } else if (category.value === "oa") {
    let params = {
      page_size: queryParams.value.pageSize,
      page: queryParams.value.pageNum,
      article_type: queryParams.value.status,
      account_type: "oa",
      name: queryParams.value.name,
    };
    getWechatArticleList(params).then((res) => {
      if (res.code === 200) {
        list_oa.value = res.data.articles || [];
        total.value = res.data.pagination?.total;
        loading.value = false;
      }
    });
  } else if (category.value === "mp") {
    let params = {
      page_size: queryParams.value.pageSize,
      page: queryParams.value.pageNum,
      article_type: queryParams.value.status,
      account_type: "mp",
      name: queryParams.value.name,
    };
    getWechatArticleList(params).then((res) => {
      if (res.code === 200) {
        list_mp.value = res.data.articles || [];
        total.value = res.data.pagination?.total;
        loading.value = false;
      }
    });
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
const getCommentList = () => {
  commentListLoading.value = true;
  commentTree({
    article_id: currentRow.value.id,
    page: 1,
    page_size: 99,
    query: commentQuery.value,
  })
    .then((res) => {
      if (res.code === 200) {
        commentList.value = res.data.comments || [];
      }
    })
    .finally(() => {
      commentListLoading.value = false;
    });
};
const handleUpdateCard = (row) => {
  currentRow.value = row;
  openEditCard.value = true;
  formEditCard.value = {
    article_id: row.id,
    title: row.title,
    summary: row.summary,
    tagsString: row.tags.join(",") || "",
    cover_image: row.cover_image,
    status: row.status,
  };
};
const handleUpdate = (row) => {
  currentRow.value = row;
  open.value = true;
};
const handleDelete = (row) => {
  proxy.$modal
    .confirm("确认删除?")
    .then(function () {
      return deleteArticle({ id: row.id });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("操作成功");
    })
    .catch(() => {});
};
const handleUpdateComment = (row) => {
  currentRow.value = row;
  openComment.value = true;
  getCommentList();
};
const handleUpdateRelated = (row) => {
  currentRow.value = row;
  try {
    currentRelated.value =
      row.related_projects?.map((item) => JSON.parse(item)) || [];
  } catch (error) {
    currentRelated.value = [];
  }
  openRelated.value = true;
};
const handleAddRelated = () => {
  addRelatedType.value = "add";
  openAddRelated.value = true;
};
const addRelated = () => {
  proxy.$refs["relatedRef"].validate((valid) => {
    if (valid) {
      if (addRelatedType.value === "add") {
        currentRelated.value.push({
          title: formRelated.value.title,
          url: formRelated.value.url,
        });
      } else {
        currentRelated.value[addRelatedIndex.value] = {
          title: formRelated.value.title,
          url: formRelated.value.url,
        };
      }
      openAddRelated.value = false;
    }
  });
};
const updateRelated = (row, index) => {
  // console.log("inedx>>", index);
  addRelatedIndex.value = index;
  addRelatedType.value = "edit";
  formRelated.value = JSON.parse(JSON.stringify(row));
  openAddRelated.value = true;
};
const deleteRelated = (item, index) => {
  currentRelated.value.splice(index, 1);
};
const saveRelated = () => {
  let str = currentRelated.value.map((e) => JSON.stringify(e));
  updateArticle({
    article_id: currentRow.value.id,
    related_projects: str,
  }).then((res) => {
    if (res.code === 200) {
      proxy.$modal.msgSuccess("操作成功");
      openRelated.value = false;
      getList();
    }
  });
};
const handleReplyComment = (row) => {
  formComment.value = {
    content: "",
    parent_id: row.id,
    project_id: row.article_id,
  };
  openReplyComment.value = true;
};
const replyComment = () => {
  proxy.$refs["commentRef"].validate((valid) => {
    if (valid) {
      commentCreat(formComment.value).then((res) => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess("回复成功");
          openReplyComment.value = false;
          getCommentList();
        }
      });
    }
  });
};
const handleDeleteComment = (row) => {
  proxy.$modal
    .confirm("删除操作会将子评论一并删除，确认删除?")
    .then(function () {
      return commentAudit({ comment_id: row.id, status: "rejected" });
    })
    .then(() => {
      getCommentList();
      proxy.$modal.msgSuccess("操作成功");
    })
    .catch(() => {});
};
const beforeAvatarUpload2 = (rawFile) => {
  if (!["image/jpeg", "image/png", "image/gif"].includes(rawFile.type)) {
    proxy.$modal.msgWarning("只能上传jpg或png或gif格式的图片!");
    return false;
  } else if (rawFile.size / 1024 / 1024 > 2) {
    proxy.$modal.msgWarning("图片不能超过2MB!");
    return false;
  }
  return true;
};
const handleAvatarSuccess2 = (response, uploadFile) => {
  formEditCard.value.cover_image = response?.data?.files?.[0]?.url;
};
const submitFormEditCard = () => {
  proxy.$refs["editCardRef"].validate((valid) => {
    if (valid) {
      proxy.$modal.loading();
      updateArticle({
        ...formEditCard.value,
        tags: formEditCard.value.tagsString.split(","),
        tagsString: undefined,
      })
        .then((response) => {
          proxy.$modal.msgSuccess("编辑成功");
          openEditCard.value = false;
          getList();
        })
        .finally(() => {
          proxy.$modal.closeLoading();
        });
    }
  });
};
/** 添加项目 */
const handleAdd = () => {
  currentRow.value = {};
  open.value = true;
};

getList();
</script>
<style scoped lang="scss">
.image-select {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  .image-select-item {
    width: 100px;
    height: 100px;
    border: solid 1px #ccc;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    position: relative;
    &:hover {
      border-color: #3498db;
      box-shadow: 0 0 4px 0 #3498db;
      .image-select-item-delete {
        visibility: visible;
      }
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    &.selected {
      border-color: #3498db;
      box-shadow: 0 0 4px 0 #3498db;
    }
    .image-select-item-icon {
      position: absolute;
      right: 4px;
      top: 4px;
      color: #3498db;
      filter: drop-shadow(0 0 2px #fff);
    }
    &.icon-select-item {
      width: 68px;
      height: 68px;
      .image-select-item-icon {
        position: absolute;
        right: 2px;
        top: 2px;
        color: #3498db;
      }
    }
    .image-select-item-delete {
      position: absolute;
      right: 4px;
      bottom: 4px;
      color: #fc5846;
      cursor: pointer;
      filter: drop-shadow(0 0 2px #fff);
      visibility: hidden;
      &:hover {
        color: #ce3423;
      }
    }
  }
}
.redtext {
  color: #f56c6c;
}
.greentext {
  color: #67c23a;
}
.alink {
  &:hover {
    color: #409eff;
  }
  &:visited {
    color: #409eff;
  }
}
:deep(.optButton.el-button > span) {
  margin-left: 0px;
}
:deep(.optButton.rotate180 > i) {
  transform: rotate(180deg);
}

:deep(.avatar-uploader .avatar) {
  width: 100px;
  height: 100px;
  display: block;
  object-fit: contain;
}
:deep(.avatar-uploader .el-upload) {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: 0.2s;
}

:deep(.avatar-uploader .el-upload:hover) {
  border-color: #409eff;
}

:deep(.el-icon.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  text-align: center;
}
</style>
