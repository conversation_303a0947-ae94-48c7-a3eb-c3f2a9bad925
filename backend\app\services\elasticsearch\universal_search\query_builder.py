"""
通用查询构建器

提供统一的Elasticsearch查询构建功能，支持多种内容类型的查询策略。
"""
import logging
from typing import Dict, Any, List, Optional
from .types import SearchRequest, ContentType, SortOrder

logger = logging.getLogger(__name__)


class UnifiedQueryBuilder:
    """统一查询构建器"""
    
    def __init__(self):
        """初始化查询构建器"""
        pass
    
    def build_multi_index_query(self, 
                               request: SearchRequest,
                               index_queries: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        构建多索引查询
        
        Args:
            request: 搜索请求
            index_queries: 各索引的查询配置 {index_name: query_body}
            
        Returns:
            Dict[str, Any]: 多索引查询体
        """
        if not index_queries:
            return {"query": {"match_all": {}}}
        
        # 如果只有一个索引，直接返回该索引的查询
        if len(index_queries) == 1:
            return list(index_queries.values())[0]
        
        # 构建多索引查询
        indices = list(index_queries.keys())
        
        # 合并查询条件
        should_conditions = []
        for index_name, query_body in index_queries.items():
            # 为每个索引的查询添加索引过滤
            index_query = {
                "bool": {
                    "must": [query_body.get("query", {"match_all": {}})],
                    "filter": [{"term": {"_index": index_name}}]
                }
            }
            should_conditions.append(index_query)
        
        return {
            "query": {
                "bool": {
                    "should": should_conditions,
                    "minimum_should_match": 1
                }
            }
        }
    
    def build_base_text_query(self, 
                             query: str,
                             fields: List[str],
                             boost_config: Dict[str, float] = None) -> Dict[str, Any]:
        """
        构建基础文本查询
        
        Args:
            query: 搜索关键词
            fields: 搜索字段列表
            boost_config: 字段权重配置
            
        Returns:
            Dict[str, Any]: 文本查询
        """
        if not query or not query.strip():
            return {"match_all": {}}
        
        query = query.strip()
        boost_config = boost_config or {}
        
        # 检测是否包含中文字符
        has_chinese = any('\u4e00' <= char <= '\u9fff' for char in query)
        is_short_query = len(query) <= 3
        
        should_conditions = []
        
        for field in fields:
            base_boost = boost_config.get(field, 1.0)
            
            # 精确匹配（最高权重）
            should_conditions.append({
                "match_phrase": {
                    field: {
                        "query": query,
                        "boost": base_boost * 3.0
                    }
                }
            })
            
            # 智能匹配
            field_suffix = ".ik" if has_chinese else ".english"
            analyzed_field = f"{field}{field_suffix}"
            
            should_conditions.append({
                "match": {
                    analyzed_field: {
                        "query": query,
                        "boost": base_boost * 2.0,
                        "minimum_should_match": "90%" if is_short_query else "80%",
                        "operator": "and" if is_short_query else "or",
                        **({
                            "fuzziness": "1" if len(query) <= 6 else "AUTO"
                        } if not has_chinese and len(query) >= 2 else {})
                    }
                }
            })
            
            # 前缀匹配
            should_conditions.append({
                "match_phrase_prefix": {
                    analyzed_field: {
                        "query": query,
                        "boost": base_boost * 1.5
                    }
                }
            })
            
            # N-gram匹配（仅对较长查询词启用）
            if (not has_chinese and len(query) >= 4) or (has_chinese and len(query) >= 3):
                should_conditions.append({
                    "match": {
                        f"{field}.ngram": {
                            "query": query,
                            "boost": base_boost * 0.5
                        }
                    }
                })
        
        return {
            "bool": {
                "should": should_conditions,
                "minimum_should_match": 1
            }
        }
    
    def build_filter_conditions(self, 
                               filters: Dict[str, Any],
                               field_mappings: Dict[str, str] = None) -> List[Dict[str, Any]]:
        """
        构建过滤条件
        
        Args:
            filters: 过滤条件
            field_mappings: 字段映射 {filter_key: es_field}
            
        Returns:
            List[Dict[str, Any]]: 过滤条件列表
        """
        if not filters:
            return []
        
        field_mappings = field_mappings or {}
        filter_conditions = []
        
        for field, value in filters.items():
            # 获取实际的ES字段名
            es_field = field_mappings.get(field, field)
            
            if isinstance(value, list) and value:
                filter_conditions.append({"terms": {es_field: value}})
            elif isinstance(value, dict):
                filter_conditions.append({"range": {es_field: value}})
            elif isinstance(value, bool):
                filter_conditions.append({"term": {es_field: value}})
            elif value is not None:
                filter_conditions.append({"term": {es_field: value}})
        
        return filter_conditions
    
    def build_sort_conditions(self, 
                             sort_by: str = None,
                             sort_order: SortOrder = SortOrder.DESC,
                             valid_fields: List[str] = None) -> List[Dict[str, Any]]:
        """
        构建排序条件
        
        Args:
            sort_by: 排序字段
            sort_order: 排序方向
            valid_fields: 有效排序字段列表
            
        Returns:
            List[Dict[str, Any]]: 排序条件
        """
        sort_conditions = []
        
        # 验证排序字段
        if sort_by and valid_fields and sort_by not in valid_fields:
            sort_by = "_score"
        
        # 默认使用分数排序
        if not sort_by:
            sort_by = "_score"
        
        # 构建排序条件
        if sort_by == "_score":
            sort_conditions.append({"_score": {"order": sort_order.value}})
        elif sort_by in ["title", "name"]:
            sort_conditions.append({f"{sort_by}.keyword": {"order": sort_order.value}})
        else:
            sort_conditions.append({sort_by: {"order": sort_order.value}})
        
        # 添加默认的二级排序
        if sort_by != "updated_at":
            sort_conditions.append({"updated_at": {"order": "desc"}})
        
        return sort_conditions
    
    def build_highlight_config(self, 
                              fields: List[str],
                              query: str = None) -> Dict[str, Any]:
        """
        构建高亮配置
        
        Args:
            fields: 需要高亮的字段列表
            query: 搜索查询词
            
        Returns:
            Dict[str, Any]: 高亮配置
        """
        if not fields:
            return {}
        
        # 检测查询语言类型
        has_chinese = False
        if query:
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in query)
        
        highlight_fields = {}
        
        for field in fields:
            # 根据语言选择合适的字段
            field_suffix = ".ik" if has_chinese else ".english"
            analyzed_field = f"{field}{field_suffix}"
            
            highlight_fields[analyzed_field] = {
                "number_of_fragments": 3,
                "fragment_size": 150,
                "boundary_scanner": "sentence",
                "boundary_max_scan": 100,
                "require_field_match": True,
                "no_match_size": 0,
                "order": "score"
            }
        
        return {
            "fields": highlight_fields,
            "pre_tags": ["<mark>"],
            "post_tags": ["</mark>"],
            "require_field_match": True,
            "order": "score",
            "number_of_fragments": 3,
            "fragment_size": 150,
            "boundary_scanner": "sentence",
            "boundary_max_scan": 100,
            "no_match_size": 0,
            "phrase_limit": 256,
            "max_analyzed_offset": 1000000
        }
