#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/7/14 13:08
# @File    : system_log.py
# @Description: 
"""
"""
GitHub系统日志数据库模型
"""
from datetime import datetime
from sqlalchemy import Column, String, Text, Boolean, ForeignKey
from sqlalchemy.orm import relationship

from app.models.model_base import ModelBase


class SystemLogModel(ModelBase):

    """系统日志表"""
    __tablename__ = "system_logs"

    # 消息内容
    message = Column(Text, nullable=False, comment='系统消息内容')

    # 已读状态
    is_read = Column(Boolean, nullable=False, default=False, comment='是否已读')

    # 消息类型（可选，用于分类）
    message_type = Column(String(50), nullable=True, default='system', comment='消息类型')

    # 消息关联项目
    project_id = Column(String(48), nullable=True, default=None, comment='关联项目类型')


    def __repr__(self):
        return f"<GitHubSystemLog {self.id} - {self.message[:50]}...>"