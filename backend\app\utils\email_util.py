#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/01/10
# @File    : email_util.py
# @Description: 邮件通知工具类
"""

import structlog
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime

from app.core.config import settings
from app.models.github import GitHubProjectModel
from app.models.rbac.user import UserModel
from app.services.rbac.email import EmailService

logger = structlog.get_logger(__name__)


class EmailNotificationUtil:
    """邮件通知工具类"""

    @staticmethod
    async def send_project_analysis_notification(
            session: AsyncSession,
            project_id: str,
            success: bool,
            email_service: Optional[EmailService] = None
    ) -> bool:
        """
        根据项目状态向用户发送邮件通知

        Args:
            session: 数据库会话
            project_id: 项目ID
            success: 项目处理是否成功
            email_service: 邮件服务实例，如果不提供会创建新实例

        Returns:
            bool: 邮件发送是否成功
        """
        try:
            if not settings.distributed.is_publisher:
                logger.error("当前不是发布机模式，无法发送邮件提醒")
                return False
            # 获取项目信息
            project = await session.get(GitHubProjectModel, project_id)
            if not project:
                logger.warning("项目不存在", project_id=project_id)
                return False

            # 获取用户信息
            user_id = project.created_by
            user = await session.get(UserModel, user_id)
            if not user:
                logger.warning("用户不存在", user_id=user_id, project_id=project_id)
                return False

            # 检查用户是否有邮箱
            if not user.email:
                logger.warning("用户未设置邮箱", user_id=user_id, project_id=project_id)
                return False

            logger.info("准备发送项目分析通知邮件",
                        project_id=project_id,
                        user_id=user_id,
                        user_email=user.email,
                        project_name=project.name,
                        success=success)

            # 如果没有传入邮件服务实例，创建一个新的
            if email_service is None:
                email_service = EmailService()

            # 发送邮件通知
            subject = "guguke - 项目分析成功  " + str(datetime.now())
            content = f"项目名称：{project.name}，分析成功请在网站查看分析结果。"
            logger.info("发送分析结果邮件 :: " + str(user.email) + str(project.name))
            send_result = email_service.send_email(
                to_email=user.email,
                subject=subject,
                content=content
            )

            if send_result:
                logger.info(
                    "项目分析通知邮件发送成功",
                    project_id=project_id,
                    user_id=user_id,
                    user_email=user.email,
                    success=success
                )
            else:
                logger.error(
                    "项目分析通知邮件发送失败",
                    project_id=project_id,
                    user_id=user_id,
                    user_email=user.email,
                    success=success
                )

            return send_result

        except Exception as e:
            logger.error(
                "发送项目分析通知邮件异常",
                project_id=project_id,
                success=success,
                error=str(e),
                exc_info=True
            )
            return False