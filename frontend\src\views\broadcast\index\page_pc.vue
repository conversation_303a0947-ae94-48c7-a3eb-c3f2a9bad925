<template>
  <div class="app-container home broadcasthome" ref="homeRef">
    <vue-particles
      id="tsparticles"
      :options="particlesOptions"
      @particles-loaded="particlesLoaded" />
    <div class="home-title">新鲜资讯动态捕捉，gugu播报</div>
    <div class="search-wrap-parent">
      <div class="search-wrap">
        <el-autocomplete
          ref="autocompleteRef"
          v-if="showAutocomplete"
          @keyup.enter="doSearch"
          v-model="searchKey"
          style="width: 100%; height: 50px; border-radius: 22px"
          clearable
          :fetch-suggestions="fetchSuggestions"
          @select="handleSelect"
          append-to=".search-wrap"
          popper-class="search-wrap-autocomplete"
          placeholder="搜索关键词或GitHub链接">
          <template #append>
            <el-tooltip content="添加项目分析">
              <div class="search-icon-wrap" @click="doAddProject">
                <img class="search-icon" src="@/assets/images/add.png" />
              </div>
            </el-tooltip>
            <el-tooltip content="搜索">
              <div class="search-icon-wrap" @click="doSearch">
                <img class="search-icon" src="@/assets/images/search.png" />
              </div>
            </el-tooltip>
          </template>
          <template #default="{ item }">
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: space-between;
              ">
              <div>{{ item.value }}</div>
              <div
                class="delete-icon"
                title="删除"
                @click.prevent.stop="deleteSuggestion(item)">
                x
              </div>
            </div>
          </template>
        </el-autocomplete>
      </div>
    </div>
    <div class="tab-wrap">
      <div
        v-if="searchKeyUsing"
        v-for="item in tabItems"
        class="tab"
        :class="{ active: currentTab === item.key }"
        @click="changeTab(item)">
        {{ item.label }}
      </div>
      <div
        v-if="!searchKeyUsing"
        class="view-mode-wrap"
        @click="viewMode = viewMode === 'card' ? 'list' : 'card'">
        <img
          v-if="viewMode === 'card'"
          src="@/assets/images/icon31.png"
          alt="" />
        <img v-else src="@/assets/images/icon32.png" alt="" />
      </div>
    </div>
    <WaterfallLayout
      class="waterfall"
      v-if="viewMode === 'card' && !searchKeyUsing"
      :options="options"
      :list="list"
      :isOver="isOver"
      :isLoading="isLoading"
      :scroll-dom="homeRef"
      @getNext="getNext">
      <template #default="{ item, url, index }">
        <ProjectCardLazy
          :gray="projectReadList.includes(item.id)"
          :project="item"
          @toDetails="toDetails"></ProjectCardLazy>
      </template>
    </WaterfallLayout>
    <WaterfallLayout
      class="waterfall"
      v-if="viewMode === 'list' && !searchKeyUsing"
      :options="options1"
      :list="list"
      :isOver="isOver"
      :isLoading="isLoading"
      :scroll-dom="homeRef"
      @getNext="getNext">
      <template #default="{ item, url, index }">
        <NewsItem
          :gray="projectReadList.includes(item.id)"
          :project="item"
          @toDetails="toDetails"></NewsItem>
      </template>
    </WaterfallLayout>
    <WaterfallLayout
      class="waterfall"
      v-if="searchKeyUsing"
      :options="options2"
      :list="list2"
      :isOver="isOver"
      :isLoading="isLoading"
      :scroll-dom="homeRef"
      @getNext="getNext">
      <template v-slot:default="slotProp">
        <div
          class="list-item"
          v-preventLongPress="() => toDetails(slotProp.item)">
          <div
            v-if="getHighlightName(slotProp.item)?.length > 0"
            style="display: flex; align-items: center; margin-bottom: 12px">
            <img
              v-if="slotProp.item.content_type === 'article'"
              class="markicon"
              src="@/assets/images/icon30.png"
              alt="" />
            <div
              class="list-item-title"
              v-html="getHighlightName(slotProp.item)"></div>
          </div>
          <div
            v-else
            class="list-item-title"
            style="display: flex; align-items: center; margin-bottom: 12px">
            <img
              v-if="slotProp.item.content_type === 'article'"
              class="markicon"
              src="@/assets/images/icon30.png"
              alt="" />
            {{ slotProp.item.title }}
          </div>
          <!-- <div
            class="list-item-content"
            v-if="slotProp.item.highlights"
            v-for="item in slotProp.item.highlights?.highlights"
            v-html="cleanSpecialChars(item.fragments?.[0])"></div> -->
          <div
            class="list-item-content description_project"
            v-html="getHighlightDescription(slotProp.item)"></div>
          <div
            class="list-item-content"
            v-if="slotProp.item.matching_cards"
            v-for="card in slotProp.item.matching_cards">
            <div
              v-for="item in card.highlights?.highlights"
              v-html="cleanSpecialChars(item.fragments?.[0])"></div>
          </div>
        </div>
      </template>
      <template v-slot:footer>
        <div class="own-over-text">
          {{ overText }}
          <div
            class="more-wrap"
            v-show="overText.includes('未找到与关键词匹配的仓库')">
            您可以试试搜索
            <span @click="toNewindex('AI')" class="more">AI</span>
            、
            <span @click="toNewindex('平台')" class="more">平台</span>
            、
            <span @click="toNewindex('数据库')" class="more">数据库</span>
            ...
          </div>
        </div>
        <div
          class="over-text-btns"
          v-show="overText.includes('正在努力分析中')">
          <div class="over-text-btn" @click="searchKey = ''">取消</div>
          <div class="over-text-btn color" @click="handleFenxi">确认</div>
        </div>
      </template>
    </WaterfallLayout>
    <HomeFooter style="background-color: transparent"></HomeFooter>
    <el-backtop
      :right="40"
      :bottom="60"
      :visibility-height="300"
      target=".broadcasthome" />
    <WxQrCode />
    <el-dialog
      class="dialog"
      v-model="addProjectDialogVisible"
      width="462"
      :before-close="handleClose"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      align-center
      style="--el-color-danger: #ec5704; border-radius: 12px 12px 12px 12px">
      <div
        style="font-size: 16px; font-weight: bold; color: #000; margin: 0 20px">
        添加项目分析
      </div>
      <div style="font-size: 14px; color: #000; margin: 20px 20px 0">
        输入公共github仓库地址
      </div>
      <div class="form-div">
        <el-form
          class="ruleForm"
          ref="ruleFormRef"
          :model="ruleForm"
          :rules="rules"
          label-width="0px">
          <el-form-item label="" prop="repoUrl">
            <el-input
              class="el-ipt"
              v-model="ruleForm.repoUrl"
              placeholder="请输入仓库地址"></el-input>
          </el-form-item>
          <el-form-item>
            <div style="width: 100%; display: flex; justify-content: flex-end">
              <el-button
                type="primary"
                color="#55C4D5"
                plain
                @click="submitForm(ruleFormRef)">
                确认
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="BroadcastIndexPc">
import HomeFooter from "@/layout/components/HomeFooter/index.vue";
import {
  validateRepo,
  normaldownload,
  getProjectMixed,
  searchEs,
} from "@/api/broadcast.js";
import { getArticleList } from "@/api/article.js";
import WxQrCode from "@/components/WxQrCode";
import { isHttp } from "@/utils/validate";
import useUserStore from "@/store/modules/user";
import ProjectCardLazy from "@/views/broadcast/components/ProjectCardLazy.vue";
import NewsItem from "@/views/broadcast/components/NewsItem.vue";
import WaterfallLayout from "@/components/WaterfallLayout/index.vue";
import { nextTick, watch } from "vue";
import useAppStore from "@/store/modules/app";
import { onMounted } from "vue";
const appStore = useAppStore();
const userStore = useUserStore();
const router = useRouter();
const route = useRoute();
const { proxy } = getCurrentInstance();
let tabItems = ref([
  { key: "project", label: "仓库", count: 0 },
  { key: "article", label: "资讯", count: 0 },
]);
let currentTab = ref("");
//展示的卡片列表，新增时建议使用list.value=list.value.concat[data]方式
let list = ref([]);
let list2 = ref([]);
let list3 = ref([]);
let homeRef = ref(null);
// 当前查看模式，card、list
let viewMode = ref("card");
let options = ref({
  space: 26,
  breakpoints: {
    960: {
      rowPerView: 4,
    },
    800: {
      rowPerView: 3,
    },
    600: {
      rowPerView: 2,
    },
  },
});
let options1 = ref({
  space: 16,
  breakpoints: {
    960: {
      rowPerView: 1,
    },
  },
});
let options2 = ref({
  space: 16,
  breakpoints: {
    960: {
      rowPerView: 1,
    },
  },
});
//父组件是否挂载完成
let isMounted = ref(false);
let autocompleteRef = ref(null);
//是否显示搜索框,用来处理append-to=".search-wrap"时，search-wrap不存在的问题
let showAutocomplete = computed(() => {
  return isMounted.value;
});
//控制请求数据时显示加载状态提示
let isLoading = ref(false);
//控制数据是否已经全部加载完成(即不需要再滚动加载)
let isOver = ref(false);
let overText = ref("呀，被看光了！");
let addProjectDialogVisible = ref(false);
let ruleForm = ref({
  repoUrl: "",
});
let ruleFormRef = ref(null);
const validateRepoUrl = (rule, value, callback) => {
  if (!ruleForm.value.repoUrl?.startsWith("https://github.com/")) {
    callback(new Error("仓库地址格式不正确"));
  }
  callback();
};
let rules = {
  repoUrl: [
    { required: true, message: "仓库地址不能为空", trigger: "blur" },
    { validator: validateRepoUrl, trigger: "blur" },
  ],
};
const particlesOptions = ref({
  background: {
    color: {
      value: "#ffffff",
    },
  },
  fpsLimit: 60,
  fullScreen: {
    // 是否全屏，默认true。想要在当前容器内需要设置false
    enable: false,
  },
  interactivity: {
    events: {
      onClick: {
        enable: false,
        mode: "push",
      },
      onHover: {
        enable: true,
        mode: "grab",
      },
    },
    modes: {
      push: {
        quantity: 4,
      },
      grab: {
        distance: 150,
      },
    },
  },
  particles: {
    color: {
      value: "random",
    },
    links: {
      color: "random",
      distance: 150,
      enable: true,
      opacity: 0.3,
      width: 1,
    },
    move: {
      direction: "none",
      enable: true,
      outModes: "bounce",
      random: false,
      speed: 1,
      straight: false,
    },
    number: {
      // 密度 用value值除以区域值
      density: {
        enable: true,
        area: 2,
      },
      value: 150,
    },
    opacity: {
      value: 0.3,
    },
    shape: {
      type: "circle",
    },
    size: {
      value: { min: 1, max: 2 },
    },
  },
  //是否适配高清屏幕
  detectRetina: true,
});
const particlesLoaded = (container) => {
  // console.log("Particles container loaded", container);
  // 你可以在这里访问 container 对象，例如：
  // container.play();
  // container.pause();
  // container.refresh();
};
let searchKey = ref(route.query.search || "");
//正在查询中的searchKey,有值显示列表,否则显示卡片
let searchKeyUsing = ref("");
let searching = ref(false);
let page_size = ref(10);
let page = ref(1);
const getData = async () => {
  currentTab.value = "";
  isLoading.value = true;
  searching.value = true;
  let params = {
    page: page.value,
    page_size: page_size.value,
    status: "published",
  };
  if (!userStore.name) {
    params.recommend_tags = sessionStorage.getItem("latestTags");
  }
  getProjectMixed(params)
    .then((res) => {
      if (res.code == 200) {
        list.value = list.value.concat(res.data?.items || []);
        if (list.value.length >= res.data?.total) {
          isOver.value = true;
          overText.value = "呀，被看光了！";
        } else {
          page.value = page.value + 1;
        }
        if (list.value.length <= 10) {
          overText.value = "";
        }
      }
    })
    .catch((err) => {
      overText.value = "";
      isOver.value = true;
    })
    .finally(() => {
      isLoading.value = false;
      searching.value = false;
    });
};

// 找出在<mark> 和 </mark> 之间的内容
const extractMarkedText = (str) => {
  // 正则表达式匹配 <mark> 和 </mark> 之间的内容（非贪婪匹配）
  const regex = /<mark>(.*?)<\/mark>/g;
  const matches = [];
  let match;

  // 使用正则表达式循环匹配所有结果
  while ((match = regex.exec(str)) !== null) {
    // 将匹配到的内容（第一个捕获组）添加到数组中
    matches.push(match[1]);
  }

  // 使用 Set 去重并转回数组
  return [...new Set(matches)];
};
// 找出item中所有<mark> 和 </mark> 之间的词
const getMarkedTextFromItem = (item) => {
  let matches = [];
  if (item.highlights?.highlights?.length > 0) {
    item.highlights?.highlights.forEach((highlight) => {
      // highlight.fragments?.forEach((fragment) => {
      //   matches = matches.concat(extractMarkedText(fragment));
      // });
      matches = matches.concat(extractMarkedText(highlight.fragments?.[0]));
    });
  }
  if (item.matching_cards?.length > 0) {
    item.matching_cards?.forEach((card) => {
      card.highlights?.highlights?.forEach((highlight) => {
        // highlight.fragments?.forEach((fragment) => {
        //   matches = matches.concat(extractMarkedText(fragment));
        // });
        matches = matches.concat(extractMarkedText(highlight.fragments?.[0]));
      });
    });
  }
  return [...new Set(matches)].join(" ");
};
// 清理特殊字符
const cleanSpecialChars = (text) => {
  if (!text) return "";

  return (
    text
      // 移除所有的#字符
      .replace(/#/g, "")
      // 移除连续的2个及以上的*字符
      .replace(/\*{2,}/g, "")
      // 移除所有的`字符
      .replace(/`/g, "")
  );
};
const getHighlightName = (item) => {
  let a = item.highlights?.highlights?.find((e) => {
    return e.field_type === "项目名称" || e.field_type === "文章标题";
  });
  if (a) {
    return a.fragments?.[0];
  } else {
    return "";
  }
};
const getHighlightDescription = (item) => {
  let matches = [];
  if (item.highlights?.highlights?.length > 0) {
    item.highlights?.highlights.forEach((highlight) => {
      highlight.fragments?.forEach((fragment) => {
        matches = matches.concat(extractMarkedText(fragment));
      });
    });
  }
  if (matches.length > 0) {
    let des = JSON.parse(
      JSON.stringify(
        item.content_type === "project"
          ? item.metadata?.description_project
          : item.metadata?.summary
      )
    );
    matches.forEach((e) => {
      des = des.replaceAll(e, `<mark>${e}</mark>`);
    });
    return des;
  } else {
    return item.content_type === "project"
      ? item.metadata?.description_project
      : item.metadata?.summary;
  }
};
let projectReadList = ref(
  JSON.parse(localStorage.getItem("projectReadList")) || []
);
const changeTab = (item) => {
  if (currentTab.value === item.key) {
    currentTab.value = "";
  } else {
    currentTab.value = item.key;
  }
  doSearch();
};
// 将项目id加入已读列表。并存在localStorage里面
const addReadList = (item) => {
  let readList = JSON.parse(localStorage.getItem("projectReadList")) || [];
  if (!readList.includes(item.id)) {
    readList.unshift(item.id);
  }
  readList = readList.slice(0, 100); //最多100个
  localStorage.setItem("projectReadList", JSON.stringify(readList));
  // 手动更新响应式数据
  projectReadList.value = readList;

  //如果未登录，还要同时保存最近读的一个项目的tag到sessionstorage
  if (!userStore.name) {
    sessionStorage.setItem("latestTags", item.tags?.join(",") || "");
  }
};
const toDetails = (item) => {
  // console.log("toDetails", item, type);
  addReadList(item);
  const path =
    item.content_type === "project"
      ? "/broadcast/details"
      : "/broadcast/article";
  // 如果type是search还要带上参数：searchKey、匹配的卡片id
  let query = {
    id: item.id,
  };
  if (searchKeyUsing.value) {
    if (!isHttp(searchKey.value?.trim()?.split(" ")?.[0])) {
      query.search = getMarkedTextFromItem(item);
      query.cardIds = item.matching_cards
        ?.map((e) => {
          return e.id;
        })
        ?.join(",");
    }
    // 构建URL和查询参数
    const queryString = Object.entries(query)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join("&");
    // 在新窗口打开
    window.open(`${path}?${queryString}`, "_blank");
    return;
  }
  // console.log("query", query);
  router.push({
    path: path,
    query: query,
  });
};
const toNewindex = (key) => {
  const path = "/broadcast/index";
  window.open(`${path}?search=${key}`, "_blank");
};
const handleFenxi = () => {
  if (!userStore.userInfo.username) {
    userStore.loginDialogVisible = true;
  } else {
    let params = {
      projects: [{ repository_url: searchKeyUsing.value }],
    };
    proxy.$modal.loading();
    normaldownload(params)
      .then(() => {
        proxy.$modal.msgSuccess("操作成功");
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }
};
const addSuggestions = (str) => {
  let allSuggestions = JSON.parse(localStorage.getItem("allSuggestions")) || [];
  if (
    str.trim()?.length > 0 &&
    !allSuggestions.some((e) => {
      return e.value === str;
    })
  ) {
    allSuggestions.unshift({ value: str });
  }
  allSuggestions = allSuggestions.slice(0, 10); //最多10个建议词
  localStorage.setItem("allSuggestions", JSON.stringify(allSuggestions));
};
const deleteSuggestion = (item) => {
  let allSuggestions = JSON.parse(localStorage.getItem("allSuggestions")) || [];
  allSuggestions = allSuggestions.filter((e) => {
    return e.value !== item.value;
  });
  localStorage.setItem("allSuggestions", JSON.stringify(allSuggestions));
  setTimeout(() => {
    // 重新获取下拉数据
    autocompleteRef.value.getData(searchKeyUsing.value);
  }, 0);
};
const getSearchData = () => {
  isLoading.value = true;
  searching.value = true;
  searchEs({
    content_types: currentTab.value || "all",
    query: searchKeyUsing.value,
    filters: {
      status: "published",
    },
    page: page.value,
    size: page_size.value,
    include_cards: true,
    highlight: true,
  })
    .then((res) => {
      if (res.code == 200) {
        list2.value = list2.value.concat(res.data?.items || []);
        tabItems.value[0].count = res.data?.total_projects;
        tabItems.value[1].count = res.data?.total_articles;
        if (list2.value.length >= res.data?.total) {
          isOver.value = true;
          overText.value = "呀，被看光了！";
        } else {
          page.value = page.value + 1;
        }
        if (!(list2.value?.length > 0)) {
          overText.value = "未找到与关键词匹配的仓库！";
        } else if (list2.value?.length <= 10) {
          overText.value = "";
        }
      }
    })
    .catch((err) => {
      overText.value = "";
      isOver.value = true;
    })
    .finally(() => {
      isLoading.value = false;
      searching.value = false;
      addSuggestions(searchKeyUsing.value);
    });
};
const doAddProject = () => {
  // 判断用户是否登录，如果为登录，则提示用户去登录
  if (!userStore?.name) {
    proxy.$modal.msgWarning("登录后即可添加项目分析");
    return;
  }
  addProjectDialogVisible.value = true;
};
const handleClose = (done) => {
  done();
  nextTick(() => {
    ruleFormRef.value?.resetFields();
  });
};
const submitForm = () => {
  proxy.$refs["ruleFormRef"].validate((valid) => {
    if (valid) {
      validateRepo({ url: ruleForm.value.repoUrl }).then((res) => {
        if (res.code == 200) {
          if (res.data.exists) {
            // 被本地收录了，
            proxy.$modal.msgError("该仓库已被收录，请直接搜索查看！");
          } else if (res.data.valid) {
            // 未被收录，加入分析
            let params = {
              projects: [{ repository_url: ruleForm.value.repoUrl }],
            };
            proxy.$modal.loading();
            normaldownload(params)
              .then(() => {
                proxy.$modal.msgSuccess("任务已添加，请到任务列表中查看");
              })
              .finally(() => {
                proxy.$modal.closeLoading();
              });
          } else {
            proxy.$modal.msgError("未找到与地址匹配的仓库！");
          }
        }
      });
    }
  });
};
const getArticleData = () => {
  isLoading.value = true;
  searching.value = true;
  let params = {
    page: page.value,
    page_size: page_size.value,
    status: "published",
  };
  if (searchKeyUsing.value) {
    params.search = searchKeyUsing.value;
  }
  getArticleList(params)
    .then((res) => {
      if (res.code == 200) {
        list3.value = list3.value.concat(res.data?.articles || []);
        if (list3.value.length >= res.data?.total) {
          isOver.value = true;
          overText.value = "呀，被看光了！";
        } else {
          page.value = page.value + 1;
        }
        if (list3.value.length <= 10) {
          overText.value = "";
        }
      }
    })
    .catch((err) => {
      overText.value = "";
      isOver.value = true;
    })
    .finally(() => {
      isLoading.value = false;
      searching.value = false;
    });
};
const doSearch = (type) => {
  // autocompleteRef.value?.blur();
  // searchKey.value = searchKey.value?.trim();
  // if(!searchKey.value?.trim()?.length>0){
  //   proxy.$modal.msgError("搜索关键词不能为空");
  //   return;
  // }
  if (type !== "getNext") {
    let a = JSON.parse(JSON.stringify(route.query));
    if (a.search) {
      a.search = searchKey.value;
    } else {
      delete a.search;
    }
    router.replace({
      path: "/broadcast/index",
      query: a,
    });
    searchKeyUsing.value = searchKey.value;
    page.value = 1;
    page_size.value = 10;
    isOver.value = false;
    list.value = [];
    list2.value = [];
    list3.value = [];
    setTimeout(() => {
      //增加定时器，修复Safari上直接scrollTop不生效的问题
      document.querySelector(".home").scrollTop = 0;
    }, 10);
  }

  // 每次搜索之前判断输入词是否是http url，如果是，则判断是否是合法的github地址以及是否被本地收录
  // 如果被本地收录了，直接显示返回的项目信息，否则显示提示文字
  if (isHttp(searchKey.value?.split(" ")?.[0])) {
    if (searching.value) {
      return;
    }
    searching.value = true;
    isLoading.value = true;
    validateRepo({ url: searchKey.value })
      .then((res) => {
        if (res.code == 200) {
          list2.value = [];
          isOver.value = true;
          if (res.data.exists) {
            // 被本地收录了，直接显示返回的项目信息
            list2.value = res.data.project ? [res.data.project] : [];
            overText.value = "";
          } else if (res.data.valid) {
            // 未被收录，显示提示文字
            overText.value = "gugu正在努力分析中，有结果第一时间同步给您。";
          } else {
            overText.value = "未找到与链接匹配的仓库！";
          }
        }
      })
      .catch((err) => {
        overText.value = "";
        isOver.value = true;
      })
      .finally(() => {
        isLoading.value = false;
        searching.value = false;
        addSuggestions(searchKeyUsing.value);
      });
  } else {
    if (searchKeyUsing.value) {
      getSearchData();
    } else {
      getData();
    }
  }
};
const getNext = () => {
  // console.log("getNext", isOver.value);
  doSearch("getNext");
};
// 添加标志位防止重复搜索
const isResetting = ref(false);
watch(
  () => appStore.refreshBroadcastIndex,
  (newValue) => {
    if (newValue) {
      isResetting.value = true;
      appStore.refreshBroadcastIndex = false;
      searchKey.value = "";
      doSearch();
      nextTick(() => {
        isResetting.value = false;
      });
    }
  }
);
// url中有search的时候调用搜索
watch(
  () => route.query.search,
  (newValue) => {
    if (newValue) {
      searchKey.value = newValue;
    }
  }
);
watch(
  searchKey,
  (value) => {
    // console.log("watch");
    if (value === "" && !isResetting.value) {
      doSearch();
    }
  },
  { immediate: true }
);

watch(
  () => userStore.name,
  (newValue, oldValue) => {
    // 当登录或者退出登录时，刷新列表数据
    if ((newValue && !oldValue) || (!newValue && oldValue)) {
      // console.log("route.query.from", sessionStorage.getItem("query_from"));
      if (sessionStorage.getItem("query_from") === "/broadcast/index") {
        sessionStorage.setItem("query_from", "");
        doSearch();
      }
    }
  }
);

const handleBeforeUnload = () => {
  sessionStorage.setItem("homeScrollTop", 0);
};
onActivated(() => {
  console.log("onActivated");
  // 登录及退出时刷新首页数据
  let query_from = sessionStorage.getItem("query_from");
  if (query_from?.length > 0 && query_from !== "/broadcast/index") {
    sessionStorage.setItem("query_from", "");
    doSearch();
  }
  isMounted.value = true;
  if (searchKey.value.length > 0) {
    doSearch();
  }

  setTimeout(() => {
    document.querySelector(".home").scrollTop =
      sessionStorage.getItem("homeScrollTop") || 0;
  }, 10);
  // 同步收藏变更的数据
  nextTick(() => {
    let collectChangeData = sessionStorage.getItem("collectChangeData");
    if (collectChangeData?.length > 0) {
      collectChangeData = JSON.parse(collectChangeData);
      list.value = list.value.map((item) => {
        let existIndex = -1;
        for (let i = 0, l = collectChangeData.length; i < l; i++) {
          if (collectChangeData[i][item.id]) {
            existIndex = i;
            break;
          }
        }
        if (existIndex > -1) {
          item.is_collected = collectChangeData[existIndex][item.id][0];
          item.collect_count = collectChangeData[existIndex][item.id][1];
        }
        return item;
      });
      sessionStorage.removeItem("collectChangeData");
    }
  });
  window.addEventListener("beforeunload", handleBeforeUnload);
});
onDeactivated(() => {
  //记录滚动位置
  sessionStorage.setItem(
    "homeScrollTop",
    document.querySelector(".home")?.scrollTop || 0
  );
  window.removeEventListener("beforeunload", handleBeforeUnload);
});
const fetchSuggestions = (queryString, cb) => {
  queryString = "" + queryString;
  let results = JSON.parse(localStorage.getItem("allSuggestions")) || [];
  if (queryString) {
    results = results.filter((item) => {
      return item.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
    });
  }
  cb(results);
};

const handleSelect = (item) => {
  doSearch();
};
</script>

<style scoped lang="scss">
.app-container {
  background:
    radial-gradient(at 20% 0%, rgba(202, 229, 245, 0.6), transparent 20%),
    radial-gradient(at 60% -15%, rgba(202, 229, 245, 0.7), transparent 40%),
    radial-gradient(at 100% 100%, rgba(160, 235, 248, 0.7), transparent 40%),
    radial-gradient(at 0% 100%, rgba(243, 252, 202, 1), transparent 30%);
  padding: 0;
}
.home {
  position: relative;
  height: calc(100vh - var(--navbar-height));
  overflow-y: scroll;
  scroll-behavior: smooth;
  width: 100vw;
  #tsparticles {
    position: fixed;
    z-index: -1;
    top: var(--navbar-height);
    bottom: 0;
    left: 0;
    right: 0;
  }
  .home-title {
    font-size: 32px;
    text-align: center;
    height: 120px;
    line-height: 120px;
    // font-weight: bold;
  }
  .search-wrap-parent {
    position: sticky;
    top: 10px;
    z-index: 10;
    width: 80vw;
    max-width: 675px;
    margin: 0 auto 80px;
  }
  .search-wrap {
    width: 100%;
    background: #ffffff;
    border-radius: 22px;
    box-shadow: 0 0 0 1px #55c4d5;
    // overflow: hidden;
    transition: all 0.5s;
    position: absolute;
    top: 0;
    left: 0;
    border: solid 1px #55c4d5;
    &:has(.el-input__wrapper.is-focus) {
      box-shadow: 0 0 4px 4px #55c4d5;
      // border-radius: 22px 22px 0 0;
    }
    .search-icon-wrap {
      width: 30px;
      height: 48px;
      display: flex;
      align-items: center;
      cursor: pointer;
      & + .search-icon-wrap {
        margin-left: 4px;
      }
    }
    .search-icon {
      width: 30px;
      height: 30px;
    }
  }
}
.tab-wrap {
  display: flex;
  align-items: center;
  font-size: 12px;
  width: 960px;
  height: 30px;
  margin: 0 auto 10px;
  position: relative;
  .tab {
    border-radius: 4px;
    border: solid 1px #55c4d5;
    cursor: pointer;
    padding: 8px 24px;
    &.active,
    &:hover {
      background-color: #55c4d5;
    }
    & + .tab {
      margin-left: 16px;
    }
  }
  .view-mode-wrap {
    position: absolute;
    right: 2px;
    top: 0;
    img {
      cursor: pointer;
      width: 28px;
      height: 28px;
    }
  }
}
.waterfall {
  min-height: calc(100vh - var(--navbar-height) - 120px - 120px);
  max-width: 960px;
  margin: 0 auto;
}
.own-over-text {
  color: #999;
  text-align: center;
  .more-wrap {
    margin-top: 10px;
  }
  .more {
    color: #0048bd;
    cursor: pointer;
  }
}
.over-text-btns {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  .over-text-btn {
    border: solid 1px #55c4d5;
    cursor: pointer;
    border-radius: 12px;
    width: 120px;
    height: 28px;
    font-size: 12px;
    color: #55c4d5;
    line-height: 28px;
    text-align: center;
    & + .over-text-btn {
      margin-left: 26px;
    }
    &.color {
      background-color: #55c4d5;
      color: #fff;
    }
  }
}

.list-item {
  background-color: #f0f0f0;
  border-radius: 4px;
  padding: 10px 20px;
  cursor: pointer;
  color: #111;
  &:hover {
    background-color: #d5edf0;
  }
  .list-item-title {
    font-size: 14px;
    font-weight: bold;
    color: #000;
  }
  .list-item-content {
    font-size: 12px;
    color: #232323;
    word-break: break-all;
    line-height: 18px;
    text-wrap: pretty;
    &.description_project {
      color: #6f6f6f;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      line-clamp: 3;
      -webkit-line-clamp: 3; /* 限制显示的行数 */
      overflow: hidden;
      text-overflow: ellipsis;
    }
    & + .list-item-content {
      margin-top: 6px;
    }
  }
}
.markicon {
  width: 24px;
  height: 24px;
  margin-right: 4px;
}
.dialog {
  .form-div {
    display: flex;
    justify-content: center;
    margin-top: 18px;

    .ruleForm {
      --el-input-focus-border-color: #ec5704;
      width: 100%;
      margin: 0 20px;

      .el-ipt {
        width: 100%;
        height: 40px;
        :deep(.el-input__wrapper) {
          box-shadow: 0 0 0 1px #dcdfe6;
          border: none;
          font-size: 14px;
          background-color: transparent;
          padding-top: 2px;
        }
      }
    }
  }
}
//当屏幕小于960时设置样式
@media screen and (max-width: 960px) {
  .home {
    .home-title {
      font-size: 26px;
    }
  }
}
@media screen and (max-width: 768px) {
  .home {
    .home-title {
      font-size: 20px;
    }
  }
}
:deep(.el-input__wrapper) {
  box-shadow: none;
  border: none;
  font-size: 18px;
  background-color: transparent;
  padding-top: 2px;
}
:deep(.el-input-group__append) {
  background-color: transparent;
  border: none;
  padding-left: 10px;
  box-shadow: none;
}
:deep(.list-item-title mark),
:deep(.list-item-content mark) {
  color: #fa5d15;
  text-decoration: underline;
  background-color: transparent;
}
:deep(.el-autocomplete .el-input) {
  height: 100%;
}
:deep(.delete-icon) {
  cursor: pointer;
  font-size: 14px;
  color: #999;
  width: 24px;
  height: 24px;
  line-height: 22px;
  border-radius: 50%;
  text-align: center;
  visibility: hidden;
  &:hover {
    background-color: #dbdcdd;
  }
}
// 只有鼠标悬浮时才显示删除按钮
:deep(.el-autocomplete-suggestion__list > li:hover .delete-icon) {
  visibility: visible;
}
:deep(.search-wrap .search-wrap-autocomplete) {
  border: none;
  box-sizing: border-box;
  box-shadow: none;
  border-radius: 0 0 22px 22px;
  outline: none;
  overflow: hidden;
  position: static !important;
}
:deep(.search-wrap-autocomplete .el-popper__arrow) {
  display: none;
}
:deep(.el-autocomplete-suggestion__wrap:not(:has(ul > li))) {
  padding: 0;
}
:deep(.el-zoom-in-top-enter-active),
:deep(.el-zoom-in-top-leave-active) {
  transition: none !important;
}
</style>
