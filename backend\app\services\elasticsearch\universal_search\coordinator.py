"""
搜索协调器

协调多种内容类型的搜索，处理搜索策略和结果合并。
"""
import logging
import asyncio
from typing import Dict, Any, List, Optional
from app.services.elasticsearch.client import ElasticsearchClient
from .types import SearchRequest, SearchResult, ContentType, ContentTypeAdapter
from .result_processor import UnifiedResultProcessor

logger = logging.getLogger(__name__)


class SearchCoordinator:
    """搜索协调器"""
    
    def __init__(self, es_client: ElasticsearchClient):
        """
        初始化搜索协调器
        
        Args:
            es_client: Elasticsearch客户端
        """
        self.es_client = es_client
        self.result_processor = UnifiedResultProcessor()
        self.adapters: Dict[ContentType, ContentTypeAdapter] = {}
    
    def register_adapter(self, adapter: ContentTypeAdapter):
        """
        注册内容类型适配器
        
        Args:
            adapter: 内容类型适配器
        """
        self.adapters[adapter.content_type] = adapter
        logger.info(f"注册内容类型适配器: {adapter.content_type.value}")
    
    async def search(self, request: SearchRequest) -> SearchResult:
        """
        执行搜索
        
        Args:
            request: 搜索请求
            
        Returns:
            SearchResult: 搜索结果
        """
        try:
            # 确定要搜索的内容类型
            target_types = self._determine_target_types(request.content_types)
            
            if not target_types:
                return self._empty_result(request)
            
            # 执行搜索
            if len(target_types) == 1:
                # 单一类型搜索
                return await self._single_type_search(request, target_types[0])
            else:
                # 多类型搜索
                return await self._multi_type_search(request, target_types)
                
        except Exception as e:
            logger.error(f"搜索执行失败: {str(e)}", exc_info=True)
            return self._empty_result(request)
    
    def _determine_target_types(self, content_types: List[ContentType]) -> List[ContentType]:
        """
        确定目标内容类型
        
        Args:
            content_types: 请求的内容类型列表
            
        Returns:
            List[ContentType]: 实际要搜索的内容类型列表
        """
        target_types = []
        
        for content_type in content_types:
            if content_type == ContentType.ALL:
                # 搜索所有已注册的类型
                target_types.extend([ct for ct in self.adapters.keys() if ct != ContentType.ALL])
            elif content_type in self.adapters:
                target_types.append(content_type)
            else:
                logger.warning(f"未注册的内容类型: {content_type.value}")
        
        # 去重
        return list(set(target_types))
    
    async def _single_type_search(self, 
                                request: SearchRequest,
                                content_type: ContentType) -> SearchResult:
        """
        单一类型搜索
        
        Args:
            request: 搜索请求
            content_type: 内容类型
            
        Returns:
            SearchResult: 搜索结果
        """
        adapter = self.adapters[content_type]
        
        try:
            # 执行搜索
            result = await adapter.search(request)
            
            # 处理结果
            processed_result = self.result_processor.process_elasticsearch_result(
                result, content_type
            )
            
            # 转换为统一格式
            return self._convert_to_search_result(processed_result, request)
            
        except Exception as e:
            logger.error(f"单一类型搜索失败 {content_type.value}: {str(e)}")
            return self._empty_result(request)
    
    async def _multi_type_search(self,
                               request: SearchRequest,
                               content_types: List[ContentType]) -> SearchResult:
        """
        多类型搜索

        Args:
            request: 搜索请求
            content_types: 内容类型列表

        Returns:
            SearchResult: 合并后的搜索结果
        """
        # 为多类型搜索创建修改后的请求，获取更多结果用于合并
        modified_request = SearchRequest(
            query=request.query,
            content_types=request.content_types,
            filters=request.filters,
            sort_by=request.sort_by,
            sort_order=request.sort_order,
            page=1,  # 从第一页开始
            page_size=request.page_size * len(content_types) * 2,  # 获取更多结果
            highlight=request.highlight
        )

        # 并发执行各类型搜索
        search_tasks = []

        for content_type in content_types:
            if content_type in self.adapters:
                task = self._search_single_type_async(modified_request, content_type)
                search_tasks.append(task)

        if not search_tasks:
            return self._empty_result(request)

        try:
            # 等待所有搜索完成
            results = await asyncio.gather(*search_tasks, return_exceptions=True)

            # 过滤异常结果
            valid_results = []
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"搜索任务异常: {str(result)}")
                elif result:
                    valid_results.append(result)

            # 合并结果
            return self.result_processor.merge_results(valid_results, request)

        except Exception as e:
            logger.error(f"多类型搜索失败: {str(e)}")
            return self._empty_result(request)
    
    async def _search_single_type_async(self, 
                                      request: SearchRequest,
                                      content_type: ContentType) -> Optional[Dict[str, Any]]:
        """
        异步执行单一类型搜索
        
        Args:
            request: 搜索请求
            content_type: 内容类型
            
        Returns:
            Optional[Dict[str, Any]]: 搜索结果
        """
        try:
            adapter = self.adapters[content_type]
            result = await adapter.search(request)
            
            return self.result_processor.process_elasticsearch_result(
                result, content_type
            )
            
        except Exception as e:
            logger.error(f"异步搜索失败 {content_type.value}: {str(e)}")
            return None
    
    def _convert_to_search_result(self,
                                processed_result: Dict[str, Any],
                                request: SearchRequest) -> SearchResult:
        """
        转换为SearchResult格式

        Args:
            processed_result: 处理后的结果
            request: 搜索请求

        Returns:
            SearchResult: 搜索结果
        """
        items = processed_result.get("items", [])
        total = processed_result.get("total", 0)

        # 分页已经在 Elasticsearch 查询中处理，这里不需要再次分页
        total_pages = (total + request.page_size - 1) // request.page_size if total > 0 else 0

        return SearchResult(
            items=items,
            total=total,
            page=request.page,
            page_size=request.page_size,
            total_pages=total_pages,
            search_time=processed_result.get("search_time", 0),
            aggregations=processed_result.get("aggregations", {}),
            query=request.query or "",
            content_types=request.content_types
        )
    
    def _empty_result(self, request: SearchRequest) -> SearchResult:
        """
        创建空搜索结果
        
        Args:
            request: 搜索请求
            
        Returns:
            SearchResult: 空搜索结果
        """
        return SearchResult(
            items=[],
            total=0,
            page=request.page,
            page_size=request.page_size,
            total_pages=0,
            search_time=0,
            aggregations={},
            query=request.query or "",
            content_types=request.content_types
        )
    
    def get_registered_types(self) -> List[ContentType]:
        """
        获取已注册的内容类型
        
        Returns:
            List[ContentType]: 已注册的内容类型列表
        """
        return list(self.adapters.keys())
    
    def is_type_supported(self, content_type: ContentType) -> bool:
        """
        检查是否支持指定的内容类型
        
        Args:
            content_type: 内容类型
            
        Returns:
            bool: 是否支持
        """
        return content_type in self.adapters
