#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/1/27
# @File    : flow_log_utils.py
# @Description: 用户流程日志记录工具
"""

from typing import Optional
import structlog
from sqlalchemy.orm import Session

from app.models.statistics import UserFlowLogModel

logger = structlog.get_logger(__name__)



async def record_system_message(
        session: Session,
        message: str,
        message_type: str = "system",
        created_by: Optional[str] = None,
        project_id: Optional[str] = None,

) :
    """
    记录系统消息

    Args:
        session: 数据库会话
        message: 系统消息内容
        message_type: 消息类型（默认为"system"）
        created_by: 创建人ID（可选）

    Returns:
        SystemLogModel: 创建的系统消息记录

    Raises:
        Exception: 当记录系统消息失败时抛出异常
    """
    try:
        # 导入系统日志模型
        from app.models.rbac.system_log import SystemLogModel

        # 创建系统消息记录
        system_message = SystemLogModel(
            message=message,
            message_type=message_type,
            is_read=False,  # 默认未读
            created_by=created_by,
            project_id=project_id
        )

        # 添加到会话
        session.add(system_message)

        # 注意：这里不执行 commit，由调用方负责提交事务
        # session.commit()

        logger.info(
            "系统消息记录已创建",
            message=message[:50] + "..." if len(message) > 50 else message,
            message_type=message_type,
            created_by=created_by
        )

        return system_message

    except Exception as e:
        logger.error(
            "记录系统消息失败",
            error=str(e),
            message=message[:50] + "..." if len(message) > 50 else message,
            message_type=message_type,
            created_by=created_by
        )
        raise Exception(f"记录系统消息失败: {str(e)}") from e

async def record_user_flow_log(
        session: Session,
        log_type: str,
        project_id: str,
        project_name: str = None,
        content: str = None,
        created_by: Optional[str] = None
) -> UserFlowLogModel:
    """
    记录用户流程日志

    Args:
        session: 数据库会话
        log_type: 日志类型
        project_id: 日志关联项目id
        project_name: 日志关联项目名字
        content: 日志内容
        created_by: 创建人ID（可选）

    Returns:
        UserFlowLogModel: 创建的日志记录

    Raises:
        Exception: 当记录日志失败时抛出异常
    """
    try:

        project_name = project_name or "未知项目"
        content = content or f"执行了{log_type}操作"

        # 创建日志记录
        log_record = UserFlowLogModel(
            log_type=log_type,
            project_id=project_id,
            project_name=project_name,
            content=content,
            created_by=created_by
        )

        # 添加到会话
        session.add(log_record)

        # 注意：这里不执行 commit，由调用方负责提交事务
        # session.commit()

        # 多邻国发送服务号
        # here

        logger.info(
            "用户流程日志记录已创建",
            log_type=log_type,
            project_id=project_id,
            project_name=project_name,
            created_by=created_by
        )

        return log_record

    except Exception as e:
        logger.error(
            "记录用户流程日志失败",
            error=str(e),
            log_type=log_type,
            project_id=project_id,
            project_name=project_name
        )
        raise Exception(f"记录用户流程日志失败: {str(e)}") from e


def record_user_flow_log_sync(
        session: Session,
        log_type: str,
        project_id: str,
        project_name: str,
        content: str,
        created_by: Optional[str] = None
) -> UserFlowLogModel:
    """
    记录用户流程日志（同步版本）

    Args:
        session: 数据库会话
        log_type: 日志类型
        project_id: 日志关联项目id
        project_name: 日志关联项目名字
        content: 日志内容
        created_by: 创建人ID（可选）

    Returns:
        UserFlowLogModel: 创建的日志记录

    Raises:
        Exception: 当记录日志失败时抛出异常
    """
    try:

        project_name = project_name or "未知项目"
        content = content or f"执行了{log_type}操作"

        # 创建日志记录
        log_record = UserFlowLogModel(
            log_type=log_type,
            project_id=project_id,
            project_name=project_name,
            content=content,
            created_by=created_by
        )

        # 添加到会话
        session.add(log_record)

        # 注意：这里不执行 commit，由调用方负责提交事务
        # session.commit()

        logger.info(
            "用户流程日志记录已创建",
            log_type=log_type,
            project_id=project_id,
            project_name=project_name,
            created_by=created_by
        )

        return log_record

    except Exception as e:
        logger.error(
            "记录用户流程日志失败",
            error=str(e),
            log_type=log_type,
            project_id=project_id,
            project_name=project_name
        )
        raise Exception(f"记录用户流程日志失败: {str(e)}") from e


# 预定义的日志类型常量
class FlowLogType:
    """流程日志类型常量"""
    PROJECT_DOWNLOAD = "project_download"  # 项目下载
    PROJECT_ANALYSIS = "project_analysis"  # 项目分析
    PROJECT_GENERATE = "project_generate"  # 项目生成
    PROJECT_SHARE = "project_share"  # 项目分享
    PROJECT_COLLECT = "project_collect"  # 项目收藏
    PROJECT_CANCEL = "project_cancel"  # 项目取消
    PROJECT_PRIORITY = "project_priority"  # 项目加急
    PROJECT_ERROR = "project_error"  # 项目错误
    USER_LOGIN = "user_login"  # 用户登录
    USER_LOGOUT = "user_logout"  # 用户登出
    SYSTEM_OPERATION = "system_operation"  # 系统操作
    HISTORY = "history_log"

    # used in system message new
    ARTICLE_HISTORY = "article_history_log"