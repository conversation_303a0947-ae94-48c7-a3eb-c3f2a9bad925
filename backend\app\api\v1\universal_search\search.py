import logging
from typing import Dict, Any, Optional, List, Union

from app.api.base import <PERSON>Handler
from app.services.elasticsearch.universal_search import UniversalSearchEngine
from app.core.di.containers import Container
from dependency_injector.wiring import inject, Provide

logger = logging.getLogger(__name__)


class UniversalSearchHandler(BaseHandler):

    @inject
    def initialize(
        self,
        universal_search_engine: UniversalSearchEngine = Provide[Container.universal_search_engine]
    ):
        """初始化处理器

        Args:
            universal_search_engine: 通用搜索引擎
        """
        self.search_engine = universal_search_engine
        super().initialize()

    async def post(self):
        try:
            request_data = self.json_body or {}

            query = request_data.get("query", "").strip()
            content_types = request_data.get("content_types", "all")
            filters = request_data.get("filters", {})
            sort_by = request_data.get("sort_by", "_score")
            sort_order = request_data.get("sort_order", "desc")
            page = request_data.get("page", 1)
            page_size = request_data.get("size", 10)
            highlight = request_data.get("highlight", True)

            validation_result = self._validate_search_params(
                query, content_types, page, page_size, sort_by, sort_order
            )
            if validation_result:
                return self.write_error(400, error_message=validation_result)

            search_result = await self.search_engine.search(
                query=query,
                content_types=content_types,
                filters=filters,
                sort_by=sort_by,
                sort_order=sort_order,
                page=page,
                page_size=page_size,
                highlight=highlight
            )

            return self.success_response(search_result)

        except ValueError as e:
            return self.write_error(400, error_message=f"参数错误: {str(e)}")
        except Exception as e:
            logger.error(f"搜索执行失败: {str(e)}", exc_info=True)
            return self.write_error(500, error_message="搜索服务异常")
    
    def _validate_search_params(self,
                               query: str,
                               content_types: Union[str, List[str]],
                               page: int,
                               page_size: int,
                               sort_by: str,
                               sort_order: str) -> Optional[str]:
        if not query:
            return "搜索关键词不能为空"

        if len(query) > 200:
            return "搜索关键词长度不能超过200个字符"

        valid_content_types = {"all", "article", "project"}
        if isinstance(content_types, str):
            if content_types not in valid_content_types:
                return f"无效的内容类型: {content_types}"
        elif isinstance(content_types, list):
            for ct in content_types:
                if ct not in valid_content_types:
                    return f"无效的内容类型: {ct}"
        else:
            return "内容类型格式错误"

        if page < 1:
            return "页码必须大于0"

        if page_size < 1 or page_size > 100:
            return "每页数量必须在1-100之间"

        valid_sort_fields = {
            "_score", "title", "name", "created_at", "updated_at", "published_at",
            "read_count", "like_count", "comment_count", "collect_count",
            "popularity_score", "cards_count", "likes_count", "views_count",
            "popularity", "relevance"
        }
        if sort_by not in valid_sort_fields:
            return f"无效的排序字段: {sort_by}"

        if sort_order not in {"asc", "desc"}:
            return f"无效的排序方向: {sort_order}"

        return None


class ArticleSearchHandler(BaseHandler):

    @inject
    def initialize(
        self,
        universal_search_engine: UniversalSearchEngine = Provide[Container.universal_search_engine]
    ):
        """初始化处理器

        Args:
            universal_search_engine: 通用搜索引擎
        """
        self.search_engine = universal_search_engine
        super().initialize()

    async def post(self):
        try:
            request_data = self.json_body or {}

            query = request_data.get("query", "").strip()
            filters = request_data.get("filters", {})
            sort_by = request_data.get("sort_by", "_score")
            sort_order = request_data.get("sort_order", "desc")
            page = request_data.get("page", 1)
            page_size = request_data.get("size", 10)
            highlight = request_data.get("highlight", True)

            search_result = await self.search_engine.search_articles(
                query=query,
                filters=filters,
                sort_by=sort_by,
                sort_order=sort_order,
                page=page,
                page_size=page_size,
                highlight=highlight
            )

            return self.success_response(search_result)

        except Exception as e:
            logger.error(f"文章搜索失败: {str(e)}", exc_info=True)
            return self.write_error(500, error_message="文章搜索服务异常")


class ProjectSearchHandler(BaseHandler):

    @inject
    def initialize(
        self,
        universal_search_engine: UniversalSearchEngine = Provide[Container.universal_search_engine]
    ):
        """初始化处理器

        Args:
            universal_search_engine: 通用搜索引擎
        """
        self.search_engine = universal_search_engine
        super().initialize()

    async def post(self):
        try:
            request_data = self.json_body or {}

            query = request_data.get("query", "").strip()
            filters = request_data.get("filters", {})
            sort_by = request_data.get("sort_by", "_score")
            sort_order = request_data.get("sort_order", "desc")
            page = request_data.get("page", 1)
            page_size = request_data.get("size", 10)
            highlight = request_data.get("highlight", True)

            search_result = await self.search_engine.search_projects(
                query=query,
                filters=filters,
                sort_by=sort_by,
                sort_order=sort_order,
                page=page,
                page_size=page_size,
                highlight=highlight
            )

            return self.success_response(search_result)

        except Exception as e:
            logger.error(f"项目搜索失败: {str(e)}", exc_info=True)
            return self.write_error(500, error_message="项目搜索服务异常")


class SearchStatsHandler(BaseHandler):

    @inject
    def initialize(
        self,
        universal_search_engine: UniversalSearchEngine = Provide[Container.universal_search_engine]
    ):
        """初始化处理器

        Args:
            universal_search_engine: 通用搜索引擎
        """
        self.search_engine = universal_search_engine
        super().initialize()

    async def get(self):
        try:
            stats = await self.search_engine.get_statistics()
            return self.success_response(stats)

        except Exception as e:
            logger.error(f"获取搜索统计失败: {str(e)}", exc_info=True)
            return self.write_error(500, error_message="获取统计信息失败")
