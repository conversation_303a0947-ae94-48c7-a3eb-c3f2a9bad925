"""
API路由配置
"""
import os
from typing import List, Tuple, Union, Type
from tornado.web import URLSpec, RequestHandler

from app.api.base import CORSStaticFileHandler
from app.api.v1.article import ArticleComment<PERSON><PERSON><PERSON><PERSON><PERSON>, ArticleCommentCreate<PERSON>and<PERSON>, ArticleCommentAuditHandler
from app.api.v1.article.article import ArticleHand<PERSON>, ArticleLikeHandler, ArticleShareHandler, \
    ArticleCommentSearchHandler
from app.api.v1.article.article_collect import Article<PERSON>ollectHand<PERSON>, ArticleCollectCheckHandler
from app.api.v1.article.comment_article import ArticleCommentDeleteHandler
from app.api.v1.article.wechat_article import <PERSON>chatArticlesHandler, WechatArticleDetailHandler, \
    WechatArticlesStatsHandler

from app.api.v1.github.github_image import GitHubImageUploadHandler, GitHubImageListHandler
from app.api.v1.github.github_project import GitHubProject<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GitHubProject<PERSON><PERSON><PERSON>, \
    GitHubProjectCardGenerateHandler, ProjectUserCollectHandler, MixedProjectArticleHandler
from app.api.v1.github.github_project_agent_remote import AnalyzerCalledHandler, AgentHealthCheckHandler, \
    PublisherCalledHandler, AnalyzerCancelHandler
from app.api.v1.github.github_project_card import GitHubCardHandler, GitHubCardInteractionHandler, \
    GitHubCardDetailHandler, GitHubCardBatchHandler, GitHubDownloadStatusHandler, GitHubGenerateStatusHandler, \
    GitHubAutoFinderHandler, GitHubSolveDataHandler
from app.api.v1.github.github_project_detail import GitHubProjectDetailHandler
from app.api.v1.github.github_project_file import GitHubProjectFileHandler
from app.api.v1.github.github_project_scan_workflow import ProjectScanHandler, ProjectGetFlowStatusHandler, \
    ProjectLinkCheckHandler, ProjectDownloadHandler, ProjectPriorityHandler, ProjectCancelGenerationHandler, \
    ProjectUserFlowLog, ProjectUserHistoryLog, ProjectGetFlowStatusCurrentUserHandler
from app.api.v1.github.github_project_share import GitHubShareHandler, GitHubPhoneShareSolveHandler
from app.api.v1.health import HealthHandler
from app.api.v1.rbac.auth import (
    LoginHandler,
    LogoutHandler,
    RefreshTokenHandler, FastAuthHandler, FastAuthVerificationCodeHandler, BindChangeAuthHandler,
    BindChangeAuthVerificationCodeHandler, BindChangeOriginalAuthHandler, BindChangeAuthVerificationCodeOriginalHandler
)
from app.api.v1.rbac.user import (
    UserHandler,
    UserDetailHandler,
    RegisterHandler,
    ChangePasswordHandler,
    ResetPasswordHandler,
    UserStatusHandler,
    UserPermissionsHandler, VerificationCodeHandler, UpdateSelfPasswordHandler,
    UpdateSelfPasswordVerificationCodeHandler, UpdateOauthEmailVerificationCodeHandler, UpdateOauthEmailHandler
)
from app.api.v1.rbac.role import RoleHandler, RoleDetailHandler, RolePermissionGroupHandler, RolePermissionHandler, \
    UserRoleHandler
from app.api.v1.rbac.permission import PermissionHandler, PermissionDetailHandler
from app.api.v1.rbac.permission_group import PermissionGroupHandler, PermissionGroupDetailHandler
from app.api.v1.rbac.oauth import (
    OAuthProvidersHandler,
    OAuthAuthorizeHandler,
    OAuthGithubCallbackHandler,
    OAuthAccountsHandler,
    OAuthAccountDeleteHandler, OAuthGiteeCallbackHandler,
)
from app.api.v1.rbac.current_user import CurrentUserHandler, CurrentUserChangePasswordHandler, \
    CurrentUserSystemLogHandler, GitHubCardSearchHandler
from app.api.v1.git import (
    GitRepositoryHandler,
    GitBranchesHandler,
    GitCommitsHandler,
    GitFilesHandler,
    GitFileContentHandler,
    GitFileHistoryHandler,
    GitFindRepositoriesHandler
)
from app.api.v1.rbac.user import (
    PasswordResetRequestHandler,
    PasswordResetVerifyHandler,
    PasswordResetCompleteHandler
)
from app.api.v1.github.github_project_tree import GitHubProjectTreeHandler
from app.api.v1.github.github_search import RepoUrlValidationHandler
from app.api.v1.rbac.wechat import WechatVerifyCodeHandler, WechatOfficialAccountAuthHandler
from app.api.v1.statistics import StatisticsHandler, StatisticsAdminHandler
from app.api.v1.intelligent_article import get_intelligent_article_routes

from app.api.v1.universal_search.search import (
    UniversalSearchHandler,
    ArticleSearchHandler,
    ProjectSearchHandler,
    SearchStatsHandler
)
from app.api.v1.ai_chat import (
    ChatHandler, ChatStreamHandler,
    SessionHandler, SessionListHandler, SessionDetailHandler,
    ModelListHandler, ModelConfigHandler, ModelProviderHandler,
    ToolsListHandler, MCPServerHandler, ToolsConfigHandler, ToolCallHandler
)
from app.api.v1.ai_chat.session import SessionClearHandler


def setup_routes() -> List[URLSpec]:
    """
    设置API路由
    
    Returns:
        List[URLSpec]: 路由处理器列表
    """
    # v1版本API路由
    v1_prefix = "/api/v1"
    v1_routes = [
        # 健康检查
        (f"{v1_prefix}/health", HealthHandler),
        
        # 认证相关
        (f"{v1_prefix}/auth/login", LoginHandler),
        (f"{v1_prefix}/auth/refresh", RefreshTokenHandler),
        (f"{v1_prefix}/auth/logout", LogoutHandler),
        (f"{v1_prefix}/users/verification-code", VerificationCodeHandler),

        (f"{v1_prefix}/auth/fastauth", FastAuthHandler),
        (f"{v1_prefix}/auth/fastauth-verification-code", FastAuthVerificationCodeHandler),

        (f"{v1_prefix}/auth/bind-change", BindChangeAuthHandler),
        (f"{v1_prefix}/auth/bind-change-verification-code", BindChangeAuthVerificationCodeHandler),

        (f"{v1_prefix}/auth/bind-change-original", BindChangeOriginalAuthHandler),
        (f"{v1_prefix}/auth/bind-change-verification-code-original", BindChangeAuthVerificationCodeOriginalHandler),

        # OAuth认证
        (f"{v1_prefix}/oauth/providers", OAuthProvidersHandler),
        (f"{v1_prefix}/oauth/authorize", OAuthAuthorizeHandler),
        (f"{v1_prefix}/oauth/callback/github", OAuthGithubCallbackHandler),
        (f"{v1_prefix}/oauth/callback/gitee", OAuthGiteeCallbackHandler),
        (f"{v1_prefix}/oauth/accounts", OAuthAccountsHandler),
        (f"{v1_prefix}/oauth/accounts/([^/]+)", OAuthAccountDeleteHandler),

        # auth 微信服务号
        # 微信获取验证码
        (f"{v1_prefix}/wechat/verify-code", WechatVerifyCodeHandler),

        # 微信服务器认证
        (f"{v1_prefix}/wechat/connect", WechatOfficialAccountAuthHandler),

        # 用户管理 注意users如果没有二级地址会统一适配成UserDetailHandler
        (f"{v1_prefix}/users", UserHandler),
        (f"{v1_prefix}/users/register", RegisterHandler),
        (f"{v1_prefix}/users/verification-code", VerificationCodeHandler),
        (f"{v1_prefix}/users/([^/]+)", UserDetailHandler),
        # (f"{v1_prefix}/users/([^/]+)/change-password", ChangePasswordHandler),
        (f"{v1_prefix}/users/([^/]+)/reset-password", ResetPasswordHandler),
        (f"{v1_prefix}/users/([^/]+)/status/([^/]+)", UserStatusHandler),
        (f"{v1_prefix}/users/([^/]+)/permissions", UserPermissionsHandler),

        # 为什么这里要重新实现一次？
        (f"{v1_prefix}/users/password-reset/request", PasswordResetRequestHandler),
        (f"{v1_prefix}/users/password-reset/complete", PasswordResetCompleteHandler),
        (f"{v1_prefix}/users/update-self-password/verification-code", UpdateSelfPasswordVerificationCodeHandler),
        (f"{v1_prefix}/users/update-self-password/complete", UpdateSelfPasswordHandler),
        (f"{v1_prefix}/users/update-oauth-email/verification-code", UpdateOauthEmailVerificationCodeHandler),
        (f"{v1_prefix}/users/update-oauth-email/complete", UpdateOauthEmailHandler),

        # 角色管理
        (f"{v1_prefix}/roles", RoleHandler),
        (f"{v1_prefix}/user-roles", UserRoleHandler),
        (f"{v1_prefix}/roles/([^/]+)", RoleDetailHandler),
        (f"{v1_prefix}/roles/([^/]+)/permission-groups", RolePermissionGroupHandler),
        (f"{v1_prefix}/roles/([^/]+)/permissions", RolePermissionHandler),
        
        # 权限管理
        (f"{v1_prefix}/permissions", PermissionHandler),
        (f"{v1_prefix}/permissions/([^/]+)", PermissionDetailHandler),
        
        # 权限组管理
        (f"{v1_prefix}/permission-groups", PermissionGroupHandler),
        (f"{v1_prefix}/permission-groups/([^/]+)", PermissionGroupDetailHandler),
        
        # 当前用户相关
        (f"{v1_prefix}/current-user", CurrentUserHandler),
        (f"{v1_prefix}/current-user/change-password", CurrentUserChangePasswordHandler),
        (f"{v1_prefix}/current-user/cards-search", GitHubCardSearchHandler),

        # Git仓库管理
        (f"{v1_prefix}/git/repository", GitRepositoryHandler),
        (f"{v1_prefix}/git/branches", GitBranchesHandler),
        (f"{v1_prefix}/git/commits", GitCommitsHandler),
        (f"{v1_prefix}/git/files", GitFilesHandler),
        (f"{v1_prefix}/git/file-content", GitFileContentHandler),
        (f"{v1_prefix}/git/file-history", GitFileHistoryHandler),
        (f"{v1_prefix}/git/find-repositories", GitFindRepositoriesHandler),

        # github下载和转换

        (f"{v1_prefix}/github/project", GitHubProjectDetailHandler),
        (f"{v1_prefix}/github/projects", GitHubProjectHandler),
        #混合搜索新 测试修改提交
        (f"{v1_prefix}/projects/mixed", MixedProjectArticleHandler),

        (f"{v1_prefix}/github/projects/generate", GitHubProjectCardGenerateHandler),
        (f"{v1_prefix}/github/projects/download", GitHubProjectDownloadHandler),
        (f"{v1_prefix}/github/projects/generate-cards", GitHubProjectCardGenerateHandler),
        (f"{v1_prefix}/github/projects/trees", GitHubProjectTreeHandler),
        (f"{v1_prefix}/github/projects/cards", GitHubCardHandler),
        (f"{v1_prefix}/github/projects/cards/detail", GitHubCardDetailHandler),
        (f"{v1_prefix}/github/projects/cards/batch", GitHubCardBatchHandler),  # 批量修改 先删再加
        (f"{v1_prefix}/github/projects/file", GitHubProjectFileHandler),
        (f"{v1_prefix}/github/projects/cards/action", GitHubCardInteractionHandler),
        (f"{v1_prefix}/github/upload/image", GitHubImageUploadHandler),
        (f"{v1_prefix}/github/projects/images", GitHubImageListHandler),

        # GitHub下载/AI生成统计API
        (f"{v1_prefix}/github/projects/dowloadstatus", GitHubDownloadStatusHandler),
        (f"{v1_prefix}/github/projects/generatestatus", GitHubGenerateStatusHandler),
        (f"{v1_prefix}/github/projects/needgenerateadder", GitHubAutoFinderHandler),
        (f"{v1_prefix}/github/projects/githubsolvedata", GitHubSolveDataHandler),

        (f"{v1_prefix}/github/validate-repo", RepoUrlValidationHandler),
        (f"{v1_prefix}/statistics", StatisticsHandler),
        (f"{v1_prefix}/statistics/admin", StatisticsAdminHandler),


        # 一个用户4个表 收藏/历史/流程 系统

        # github 收藏api
        (f"{v1_prefix}/github/projects/user-collect", ProjectUserCollectHandler),
        # github 流程日志api
        (f"{v1_prefix}/github/projects/user-flow-log", ProjectUserFlowLog),
        # github 浏览日志api
        (f"{v1_prefix}/github/projects/user-access-log", ProjectUserHistoryLog),
        # github 系统消息api 列表 已读 未读
        (f"{v1_prefix}/github/projects/system-message-log", CurrentUserSystemLogHandler),



        # github 分享api
        # 统计+1
        (f"{v1_prefix}/github/projects/share/statistics", GitHubShareHandler),
        # post修改 get获取
        (f"{v1_prefix}/github/projects/share/phone", GitHubPhoneShareSolveHandler),

        # 扫描项目流程 V
        (f"{v1_prefix}/githubflow/projects/scan", ProjectScanHandler),
        # 判断是否合法 post V
        (f"{v1_prefix}/githubflow/projects/validatelink", ProjectLinkCheckHandler),
        # 项目下载  post downloader中要带访问信息和访问人X
        (f"{v1_prefix}/githubflow/projects/normaldownload", ProjectDownloadHandler),
        # 项目加急 post V
        (f"{v1_prefix}/githubflow/projects/fastsolve", ProjectPriorityHandler),
        # 取消分析 post
        (f"{v1_prefix}/githubflow/projects/cancelgeneration", ProjectCancelGenerationHandler),
        # 获取下载-分析状态 get
        (f"{v1_prefix}/githubflow/projects/getflowstatus", ProjectGetFlowStatusHandler),
        # 获取下载-分析状态 get
        (f"{v1_prefix}/githubflow/projects/getflowstatus-current", ProjectGetFlowStatusCurrentUserHandler),

        # remote AI agent
        (f"{v1_prefix}/remote/agent/analyzer-called", AnalyzerCalledHandler),
        (f"{v1_prefix}/remote/agent/publisher-called", PublisherCalledHandler),
        (f"{v1_prefix}/remote/agent/health-check", AgentHealthCheckHandler),
        (f"{v1_prefix}/remote/agent/cancel-analysis", AnalyzerCancelHandler),
        # 获取某个用户的访问记录
        # 创建项目的时候记录
        # 可能要有专门的service
        # 先理清下载记录

        # 文章管理 - 添加文章相关路由
        (f"{v1_prefix}/articles", ArticleHandler),
        # (f"{v1_prefix}/articles/([^/]+)", ArticleHandler),
        (f"{v1_prefix}/articles/([^/]+)/like", ArticleLikeHandler),
        (f"{v1_prefix}/articles/([^/]+)/share", ArticleShareHandler),

        # 新增微信文章相关路由
        (f"{v1_prefix}/wechat/articles", WechatArticlesHandler),
        (f"{v1_prefix}/wechat/articles/([^/]+)/detail", WechatArticleDetailHandler),
        (f"{v1_prefix}/wechat/articles/stats", WechatArticlesStatsHandler),

        #获取评论树
        (f"{v1_prefix}/articles/comment/tree", ArticleCommentTreeHandler),
        (f"{v1_prefix}/articles/comment/create", ArticleCommentCreateHandler),
        (f"{v1_prefix}/articles/comment/audit", ArticleCommentAuditHandler),
        (f"{v1_prefix}/articles/comment/search", ArticleCommentSearchHandler),

        (f"{v1_prefix}/article/collect", ArticleCollectHandler),
        (f"{v1_prefix}/article/collect/check", ArticleCollectCheckHandler),


        (f"{v1_prefix}/articles/comment/delete", ArticleCommentDeleteHandler),



        # 通用搜索路由
        (f"{v1_prefix}/universal-search", UniversalSearchHandler),
        (f"{v1_prefix}/universal-search/articles", ArticleSearchHandler),
        (f"{v1_prefix}/universal-search/projects", ProjectSearchHandler),
        (f"{v1_prefix}/universal-search/stats", SearchStatsHandler),

        # AI对话服务路由
        (f"{v1_prefix}/ai-chat/chat", ChatHandler),
        (r"/ws/ai-chat/stream", ChatStreamHandler),

        # 会话管理路由
        (f"{v1_prefix}/ai-chat/sessions", SessionHandler),
        (f"{v1_prefix}/ai-chat/sessions/list", SessionListHandler),
        (f"{v1_prefix}/ai-chat/sessions/([^/]+)", SessionDetailHandler),
        (f"{v1_prefix}/ai-chat/sessions/([^/]+)/clear", SessionClearHandler),

        # 模型管理路由
        (f"{v1_prefix}/ai-chat/models", ModelListHandler),
        (f"{v1_prefix}/ai-chat/models/([^/]+)", ModelConfigHandler),
        (f"{v1_prefix}/ai-chat/providers", ModelProviderHandler),

        # 工具管理路由
        (f"{v1_prefix}/ai-chat/tools", ToolsListHandler),
        (f"{v1_prefix}/ai-chat/tools/config", ToolsConfigHandler),
        (f"{v1_prefix}/ai-chat/tools/([^/]+)/call", ToolCallHandler),
        (f"{v1_prefix}/ai-chat/mcp-servers", MCPServerHandler),

    ]



    upload_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'images')
    # 添加静态文件处理路由
    static_routes = [
        # 使用StaticFileHandler处理图片文件
        (r"/static/images/(.*)", CORSStaticFileHandler, {"path": upload_dir}),
    ]

    # 智能文章生成路由
    intelligent_article_routes = get_intelligent_article_routes()

    # 未来可以添加其他版本的API路由
    routes = [
        *v1_routes,
        *intelligent_article_routes,
    ]

    routes.extend(static_routes)

    # add 3/2
    urlspecs = []
    for route in routes:
        if len(route) == 2:
            pattern, handler = route
            urlspecs.append(URLSpec(pattern, handler))
        elif len(route) == 3:
            pattern, handler, kwargs = route
            urlspecs.append(URLSpec(pattern, handler, kwargs=kwargs))

    return urlspecs

    # 转换为URLSpec
    # return [URLSpec(pattern, handler) for pattern, handler in routes]
