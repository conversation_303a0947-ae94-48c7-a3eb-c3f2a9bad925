"""add system log => project_id 

Revision ID: 0a18bc8e9e57
Revises: 2b550d019b07
Create Date: 2025-08-16 10:39:00.048895

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '0a18bc8e9e57'
down_revision: Union[str, None] = '2b550d019b07'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # 添加 project_id 列到 system_logs 表
    op.add_column('system_logs', sa.Column('project_id', sa.String(48), nullable=True, default=None, comment='关联项目类型'))

def downgrade() -> None:
    """Downgrade schema."""
    # 删除 project_id 列从 system_logs 表
    op.drop_column('system_logs', 'project_id')