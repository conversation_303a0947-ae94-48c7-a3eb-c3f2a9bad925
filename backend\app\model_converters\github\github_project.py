#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/6/28 16:01
# @File    : github_project.py
# @Description: 
"""
"""
GitHub项目模型转换器
"""
from typing import Optional
from app.models.github.github_project import GitHubProjectModel
from app.schemas.github.github_project import GitHubProject
from ..base import BaseConverter


class GitHubProjectConverter(BaseConverter[GitHubProjectModel, GitHubProject]):
    """GitHub项目模型转换器"""

    def to_schema(self, model: GitHubProjectModel) -> GitHubProject:
        """将GitHub项目模型转换为schema

        Args:
            model: GitHub项目模型实例

        Returns:
            GitHub项目schema实例
        """
        # 转换基本属性
        model_dict = model.__dict__.copy()

        # # 移除 SQLAlchemy 内部属性
        # model_dict.pop('_sa_instance_state', None)
        #
        # # 确保 tags 字段是列表
        # if model_dict.get('tags') is None:
        #     model_dict['tags'] = []
        #
        # # 确保 is_priority 字段是布尔值
        if model_dict.get('is_priority') is None:
            model_dict['is_priority'] = False

        model_dict['content_type'] = "project"
        # 使用字典创建 Pydantic 模型
        project = GitHubProject.model_validate(model_dict)
        return project

    def to_model(self, schema: GitHubProject) -> GitHubProjectModel:
        """将GitHub项目schema转换为模型

        Args:
            schema: GitHub项目schema实例

        Returns:
            GitHub项目模型实例
        """
        # 转换基本属性
        project = GitHubProjectModel()
        for field in schema.model_fields:
            if hasattr(schema, field):
                value = getattr(schema, field)
                if value is not None:
                    setattr(project, field, value)

        return project
