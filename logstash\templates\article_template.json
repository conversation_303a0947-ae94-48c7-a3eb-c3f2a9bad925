{"index_patterns": ["articles-*"], "priority": 100, "template": {"settings": {"number_of_shards": 2, "number_of_replicas": 1, "refresh_interval": "3s", "max_result_window": 10000, "max_ngram_diff": 10, "mapping": {"total_fields": {"limit": 2000}}, "analysis": {"analyzer": {"ik_analyzer": {"type": "custom", "tokenizer": "ik_max_word", "filter": ["lowercase", "asciifolding", "stop", "synonym_filter"]}, "ik_search_analyzer": {"type": "custom", "tokenizer": "ik_smart", "filter": ["lowercase", "asciifolding", "synonym_filter"]}, "ik_precise_analyzer": {"type": "custom", "tokenizer": "ik_smart", "filter": ["lowercase", "asciifolding"]}, "english_analyzer": {"type": "custom", "tokenizer": "standard", "filter": ["lowercase", "asciifolding", "english_stop", "english_stemmer", "synonym_filter"]}, "english_search_analyzer": {"type": "custom", "tokenizer": "standard", "filter": ["lowercase", "asciifolding", "english_stop", "english_stemmer", "synonym_filter"]}, "ngram_analyzer": {"type": "custom", "tokenizer": "ik_max_word", "filter": ["lowercase", "asciifolding", "ngram_filter"]}, "completion_analyzer": {"type": "custom", "tokenizer": "keyword", "filter": ["lowercase", "asciifolding"]}}, "filter": {"ngram_filter": {"type": "ngram", "min_gram": 3, "max_gram": 8}, "synonym_filter": {"type": "synonym", "synonyms": ["文章,article,post,博客,blog", "教程,tutorial,guide,指南,攻略", "技术,technology,tech,科技", "开发,development,dev,编程,programming", "前端,frontend,front-end,客户端", "后端,backend,back-end,服务端", "全栈,fullstack,full-stack", "移动端,mobile,手机端,app", "网站,website,web,网页,站点", "应用,application,app,程序", "框架,framework,库,library", "工具,tool,utils,utility,插件", "项目,project,proj,作品", "开源,opensource,open-source", "示例,example,demo,演示,案例", "文档,document,doc,docs,说明", "新闻,news,资讯,动态", "分享,share,共享,交流", "经验,experience,心得,总结", "js,javascript,JavaScript,JS", "ts,typescript,TypeScript,TS", "py,python,Python,PY", "react,reactjs,React,ReactJS", "vue,vue<PERSON><PERSON>,<PERSON><PERSON>,VueJS", "api,interface,API,接口", "db,database,DB,数据库"]}, "english_stop": {"type": "stop", "stopwords": "_english_"}, "english_stemmer": {"type": "stemmer", "language": "english"}}}}, "mappings": {"properties": {"id": {"type": "keyword"}, "title": {"type": "text", "analyzer": "ik_analyzer", "search_analyzer": "ik_search_analyzer", "term_vector": "with_positions_offsets_payloads", "fields": {"keyword": {"type": "keyword"}, "ik": {"type": "text", "analyzer": "ik_analyzer", "search_analyzer": "ik_search_analyzer", "term_vector": "with_positions_offsets_payloads"}, "english": {"type": "text", "analyzer": "english_analyzer", "search_analyzer": "english_search_analyzer", "term_vector": "with_positions_offsets_payloads"}, "completion": {"type": "completion", "analyzer": "completion_analyzer"}, "ngram": {"type": "text", "analyzer": "ngram_analyzer"}, "suggest": {"type": "search_as_you_type"}}}, "content": {"type": "text", "analyzer": "ik_analyzer", "search_analyzer": "ik_search_analyzer", "term_vector": "with_positions_offsets_payloads", "fields": {"ik": {"type": "text", "analyzer": "ik_analyzer", "search_analyzer": "ik_search_analyzer", "term_vector": "with_positions_offsets_payloads"}, "english": {"type": "text", "analyzer": "english_analyzer", "search_analyzer": "english_search_analyzer", "term_vector": "with_positions_offsets_payloads"}, "ngram": {"type": "text", "analyzer": "ngram_analyzer"}}}, "summary": {"type": "text", "analyzer": "ik_analyzer", "search_analyzer": "ik_search_analyzer", "term_vector": "with_positions_offsets_payloads", "fields": {"ik": {"type": "text", "analyzer": "ik_analyzer", "search_analyzer": "ik_search_analyzer"}, "english": {"type": "text", "analyzer": "english_analyzer", "search_analyzer": "english_search_analyzer"}}}, "keywords": {"type": "text", "analyzer": "ik_analyzer", "search_analyzer": "ik_search_analyzer", "fields": {"keyword": {"type": "keyword"}}}, "tags": {"type": "keyword", "fields": {"ik": {"type": "text", "analyzer": "ik_analyzer", "search_analyzer": "ik_search_analyzer", "term_vector": "with_positions_offsets_payloads"}, "english": {"type": "text", "analyzer": "english_analyzer", "search_analyzer": "english_search_analyzer", "term_vector": "with_positions_offsets_payloads"}, "suggest": {"type": "completion", "analyzer": "completion_analyzer"}}}, "status": {"type": "keyword"}, "is_public": {"type": "boolean"}, "is_top": {"type": "boolean"}, "read_count": {"type": "integer"}, "comment_count": {"type": "integer"}, "like_count": {"type": "integer"}, "collect_count": {"type": "integer"}, "shared_count_link": {"type": "integer"}, "sort_order": {"type": "integer"}, "content_length": {"type": "integer"}, "word_count": {"type": "integer"}, "estimated_reading_time": {"type": "integer"}, "popularity_score": {"type": "integer"}, "interaction_rate": {"type": "float"}, "related_projects_count": {"type": "integer"}, "image_count": {"type": "integer"}, "shared_link": {"type": "keyword"}, "cover_image": {"type": "keyword"}, "created_at": {"type": "date"}, "updated_at": {"type": "date"}, "published_at": {"type": "date"}, "indexed_at": {"type": "date"}, "created_by": {"type": "keyword"}, "updated_by": {"type": "keyword"}, "related_projects": {"type": "keyword"}, "image_urls": {"type": "keyword"}, "title_suggest": {"type": "completion", "analyzer": "completion_analyzer", "search_analyzer": "completion_analyzer", "preserve_separators": true, "preserve_position_increments": true, "max_input_length": 50}}}}}