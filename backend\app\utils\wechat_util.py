#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/7/4 10:52
# @File    : WechatXmlUtil.py
# @Description: 微信XML消息处理工具类
"""

import xml.etree.ElementTree as ET
import hashlib
import time
import requests
import structlog
from typing import Dict, Any, Optional, Union

from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime

from app.core.config import settings
from app.models.github import GitHubProjectModel
from app.models.rbac.user import UserModel

logger = structlog.get_logger(__name__)


class WechatXmlUtil:
    """微信XML消息处理工具类"""

    # 微信配置参数
    # TOKEN = "shiyu666"
    # ENCODING_AES_KEY = "ZzBNJY9yUt07T2hSsIxO729K9z3a9oyHrLaRnglPjnD"
    # APP_ID = "wx68e5bb26923a081e"
    # APP_SECRET = "7107786e03a18ae2b1b1d84659c57946"

    TOKEN = settings.wechat_official.WEIXIN_OFFICIAL_TOKEN
    ENCODING_AES_KEY = settings.wechat_official.WEIXIN_OFFICIAL_ENCODING_AES_KEY
    APP_ID = settings.wechat_official.WEIXIN_OFFICIAL_APP_ID
    APP_SECRET = settings.wechat_official.WEIXIN_OFFICIAL_APP_SECRET

    @staticmethod
    def parse_xml(xml_string: str) -> Dict[str, Any]:
        """
        解析XML字符串，返回所有字段

        Args:
            xml_string: XML字符串

        Returns:
            Dict[str, Any]: 解析后的字段字典
        """
        try:
            logger.info("开始解析XML", xml_length=len(xml_string))

            if not xml_string.strip():
                logger.warning("XML字符串为空")
                return {}

            # 解析XML
            xml_tree = ET.fromstring(xml_string)

            # 提取所有字段
            result = {}
            for child in xml_tree:
                result[child.tag] = child.text

            logger.info("XML解析成功", fields=list(result.keys()), msg_type=result.get('MsgType'))
            return result

        except ET.ParseError as e:
            logger.error("XML解析失败", error=str(e), xml_preview=xml_string[:100])
            return {}
        except Exception as e:
            logger.error("解析XML时发生异常", error=str(e))
            return {}

    @staticmethod
    def build_text_reply(to_user: str, from_user: str, content: str) -> str:
        """
        构建文本消息回复XML

        Args:
            to_user: 接收者用户名
            from_user: 发送者用户名
            content: 消息内容

        Returns:
            str: XML格式的回复消息
        """
        try:
            current_time = int(time.time())

            xml_template = f"""<xml>
<ToUserName><![CDATA[{to_user}]]></ToUserName>
<FromUserName><![CDATA[{from_user}]]></FromUserName>
<CreateTime>{current_time}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[{content}]]></Content>
</xml>"""

            logger.info("构建文本回复成功", to_user=to_user, content_length=len(content))
            return xml_template

        except Exception as e:
            logger.error("构建文本回复失败", error=str(e))
            return ""

    @staticmethod
    async def send_official_msg_by_project_status(
            session: AsyncSession,
            project_id: str,
            success: bool
    ) -> bool:
        """
        根据项目状态向用户发送微信消息

        Args:
            session: 数据库会话
            project_id: 项目ID
            success: 项目处理是否成功

        Returns:
            bool: 消息发送是否成功
        """
        try:
            if not settings.distributed.is_publisher:
                logger.error("当前不是发布机模式，无法发送公众号消息")
                return False
            # 获取项目信息
            project = await session.get(GitHubProjectModel, project_id)
            if not project:
                logger.warning("项目不存在", project_id=project_id)
                return False

            # 获取用户信息
            user_id = project.created_by
            user = await session.get(UserModel, user_id)
            if not user:
                logger.warning("用户不存在", user_id=user_id, project_id=project_id)
                return False

            # 检查用户是否有微信openid
            if not user.wechat_openid:
                logger.warning("用户未绑定微信", user_id=user_id, project_id=project_id)
                return False

            # 构建消息内容
            if success:
                message_content = f"{project.name}已分析完成\n您可以登陆网站随时查看\n项目地址： http://58.34.173.69:80/broadcast/details?id={project.id}\n处理时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            else:
                message_content = f"{project.name}分析失败\n请联系客服管理员\n处理时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            if isinstance(message_content, bytes):
                message_content = message_content.decode('utf-8')

            logger.info("准备发送微信消息",
                        project_id=project_id,
                        user_id=user_id,
                        message_content=message_content)

            # 发送微信消息
            send_result = WechatXmlUtil.push_message(
                open_id=user.wechat_openid,
                msg_type="text",
                content=message_content
            )

            if send_result:
                logger.info(
                    "微信消息发送成功",
                    project_id=project_id,
                    user_id=user_id,
                    success=success,
                    openid=user.wechat_openid
                )
            else:
                logger.error(
                    "微信消息发送失败",
                    project_id=project_id,
                    user_id=user_id,
                    success=success,
                    openid=user.wechat_openid
                )

            return send_result

        except Exception as e:
            logger.error(
                "发送微信消息异常",
                project_id=project_id,
                success=success,
                error=str(e),
                exc_info=True
            )
            return False



    @staticmethod
    def build_image_reply(to_user: str, from_user: str, media_id: str) -> str:
        """
        构建图片消息回复XML

        Args:
            to_user: 接收者用户名
            from_user: 发送者用户名
            media_id: 媒体ID

        Returns:
            str: XML格式的回复消息
        """
        try:
            current_time = int(time.time())

            xml_template = f"""<xml>
<ToUserName><![CDATA[{to_user}]]></ToUserName>
<FromUserName><![CDATA[{from_user}]]></FromUserName>
<CreateTime>{current_time}</CreateTime>
<MsgType><![CDATA[image]]></MsgType>
<Image>
<MediaId><![CDATA[{media_id}]]></MediaId>
</Image>
</xml>"""

            logger.info("构建图片回复成功", to_user=to_user, media_id=media_id)
            return xml_template

        except Exception as e:
            logger.error("构建图片回复失败", error=str(e))
            return ""

    @staticmethod
    def build_news_reply(to_user: str, from_user: str, articles: list) -> str:
        """
        构建图文消息回复XML

        Args:
            to_user: 接收者用户名
            from_user: 发送者用户名
            articles: 文章列表

        Returns:
            str: XML格式的回复消息
        """
        try:
            current_time = int(time.time())

            articles_xml = ""
            for article in articles:
                articles_xml += f"""
<item>
<Title><![CDATA[{article.get('title', '')}]]></Title>
<Description><![CDATA[{article.get('description', '')}]]></Description>
<PicUrl><![CDATA[{article.get('pic_url', '')}]]></PicUrl>
<Url><![CDATA[{article.get('url', '')}]]></Url>
</item>"""

            xml_template = f"""<xml>
<ToUserName><![CDATA[{to_user}]]></ToUserName>
<FromUserName><![CDATA[{from_user}]]></FromUserName>
<CreateTime>{current_time}</CreateTime>
<MsgType><![CDATA[news]]></MsgType>
<ArticleCount>{len(articles)}</ArticleCount>
<Articles>{articles_xml}
</Articles>
</xml>"""

            logger.info("构建图文回复成功", to_user=to_user, article_count=len(articles))
            return xml_template

        except Exception as e:
            logger.error("构建图文回复失败", error=str(e))
            return ""

    @classmethod
    def verify_signature(cls, signature: str, timestamp: str, nonce: str, encrypted_msg: str = None) -> bool:
        """
        验证微信签名

        Args:
            signature: 微信签名
            timestamp: 时间戳
            nonce: 随机数
            encrypted_msg: 加密消息（安全模式需要）

        Returns:
            bool: 验证结果
        """
        try:
            # 构造验证字符串
            if encrypted_msg:
                # 安全模式
                verify_str = [cls.TOKEN, timestamp, nonce, encrypted_msg]
            else:
                # 明文模式
                verify_str = [cls.TOKEN, timestamp, nonce]

            # 排序并拼接
            verify_str.sort()
            verify_string = ''.join(verify_str)

            # SHA1加密
            sha1 = hashlib.sha1()
            sha1.update(verify_string.encode('utf-8'))
            hashcode = sha1.hexdigest()

            result = hashcode == signature
            logger.info("签名验证", result=result, signature=signature, hashcode=hashcode)
            return result

        except Exception as e:
            logger.error("签名验证失败", error=str(e))
            return False

    @classmethod
    def get_access_token(cls) -> Optional[str]:
        """
        获取微信接口调用的access_token

        Returns:
            Optional[str]: 成功返回access_token，失败返回None
        """
        try:
            url = f"https://api.weixin.qq.com/cgi-bin/token"
            params = {
                "grant_type": "client_credential",
                "appid": cls.APP_ID,
                "secret": cls.APP_SECRET
            }

            response = requests.get(url, params=params)
            result = response.json()

            if "access_token" in result:
                logger.info("获取access_token成功")
                return result["access_token"]
            else:
                logger.error("获取access_token失败", error=result.get("errmsg"))
                return None

        except Exception as e:
            logger.error("获取access_token异常", error=str(e))
            return None

    @classmethod
    def push_message(cls, open_id: str, msg_type: str, content: Union[str, Dict]) -> bool:
        """
        主动推送消息给用户 - 使用手动JSON序列化方法
        """
        try:
            import json

            # 获取access_token
            access_token = cls.get_access_token()
            if not access_token:
                logger.error("无法获取access_token，推送消息失败", open_id=open_id)
                return False

            # 构建请求URL
            url = f"https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token={access_token}"

            # 根据消息类型构建消息体
            message = {
                "touser": open_id,
                "msgtype": msg_type
            }

            if msg_type == "text":
                message["text"] = {"content": content}
            elif msg_type == "image":
                message["image"] = {"media_id": content}
            elif msg_type == "news":
                message["news"] = {"articles": content}

            logger.info("开始推送微信消息", open_id=open_id, msg_type=msg_type)

            # 手动序列化JSON - 这是关键修改
            json_data = json.dumps(message, ensure_ascii=False, separators=(',', ':'))
            headers = {'Content-Type': 'application/json; charset=utf-8'}

            # 发送请求
            response = requests.post(url, data=json_data.encode('utf-8'), headers=headers, timeout=10)
            result = response.json()

            if result.get("errcode") == 0:
                logger.info("消息推送成功", open_id=open_id, msg_type=msg_type)
                return True
            else:
                error_code = result.get("errcode")
                error_msg = result.get("errmsg")

                # 处理特定错误码
                if error_code == 45047:
                    logger.warning("超出回复次数限制，用户可能超过48小时未交互",
                                   open_id=open_id, error_code=error_code, error_msg=error_msg)
                elif error_code == 43004:
                    logger.warning("用户未关注公众号",
                                   open_id=open_id, error_code=error_code, error_msg=error_msg)
                elif error_code == 48001:
                    logger.error("API功能未授权，请检查服务号权限",
                                 open_id=open_id, error_code=error_code, error_msg=error_msg)
                else:
                    logger.error("消息推送失败",
                                 open_id=open_id, error_code=error_code, error_msg=error_msg)
                return False

        except Exception as e:
            logger.error("推送消息异常", error=str(e), open_id=open_id)
            return False

# 使用示例
if __name__ == "__main__":
    # 测试XML解析
    test_xml = """<xml>
<ToUserName><![CDATA[gh_123456789]]></ToUserName>
<FromUserName><![CDATA[o_123456789]]></FromUserName>
<CreateTime>1234567890</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[你好]]></Content>
<MsgId>123456789</MsgId>
</xml>"""

    # 解析XML
    result = WechatXmlUtil.parse_xml(test_xml)
    print("解析结果:", result)

    # 构建回复
    reply = WechatXmlUtil.build_text_reply(
        to_user=result['FromUserName'],
        from_user=result['ToUserName'],
        content="收到你的消息了！"
    )
    print("回复XML:", reply)

    # 测试推送消息
    WechatXmlUtil.push_message(
        open_id="orLE4vu2fcCQVYEwE0vkuCQplSLQ",
        msg_type="text",
        content="这是一条主动推送的消息"
    )