"""
通用结果处理器

处理Elasticsearch搜索结果，转换为统一的格式。
"""
import logging
from typing import Dict, Any, List, Optional
from .types import ContentItem, SearchResult, ContentType, SearchRequest

logger = logging.getLogger(__name__)


class UnifiedResultProcessor:
    """统一结果处理器"""
    
    def __init__(self):
        """初始化结果处理器"""
        pass
    
    def merge_results(self, 
                     results: List[Dict[str, Any]],
                     request: SearchRequest) -> SearchResult:
        """
        合并多个搜索结果
        
        Args:
            results: 各内容类型的搜索结果列表
            request: 搜索请求
            
        Returns:
            SearchResult: 合并后的搜索结果
        """
        all_items = []
        total_count = 0
        total_search_time = 0
        merged_aggregations = {}
        
        # 合并所有结果
        for result in results:
            if "items" in result:
                all_items.extend(result["items"])
            total_count += result.get("total", 0)
            total_search_time += result.get("search_time", 0)
            
            # 合并聚合结果
            if "aggregations" in result:
                for key, value in result["aggregations"].items():
                    if key not in merged_aggregations:
                        merged_aggregations[key] = value
                    else:
                        # 简单合并策略，可根据需要扩展
                        if isinstance(value, dict) and isinstance(merged_aggregations[key], dict):
                            merged_aggregations[key].update(value)
        
        # 排序所有项目
        all_items = self._sort_items(all_items, request)
        
        # 分页处理
        start_idx = (request.page - 1) * request.page_size
        end_idx = start_idx + request.page_size
        paginated_items = all_items[start_idx:end_idx]
        
        # 计算总页数
        total_pages = (len(all_items) + request.page_size - 1) // request.page_size if all_items else 0
        
        return SearchResult(
            items=paginated_items,
            total=len(all_items),
            page=request.page,
            page_size=request.page_size,
            total_pages=total_pages,
            search_time=total_search_time,
            aggregations=merged_aggregations,
            query=request.query or "",
            content_types=request.content_types
        )
    
    def _sort_items(self, items: List[ContentItem], request: SearchRequest) -> List[ContentItem]:
        """
        对内容项进行排序
        
        Args:
            items: 内容项列表
            request: 搜索请求
            
        Returns:
            List[ContentItem]: 排序后的内容项列表
        """
        if not items:
            return items
        
        sort_by = request.sort_by or "_score"
        reverse = request.sort_order.value == "desc"
        
        if sort_by == "_score":
            # 按分数排序
            return sorted(items, key=lambda x: x.score, reverse=reverse)
        elif sort_by == "title":
            # 按标题排序
            return sorted(items, key=lambda x: x.title.lower(), reverse=reverse)
        elif sort_by in ["created_at", "updated_at"]:
            # 按时间排序
            return sorted(items, 
                         key=lambda x: x.metadata.get(sort_by, ""), 
                         reverse=reverse)
        else:
            # 按其他字段排序
            return sorted(items, 
                         key=lambda x: x.metadata.get(sort_by, 0), 
                         reverse=reverse)
    
    def process_elasticsearch_result(self, 
                                   es_result: Dict[str, Any],
                                   content_type: ContentType) -> Dict[str, Any]:
        """
        处理单个Elasticsearch搜索结果
        
        Args:
            es_result: Elasticsearch原始结果
            content_type: 内容类型
            
        Returns:
            Dict[str, Any]: 处理后的结果
        """
        hits = es_result.get("hits", {})
        total_hits = hits.get("total", {}).get("value", 0)
        
        items = []
        for hit in hits.get("hits", []):
            try:
                item = self._convert_hit_to_content_item(hit, content_type)
                if item:
                    items.append(item)
            except Exception as e:
                logger.warning(f"转换搜索结果项失败: {str(e)}")
                continue
        
        return {
            "items": items,
            "total": total_hits,
            "search_time": es_result.get("took", 0),
            "aggregations": es_result.get("aggregations", {})
        }
    
    def _convert_hit_to_content_item(self, 
                                   hit: Dict[str, Any],
                                   content_type: ContentType) -> Optional[ContentItem]:
        """
        将Elasticsearch hit转换为ContentItem
        
        Args:
            hit: Elasticsearch hit对象
            content_type: 内容类型
            
        Returns:
            Optional[ContentItem]: 转换后的内容项
        """
        source = hit.get("_source", {})
        
        # 根据内容类型提取不同的字段
        if content_type == ContentType.ARTICLE:
            return self._convert_article_hit(hit, source)
        elif content_type == ContentType.PROJECT:
            return self._convert_project_hit(hit, source)
        else:
            logger.warning(f"未知的内容类型: {content_type}")
            return None
    
    def _convert_article_hit(self, hit: Dict[str, Any], source: Dict[str, Any]) -> ContentItem:
        """转换文章搜索结果"""
        return ContentItem(
            id=source.get("id", hit.get("_id", "")),
            content_type=ContentType.ARTICLE,
            title=source.get("title", ""),
            content=source.get("content", ""),
            score=hit.get("_score", 0.0),
            highlights=self._process_highlights(hit.get("highlight", {})),
            metadata={
                "summary": source.get("summary", ""),
                "tags": source.get("tags", []),
                "status": source.get("status", ""),
                "created_at": source.get("created_at", ""),
                "updated_at": source.get("updated_at", ""),
                "published_at": source.get("published_at", ""),
                "read_count": source.get("read_count", 0),
                "like_count": source.get("like_count", 0),
                "comment_count": source.get("comment_count", 0),
                "popularity_score": source.get("popularity_score", 0)
            }
        )
    
    def _convert_project_hit(self, hit: Dict[str, Any], source: Dict[str, Any]) -> ContentItem:
        """转换项目搜索结果"""
        # 合并项目描述作为内容
        content_parts = []
        if source.get("description_project"):
            content_parts.append(source["description_project"])
        if source.get("description_recommend"):
            content_parts.append(source["description_recommend"])
        
        content = " | ".join(content_parts)
        
        return ContentItem(
            id=source.get("id", hit.get("_id", "")),
            content_type=ContentType.PROJECT,
            title=source.get("name", ""),
            content=content,
            score=hit.get("_score", 0.0),
            highlights=self._process_highlights(hit.get("highlight", {})),
            metadata={
                "description_project": source.get("description_project", ""),
                "description_recommend": source.get("description_recommend", ""),
                "tags": source.get("tags", []),
                "status": source.get("status", ""),
                "created_at": source.get("created_at", ""),
                "updated_at": source.get("updated_at", ""),
                "cards_count": source.get("cards_count", 0),
                "likes_count": source.get("likes_count", 0),
                "views_count": source.get("views_count", 0),
                "cards": source.get("cards", []),
                "matching_cards": hit.get("inner_hits", {}).get("cards", {}).get("hits", {}).get("hits", [])
            }
        )
    
    def _process_highlights(self, highlight_data: Dict[str, List[str]]) -> Dict[str, Any]:
        """
        处理高亮信息
        
        Args:
            highlight_data: Elasticsearch高亮数据
            
        Returns:
            Dict[str, Any]: 处理后的高亮信息
        """
        if not highlight_data:
            return {}
        
        processed_highlights = []
        
        for field, fragments in highlight_data.items():
            # 过滤掉N-gram字段的高亮
            if field.endswith(".ngram"):
                continue
            
            # 确定字段类型
            field_type = self._get_field_type(field)
            
            highlight_item = {
                "field_type": field_type,
                "field_name": field,
                "fragments": fragments,
                "fragment_count": len(fragments)
            }
            
            processed_highlights.append(highlight_item)
        
        return {
            "highlights": processed_highlights,
            "total_matches": len(processed_highlights),
            "total_fragments": sum(h["fragment_count"] for h in processed_highlights)
        }
    
    def _get_field_type(self, field_name: str) -> str:
        """
        根据字段名确定字段类型
        
        Args:
            field_name: 字段名
            
        Returns:
            str: 字段类型描述
        """
        field_type_mapping = {
            "title": "标题",
            "title.ik": "标题",
            "title.english": "标题",
            "name": "名称",
            "name.ik": "名称", 
            "name.english": "名称",
            "content": "内容",
            "content.ik": "内容",
            "content.english": "内容",
            "summary": "摘要",
            "summary.ik": "摘要",
            "summary.english": "摘要",
            "description_project": "项目描述",
            "description_project.ik": "项目描述",
            "description_project.english": "项目描述",
            "description_recommend": "推荐描述",
            "description_recommend.ik": "推荐描述",
            "description_recommend.english": "推荐描述",
            "tags": "标签",
            "tags.ik": "标签",
            "tags.english": "标签",
            "keywords": "关键词"
        }
        
        return field_type_mapping.get(field_name, field_name)
