<template>
  <div
    class="card"
    :data-backgroundColor="
      gray ? '#e1e1e1' : project[getPropKey.background_color] || '#ffe6e6'
    "
    :style="`background-color: ${
      gray ? '#e1e1e1' : project[getPropKey.background_color] || '#ffe6e6'
    };--backgroundColor: ${gray ? '#e1e1e1' : project[getPropKey.background_color] || '#ffe6e6'};width: ${width};height: ${height};`"
    v-preventLongPress="() => handlePreventLongPress()">
    <img
      v-if="project.content_type === 'article'"
      class="markicon"
      src="@/assets/images/icon30.png"
      alt="" />
    <div class="main-cover-wrap">
      <LazyImg
        v-if="coverImage"
        class="main-cover"
        :url="coverImage"
        @load="imageLoad"
        @error="imageError"
        @success="imageSuccess"></LazyImg>
      <div v-else class="main-cover main-cover1">
        <div class="text">{{ project[getPropKey.title] }}</div>
      </div>
      <div class="favorite-wrap">
        <div
          class="meta-favorite"
          @click.stop="handleCollect"
          @mousedown.stop
          @mouseup.stop>
          <Button1
            class="meta-favorite-icon"
            :filled="project[getPropKey.is_collected]"></Button1>
          {{ formatStar(project[getPropKey.collect_count]) || "--" }}
        </div>
      </div>
    </div>
    <div class="card-meta">
      <div class="meta-name" :title="project[getPropKey.title]">
        {{ project[getPropKey.title] }}
      </div>
      <div class="meta-stars">
        <img class="meta-stars-icon" src="@/assets/images/icon26.png" alt="" />
        {{ formatStar(project[getPropKey.read_count]) || "--" }}
      </div>
    </div>
    <p class="card-text" :title="project[getPropKey.summary]">
      {{ project[getPropKey.summary] }}
    </p>
    <div class="time" v-if="time">
      {{ dayjs(time).format("YYYY-MM-DD HH:mm") }}
    </div>
  </div>
</template>

<script setup>
import { formatStar } from "@/utils";
import { addUserCollect, deleteUserCollect } from "@/api/broadcast.js";
import preventLongPress from "@/directive/common/preventLongPress";
import useUserStore from "@/store/modules/user";
import { LazyImg } from "vue-waterfall-plugin-next";
import Button1 from "@/components/Button/index1.vue";
import dayjs from "dayjs";
import { watch } from "vue";
const { proxy } = getCurrentInstance();
const userStore = useUserStore();
defineOptions({
  directives: {
    preventLongPress,
  },
});
const props = defineProps({
  project: {
    type: Object,
    default: () => ({}),
  },
  width: {
    type: [Number, String],
    default: "100%",
  },
  height: {
    type: [Number, String],
    default: "auto",
  },
  time: {
    type: [Number, String],
    default: "",
  },
  // 是否使用灰色样式
  gray: {
    type: Boolean,
    default: false,
  },
});
const getPropKey = computed(() => {
  if (props.project.content_type === "project") {
    return {
      id: "id",
      title: "name",
      summary: "description_recommend",
      read_count: "read_count",
      is_collected: "is_collected",
      collect_count: "collect_count",
      comment_count: "comment_count",
      cover_image: "image_url",
      background_color: "background_color",
      button_color: "button_color",
    };
  } else {
    return {
      id: "id",
      title: "title",
      summary: "summary",
      read_count: "read_count",
      is_collected: "is_collected",
      collect_count: "collect_count",
      comment_count: "comment_count",
      cover_image: "cover_image",
      background_color: "background_color",
      button_color: "button_color",
    };
  }
});
let coverImage = ref("");
watch(
  () => props.project[getPropKey.value.cover_image],
  (newUrl) => {
    coverImage.value = newUrl;
  },
  { immediate: true }
);
const emit = defineEmits();
const handlePreventLongPress = () => {
  emit("toDetails", props.project);
};
const handleCollect = async () => {
  // 判断用户是否登录，如果为登录，则提示用户去登录
  if (!userStore?.name) {
    proxy.$modal.msgWarning("登录后即可收藏");
    return;
  }
  let res = {};
  if (props.project[getPropKey.value.is_collected]) {
    let params = {};
    if (props.project.content_type === "article") {
      params.article_id = props.project[getPropKey.value.id];
    } else {
      params.project_id = props.project[getPropKey.value.id];
    }
    res = await deleteUserCollect(params);
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已取消收藏");
      props.project[getPropKey.value.is_collected] =
        !props.project[getPropKey.value.is_collected];
      props.project[getPropKey.value.collect_count]--;
      // 通知父元素收藏操作成功
      emit("collectSuccess", "delete");
    }
  } else {
    let params = {};
    if (props.project.content_type === "article") {
      params.article_id = props.project[getPropKey.value.id];
    } else {
      params.project_id = props.project[getPropKey.value.id];
    }
    res = await addUserCollect(params);
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已收藏");
      props.project[getPropKey.value.is_collected] =
        !props.project[getPropKey.value.is_collected];
      props.project[getPropKey.value.collect_count]++;
      // 通知父元素收藏操作成功
      emit("collectSuccess", "add");
    }
  }
  // 保存收藏变更的数据，方便回到首页时同步
  let pathname = window.location.pathname;
  if (pathname === "/broadcast/index") {
    return;
  }
  let collectChangeData = sessionStorage.getItem("collectChangeData");
  if (collectChangeData?.length > 0) {
    collectChangeData = JSON.parse(collectChangeData);
    // 判断是否已经存在
    let existIndex = -1;
    for (let i = 0, l = collectChangeData.length; i < l; i++) {
      if (
        collectChangeData[i][props.project[getPropKey.value.id]] ===
        props.project[getPropKey.value.id]
      ) {
        existIndex = i;
        break;
      }
    }
    if (existIndex > -1) {
      collectChangeData[existIndex] = {
        [props.project[getPropKey.value.id]]: [
          props.project[getPropKey.value.is_collected],
          props.project[getPropKey.value.collect_count],
        ],
      };
    } else {
      collectChangeData.push({
        [props.project[getPropKey.value.id]]: [
          props.project[getPropKey.value.is_collected],
          props.project[getPropKey.value.collect_count],
        ],
      });
    }

    sessionStorage.setItem(
      "collectChangeData",
      JSON.stringify(collectChangeData)
    );
  } else {
    sessionStorage.setItem(
      "collectChangeData",
      JSON.stringify([
        {
          [props.project[getPropKey.value.id]]: [
            props.project[getPropKey.value.is_collected],
            props.project[getPropKey.value.collect_count],
          ],
        },
      ])
    );
  }
};
function imageLoad(url) {
  // console.log(`${url}: 加载完成`)
}

function imageError(url) {
  // console.error(`${url}: 加载失败`);
  coverImage.value = "";
}

function imageSuccess(url) {
  // console.log(`${url}: 加载成功`)
}
</script>

<style scoped lang="scss">
.card {
  cursor: pointer;
  padding: 10px 10px 0px 10px;
  border-radius: 16px;
  border-bottom: solid 0px #000;
  position: relative;
  margin-top: 10px;
  // filter: drop-shadow(0 0 4px rgba(10, 10, 10, 0.1));
  &::before {
    content: "";
    border-radius: 16px;
    position: absolute;
    top: -10px;
    left: 0;
    width: 100%;
    height: 60px;
    z-index: -1;
    background-color: var(--backgroundColor);
    // background-color: #ffe6e6;
    transform: skewY(2deg);
    transform-origin: bottom left;
  }
  &::after {
    content: "";
    border-radius: 16px;
    position: absolute;
    bottom: -14px;
    left: 0;
    width: 100%;
    height: 60px;
    z-index: -1;
    background-color: var(--backgroundColor);
    // background-color: #ffe6e6;
    transform: skewY(4deg);
    transform-origin: bottom left;
  }
  .markicon {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 24px;
    height: 24px;
    z-index: 1;
  }
  .main-cover-wrap {
    width: 100%;
    overflow: hidden;
    border: solid 4px #fff;
    position: relative;
    .el-image {
      transition: transform 0.7s;
      &:hover {
        transform: scale(1.2);
      }
    }
    .favorite-wrap {
      position: absolute;
      bottom: 0px;
      right: 0px;
      left: 0px;
      height: 20px;
      background: linear-gradient(
        180deg,
        rgba(164, 164, 164, 0) 0%,
        rgba(0, 0, 0, 0.5) 100%
      );
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .meta-favorite {
        padding: 0 4px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-size: 12px;
        color: #fff;
        height: 100%;
        width: fit-content;
        .meta-favorite-icon {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
      }
    }
  }
  .card-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 10px 10px;
    color: #656464;
    .meta-name {
      font-size: 14px;
      font-weight: bold;
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .meta-stars {
      display: flex;
      align-items: center;
      font-size: 12px;
      .meta-stars-icon {
        width: 16px;
        height: 16px;
        margin-left: 10px;
        margin-right: 4px;
      }
    }
  }
  .card-tags {
    color: #606266;
    margin: 10px 10px;
    display: flex;
    align-items: center;
    font-size: 12px;
    .tag-label {
      color: #60848d;
    }
    .tag-item {
      display: inline-block;
      background-color: #d9ecf0;
      color: #4c6d7d;
      padding: 0px 6px 2px;
      border-radius: 4px;
      height: 24px;
      display: flex;
      align-items: center;
      white-space: nowrap;
      & + .tag-item {
        margin-left: 4px;
      }
    }
  }
  .main-cover {
    width: 100%;
    display: block;
    transition: transform 0.7s;
    &:hover {
      transform: scale(1.2);
    }
    &.main-cover1 {
      background-color: #f3efee;
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
      .text {
        font-size: 18px;
        width: 80%;
        max-height: 70%;
        text-align: center;
        color: #1d1c1c;

        display: -webkit-box;
        -webkit-box-orient: vertical;
        line-clamp: 3;
        -webkit-line-clamp: 3; /* 限制显示的行数 */
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  .card-text {
    font-size: 14px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    line-clamp: 6;
    -webkit-line-clamp: 6; /* 限制显示的行数 */
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 10px 10px;
    color: #656464;
  }
  .time {
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 10px 10px;
    color: #656464;
  }
  .arrow-wrap {
    width: 30px;
    height: 30px;
    background-color: aqua;
    border-radius: 50%;
    position: absolute;
    left: 50%;
    bottom: -72px;
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(0.6);
    .arrow {
      width: 20px;
      height: 20px;
      display: block;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateX(-50%), translateY(-50%);
    }
  }
}
@media screen and (max-width: 768px) {
  .card {
    .card-meta {
      .meta-name {
        font-weight: normal;
      }
    }
  }
}
:deep(.lazy__img[lazy="error"]) {
  width: 100% !important;
}
// 定义rotate
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
:deep(.lazy__img[lazy="loading"]) {
  width: 40% !important;
  // 让这个图片元素一直旋转
  animation: rotate 3s linear infinite;
}
</style>
