<template>
  <div
    class="comment"
    v-for="(item, index) in props.data"
    :key="index"
    :style="{
      padding: !item.parent_id
        ? device == 'mobile'
          ? '0'
          : '12px 10px'
        : ' 12px 0 0 0',
    }">
    <div class="comment-cont">
      <div class="comment-top">
        <div style="display: flex; align-items: center">
          <img class="avatar" src="@/assets/images/profile.png" />
          <div class="name">
            <span>{{ item.user_name }}</span>
            <template v-if="sendName">
              <template v-if="item.parent_id === item.root_id"></template>
              <template v-else>
                <span style="margin: 0 5px">回复</span>
                <span>{{ props.sendName }}</span>
              </template>
            </template>
          </div>
        </div>
        <div style="font-size: 14px; color: #656464">
          {{ getLastTimeStr(item.updated_at, true) }}
        </div>
      </div>
      <div class="comment-bottom">
        <div
          :class="{
            'mobile-comment-bottom': device === 'mobile',
          }"
          style="display: flex; align-items: flex-end">
          <div
            v-html="highlightWord(item.content)"
            style="max-width: 90%"></div>
          <template v-if="device === 'mobile'">
            <div class="mobile-operate">
              <span class="operate" @click="handleReply(item.id)">
                {{ userStore.replyName !== item.id ? "回复" : "" }}
              </span>
              <span
                class="operate"
                v-show="userStore.replyName !== item.id"
                @click="handleDelete(item.id)">
                {{ userStore.id === item.user_id ? "删除" : "" }}
              </span>
            </div>
          </template>
          <template v-else>
            <span class="operate" @click="handleReply(item.id)">
              {{ userStore.replyName !== item.id ? "回复" : "" }}
            </span>
            <span
              class="operate"
              v-show="userStore.replyName !== item.id"
              @click="handleDelete(item.id)">
              {{ userStore.id === item.user_id ? "删除" : "" }}
            </span>
          </template>
        </div>
        <div
          v-if="userStore.replyName === item.id"
          style="position: relative; width: 100%">
          <el-input
            ref="textareaRef"
            class="textarea"
            v-model="textarea"
            autofocus
            :rows="3"
            maxlength="1000"
            type="textarea"
            show-word-limit
            :placeholder="`回复 ${item.user_name}`" />
          <!-- <img
            class="emoji"
            src="@/assets/images/emoji.png"
            @click.stop="handleEmoji" /> -->
          <img
            class="emoji"
            src="@/assets/images/guguda.png"
            style="right: 100px"
            @click.stop="handleAiTe" />
          <el-popover placement="bottom" :width="306" :offset="30">
            <div
              ref="emojiPicker"
              :class="{
                'emoji-picker': device === 'pc',
                'mobile-emoji-picker': device === 'mobile',
              }">
              <EmojiPicker
                :hide-search="true"
                :native="true"
                @select="onSelectEmoji"
                :disabled-groups="[
                  'travel_places',
                  'flags',
                  'symbols',
                  'objects',
                  'activities',
                ]"
                :hide-group-names="true" />
            </div>
            <template #reference>
              <img
                class="emoji"
                src="@/assets/images/emoji.png"
                @click.stop="handleEmoji" />
            </template>
          </el-popover>
          <div class="btns">
            <button class="cancel" @click.stop="handleCancel">取消</button>
            <button @click.stop="handleSend(item.id)">发送</button>
          </div>
        </div>
      </div>
    </div>
    <div
      style="margin-left: 35px; margin-top: 5px"
      :style="{
        marginLeft: !item.parent_id ? '35px' : 0,
      }">
      <comment
        :data="item.replies"
        :sendName="item.user_name"
        :articleId="props.articleId"></comment>
    </div>
  </div>
</template>

<script setup>
import EmojiPicker from "vue3-emoji-picker";
import "vue3-emoji-picker/css";
import { commentCreat, commentDelete } from "@/api/article";
import { getLastTimeStr } from "@/utils/validate";
import useUserStore from "@/store/modules/user";
import useAppStore from "@/store/modules/app";
import { getCurrentInstance, ref } from "vue";
import { ElMessage } from "element-plus";
import { getToken } from "@/utils/auth";
const visible = ref(false);
const device = computed(() => useAppStore().device);
const textareaRef = useTemplateRef("textareaRef");
const userStore = useUserStore();
defineOptions({
  name: "comment",
});

const textarea = ref("");
const props = defineProps({
  data: {
    type: Object,
    default: [],
  },
  sendName: {
    type: String,
    default: "",
  },
  articleId: {
    type: String,
    default: "",
  },
});

const router = useRouter();
const handleReply = (id) => {
  showEmoji.value = false;
  if (getToken()) {
    userStore.replyName = id;
    userStore.showCommentInput = false;
    textarea.value = "";
    nextTick(() => {
      textareaRef.value[0].focus();
    });
  } else {
    ElMessage({
      message: "请登录后评论",
      type: "warning",
    });
    if (device.value === "mobile") {
      router.push("/mobileLogin");
    } else {
      userStore.loginDialogVisible = true;
    }
  }
};
const highlightWord = (str, word = "@咕咕答") => {
  return str.replaceAll(word, '<span class="hignlight">@咕咕答</span>');
};
const { proxy } = getCurrentInstance();
const handleDelete = (id) => {
  commentDelete({
    comment_id: id,
  }).then((res) => {
    if (res.code === 200) {
      proxy.$modal.msgSuccess(`删除成功`);
      userStore.refreshComment = true;
      const timer = setTimeout(() => {
        userStore.refreshComment = false;
        clearTimeout(timer);
      }, 1000);
    }
  });
};

const handleSend = (parent_id) => {
  showEmoji.value = false;
  commentCreat({
    content: textarea.value,
    parent_id,
    project_id: props.articleId,
  }).then((res) => {
    if (res.code === 200) {
      userStore.replyName = "";
      userStore.refreshComment = true;
      const timer = setTimeout(() => {
        userStore.refreshComment = false;
        clearTimeout(timer);
      }, 1000);
    }
  });
};

const handleCancel = () => {
  userStore.replyName = "";
  textarea.value = "";
  showEmoji.value = false;
};

const onSelectEmoji = (emoji) => {
  textarea.value = textarea.value + emoji.i;
  textareaRef.value[0].focus();
};
const showEmoji = ref(false);
const handleEmoji = () => {
  visible.value = !visible.value;
  showEmoji.value = !showEmoji.value;
  textareaRef.value[0].focus();
};

const handleAiTe = () => {
  textarea.value = textarea.value + "@咕咕答";
};
</script>
<style scoped lang="scss">
::v-deep(.v3-emoji-picker .v3-footer) {
  display: none;
}
::v-deep(.v3-emoji-picker .v3-header) {
  display: none;
}
::v-deep(.v3-emoji-picker) {
  box-shadow: none;
}
.comment {
  .comment-top {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .avatar {
      width: 30px;
      height: 30px;
    }

    .name {
      margin-left: 5px;
      font-size: 12px;
      color: #000000;
      font-weight: 800;
    }
  }

  .comment-bottom {
    margin-left: 35px;
    font-size: 12px;
    color: #000000;

    ::v-deep(.hignlight) {
      color: #0a78b2;
      margin: 0 2px;
    }

    .operate {
      margin-left: 15px;
      font-size: 12px;
      color: #0a78b2;
      cursor: pointer;
      visibility: hidden;
    }

    .emoji-picker {
      position: absolute;
      top: 70px;
      left: 50%;
    }

    .mobile-emoji-picker {
      position: absolute;
      top: 110px;
      right: 0px;
    }

    .emoji {
      width: 20px;
      position: absolute;
      top: 45px;
      right: 70px;
      cursor: pointer;
    }

    .textarea {
      width: 100%;
      margin-top: 5px;
      border-radius: 5px;
      font-size: 12px;
    }

    .btns {
      display: flex;
      flex-direction: row-reverse;

      button {
        width: 60px;
        height: 32px;
        margin-top: 5px;
        border-radius: 4px;
        background-color: #1e80ff;
        color: #ffffff;
        text-align: center;
        line-height: 32px;
        border: none;
        cursor: pointer;
      }

      .cancel {
        background-color: #fff;
        color: #1e80ff;
        margin-left: 8px;
        box-shadow: 0 0 0 1px #1e80ff;
      }
    }
  }

  .mobile-comment-bottom {
    display: flex;
    justify-content: space-between;

    & > div:first-child {
      flex: 1;
    }

    .mobile-operate {
      width: fit-content;
      display: flex;
      align-items: flex-end;

      .operate {
        visibility: visible;
      }
    }
  }

  .comment-cont:hover {
    .comment-bottom span {
      visibility: visible !important;
    }
  }
}
</style>
