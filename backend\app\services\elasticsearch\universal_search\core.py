"""
通用搜索引擎核心

提供统一的搜索接口，支持多种内容类型的搜索。
"""
import logging
from typing import Dict, Any, List, Optional, Union
from app.services.elasticsearch.client import ElasticsearchClient
from .types import SearchRequest, SearchResult, ContentType, SortOrder
from .coordinator import SearchCoordinator
from .adapters import ArticleAdapter, ProjectAdapter
from .utils import SearchValidationUtils

logger = logging.getLogger(__name__)


class UniversalSearchEngine:
    """通用搜索引擎"""
    
    def __init__(self, es_client: ElasticsearchClient = None):
        """
        初始化通用搜索引擎
        
        Args:
            es_client: Elasticsearch客户端，如果为None则创建新实例
        """
        self.es_client = es_client or ElasticsearchClient()
        self.coordinator = SearchCoordinator(self.es_client)
        self.validation_utils = SearchValidationUtils()
        
        # 注册默认适配器
        self._register_default_adapters()
        
        # 搜索统计信息
        self.stats = {
            "search_count": 0,
            "last_search_at": None,
            "content_type_stats": {}
        }
    
    def _register_default_adapters(self):
        """注册默认的内容类型适配器"""
        # 注册文章适配器
        article_adapter = ArticleAdapter(self.es_client)
        self.coordinator.register_adapter(article_adapter)
        
        # 注册项目适配器
        project_adapter = ProjectAdapter(self.es_client)
        self.coordinator.register_adapter(project_adapter)
        
        logger.info("已注册默认内容类型适配器: 文章、项目")
    
    async def search(self,
                    query: str = None,
                    content_types: Union[str, List[str]] = "all",
                    filters: Dict[str, Any] = None,
                    sort_by: str = None,
                    sort_order: str = "desc",
                    page: int = 1,
                    page_size: int = 10,
                    highlight: bool = True) -> Dict[str, Any]:
        """
        执行统一搜索
        
        Args:
            query: 搜索关键词
            content_types: 内容类型，可以是字符串或列表
            filters: 过滤条件
            sort_by: 排序字段
            sort_order: 排序方向
            page: 页码
            page_size: 每页数量
            highlight: 是否高亮
            
        Returns:
            Dict[str, Any]: 搜索结果
        """
        try:
            from datetime import datetime
            
            # 更新搜索统计
            self.stats["search_count"] += 1
            self.stats["last_search_at"] = datetime.now().isoformat()
            
            # 构建搜索请求
            search_request = self._build_search_request(
                query, content_types, filters, sort_by, sort_order, page, page_size, highlight
            )
            
            # 验证搜索请求
            search_request = self.validation_utils.validate_search_request(search_request)
            
            # 执行搜索
            result = await self.coordinator.search(search_request)
            
            # 更新内容类型统计
            for content_type in search_request.content_types:
                type_key = content_type.value
                if type_key not in self.stats["content_type_stats"]:
                    self.stats["content_type_stats"][type_key] = 0
                self.stats["content_type_stats"][type_key] += 1
            
            # 转换为字典格式
            return result.to_dict()
            
        except Exception as e:
            logger.error(f"统一搜索执行失败: {str(e)}", exc_info=True)
            return self._empty_search_result(query, content_types, page, page_size)
    
    async def search_articles(self,
                             query: str = None,
                             filters: Dict[str, Any] = None,
                             sort_by: str = None,
                             sort_order: str = "desc",
                             page: int = 1,
                             page_size: int = 10,
                             highlight: bool = True) -> Dict[str, Any]:
        """
        搜索文章（兼容性方法）
        
        Args:
            query: 搜索关键词
            filters: 过滤条件
            sort_by: 排序字段
            sort_order: 排序方向
            page: 页码
            page_size: 每页数量
            highlight: 是否高亮
            
        Returns:
            Dict[str, Any]: 文章搜索结果
        """
        result = await self.search(
            query=query,
            content_types=["article"],
            filters=filters,
            sort_by=sort_by,
            sort_order=sort_order,
            page=page,
            page_size=page_size,
            highlight=highlight
        )
        
        # 转换为兼容格式
        return {
            "articles": result.get("items", []),
            "total": result.get("total", 0),
            "page": result.get("page", page),
            "page_size": result.get("page_size", page_size),
            "total_pages": result.get("total_pages", 0),
            "search_time": result.get("search_time", 0),
            "aggregations": result.get("aggregations", {}),
            "query": result.get("query", ""),
            "sort_by": sort_by,
            "sort_order": sort_order
        }
    
    async def search_projects(self,
                             query: str = None,
                             filters: Dict[str, Any] = None,
                             sort_by: str = None,
                             sort_order: str = "desc",
                             page: int = 1,
                             page_size: int = 10,
                             highlight: bool = True) -> Dict[str, Any]:
        """
        搜索项目（兼容性方法）
        
        Args:
            query: 搜索关键词
            filters: 过滤条件
            sort_by: 排序字段
            sort_order: 排序方向
            page: 页码
            page_size: 每页数量
            highlight: 是否高亮
            
        Returns:
            Dict[str, Any]: 项目搜索结果
        """
        result = await self.search(
            query=query,
            content_types=["project"],
            filters=filters,
            sort_by=sort_by,
            sort_order=sort_order,
            page=page,
            page_size=page_size,
            highlight=highlight
        )
        
        # 转换为兼容格式
        return {
            "projects": result.get("items", []),
            "total": result.get("total", 0),
            "page": result.get("page", page),
            "page_size": result.get("page_size", page_size),
            "total_pages": result.get("total_pages", 0),
            "search_time": result.get("search_time", 0),
            "aggregations": result.get("aggregations", {}),
            "query": result.get("query", ""),
            "sort_by": sort_by,
            "sort_order": sort_order
        }
    
    def _build_search_request(self,
                             query: str,
                             content_types: Union[str, List[str]],
                             filters: Dict[str, Any],
                             sort_by: str,
                             sort_order: str,
                             page: int,
                             page_size: int,
                             highlight: bool) -> SearchRequest:
        """构建搜索请求对象"""
        # 处理内容类型
        if isinstance(content_types, str):
            if content_types.lower() == "all":
                content_types_list = [ContentType.ALL]
            else:
                try:
                    content_types_list = [ContentType(content_types.lower())]
                except ValueError:
                    content_types_list = [ContentType.ALL]
        else:
            content_types_list = []
            for ct in content_types:
                try:
                    content_types_list.append(ContentType(ct.lower()))
                except ValueError:
                    logger.warning(f"无效的内容类型: {ct}")
        
        # 处理排序方向
        try:
            sort_order_enum = SortOrder(sort_order.lower())
        except ValueError:
            sort_order_enum = SortOrder.DESC
        
        return SearchRequest(
            query=query,
            content_types=content_types_list,
            filters=filters,
            sort_by=sort_by,
            sort_order=sort_order_enum,
            page=page,
            page_size=page_size,
            highlight=highlight
        )
    
    def _empty_search_result(self,
                           query: str,
                           content_types: Union[str, List[str]],
                           page: int,
                           page_size: int) -> Dict[str, Any]:
        """创建空搜索结果"""
        return {
            "items": [],
            "total": 0,
            "page": page,
            "page_size": page_size,
            "total_pages": 0,
            "search_time": 0,
            "aggregations": {},
            "query": query or "",
            "content_types": content_types if isinstance(content_types, list) else [content_types],
            "error": "搜索执行失败"
        }
    
    async def get_statistics(self) -> Dict[str, Any]:
        """
        获取搜索统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            registered_types = [ct.value for ct in self.coordinator.get_registered_types()]
            
            return {
                "engine_stats": self.stats,
                "registered_content_types": registered_types,
                "supported_operations": [
                    "unified_search", "article_search", "project_search",
                    "multi_type_search", "filtered_search", "highlighted_search"
                ]
            }
            
        except Exception as e:
            logger.error(f"获取统计信息异常: {str(e)}")
            return {
                "error": str(e),
                "engine_stats": self.stats
            }
    
    def register_adapter(self, adapter):
        """
        注册新的内容类型适配器
        
        Args:
            adapter: 内容类型适配器
        """
        self.coordinator.register_adapter(adapter)
    
    def get_supported_content_types(self) -> List[str]:
        """
        获取支持的内容类型列表
        
        Returns:
            List[str]: 支持的内容类型列表
        """
        return [ct.value for ct in self.coordinator.get_registered_types()]
