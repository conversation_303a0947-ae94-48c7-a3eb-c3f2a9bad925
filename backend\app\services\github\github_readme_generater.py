#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/5/22 15:50
# @File    : github_readme_generater.py
# @Description: 
"""
import structlog
import asyncio
import os
import platform
import subprocess
import shutil
import uuid
from urllib.parse import urlparse

import aiohttp
from typing import Dict, Any, Optional, Tuple, List
import requests
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.models.github.github_project import GitHubProjectModel
from app.models.github.github_project_subscription import GitHubProjectSubscriptionModel
from app.models.rbac.user import UserModel
from app.services.reasoning.workflow import AnalysisState
from app.services.reasoning.workflow.diagram_generator_graph import generate_project_diagrams
from app.services.reasoning.workflow.diagram_generators import DiagramType, DiagramFormat
from app.services.reasoning.workflow.project_analysis_graph import analyze_project
from app.utils.email_util import EmailNotificationUtil
from app.utils.flow_log_utils import record_user_flow_log, FlowLogType, record_system_message
from app.utils.status_enum import ProjectStatusEnum
from app.utils.wechat_util import WechatXmlUtil
from app.utils.smart_fake_data_generator import generate_enhanced_fake_data

logger = structlog.get_logger(__name__)

class GitHubReadmeGenerate:
    """Git项目分析工具类"""

    _instance = None
    _generate_queue = asyncio.Queue()
    _generate_task = []
    _initialized = False
    _active_tasks = 0  # 当前活跃任务数
    _max_concurrent_tasks = 1  # 最大并发任务数
    _queue_lock = asyncio.Lock()
    _generate_stats = {
        "total": 0,
        "success": 0,
        "failed": 0,
        "in_progress": 0
    }

    _cancelled_projects = set()  # 添加取消项目集合
    _running_tasks = {}  # 跟踪正在运行的任务

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, session=None, async_session=None):
        logger.info(f"Git README生成器初始化 ")
        if not self._initialized:
            # 检测操作系统
            os_type = platform.system()
            # 从配置中获取基础目录（根据操作系统）
            os_type = platform.system()
            if os_type == "Windows":
                base_dir = settings.github.GITHUB_PROJECTS_README_DIR_WINDOWS
            else:  # Linux 或 Mac
                base_dir = settings.github.GITHUB_PROJECTS_README_DIR_LINUX
            self.base_dir = base_dir
            logger.info(f"使用项目存储目录: {self.base_dir} (操作系统: {os_type})")

            # 创建目录（如果不存在）
            os.makedirs(self.base_dir, exist_ok=True)

            # 保存会话提供者
            self._session = session
            self._async_session = async_session


            # 确保生成任务已启动
            for _ in range(self._max_concurrent_tasks):
                task = asyncio.create_task(self._process_generate_queue())
                GitHubReadmeGenerate._generate_task.append(task)
            self._initialized = True
        logger.info(f"Git README生成器初始化完成，基础目录: {self.base_dir}")

    # ... existing code ...

    @classmethod
    async def cancel_project_generation_analyzer(cls, project_id: str) -> bool:
        """
        取消分析机上的项目生成
        这个方法用于发布机调用分析机的取消接口
        """
        try:
            # 检查是否为发布机模式
            if not settings.distributed.is_publisher:
                logger.error("当前不是发布机模式，无法调用分析机取消接口")
                return False

            # 检查分析机地址是否配置
            if not settings.distributed.AI_ANALYZER_ENDPOINT:
                logger.error("分析机地址未配置")
                return False

            logger.info(f"开始调用分析机取消项目 {project_id}")

            # 构建请求数据
            request_data = {
                "publisher_project_id": project_id
            }

            # 发送POST请求到分析机的取消接口
            async with aiohttp.ClientSession() as session:
                url = f"{settings.distributed.AI_ANALYZER_ENDPOINT}/api/v1/remote/agent/cancel-analysis"

                try:
                    async with session.post(url, json=request_data, timeout=30) as response:
                        if response.status == 200:
                            result = await response.json()
                            logger.info(f"分析机取消请求成功: {result}")
                            return result.get("success", False)
                        else:
                            error_text = await response.text()
                            logger.error(f"分析机取消请求失败，状态码: {response.status}, 错误: {error_text}")
                            return False

                except asyncio.TimeoutError:
                    logger.error(f"分析机取消请求超时: {project_id}")
                    return False
                except Exception as e:
                    logger.error(f"分析机取消请求异常: {str(e)}")
                    return False

        except Exception as e:
            logger.error(f"调用分析机取消接口失败: {str(e)}")
            return False


    @classmethod
    async def cancel_project_generation(cls, project_id: str) -> bool:
        """取消项目生成

        Args:
            project_id: 项目ID字符串

        Returns:
            bool: 是否成功取消
        """
        try:
            async with cls._queue_lock:
                # 添加到取消列表
                cls._cancelled_projects.add(project_id)

                # 1. 从队列中移除
                queue_items = []
                removed_count = 0

                while not cls._generate_queue.empty():
                    try:
                        pid = await cls._generate_queue.get()
                        if pid != project_id:
                            queue_items.append(pid)
                        else:
                            removed_count += 1
                        cls._generate_queue.task_done()
                    except asyncio.QueueEmpty:
                        break

                # 重新放入队列
                for item in queue_items:
                    await cls._generate_queue.put(item)

                # 2. 取消正在运行的任务
                if project_id in cls._running_tasks:
                    task = cls._running_tasks[project_id]
                    if not task.done():
                        task.cancel()
                        logger.info(f"取消了正在运行的分析任务: {project_id}")
                        try:
                            await task
                        except asyncio.CancelledError:
                            logger.info(f"分析任务已被取消: {project_id}")



                logger.info(f"从生成队列中移除了 {removed_count} 个项目 {project_id}")
                return True

        except Exception as e:
            logger.error(f"取消项目生成失败: {str(e)}")
            return False

    @classmethod
    def is_project_cancelled(cls, project_id: str) -> bool:
        """检查项目是否已被取消"""
        return project_id in cls._cancelled_projects



    @classmethod
    async def get_generate_queue_status(cls) -> Dict[str, Any]:
        """获取生成队列状态和详细信息

        Returns:
            Dict[str, Any]: 包含队列状态和详细信息的字典，包括:
            - 基础统计信息(队列大小、活跃任务等)
            - 当前队列中的所有项目详细信息
            - 生成任务的统计数据
        """
        try:
            # 使用锁来确保并发安全
            async with cls._queue_lock:
                # 获取当前队列中的所有项目
                queue_items = []
                temp_queue = asyncio.Queue()

                # 从原队列获取所有项目信息
                while not cls._generate_queue.empty():
                    try:
                        project_id = await cls._generate_queue.get()
                        # 构建项目信息字典
                        item_info = {
                            "project_id": project_id,
                            "position": len(queue_items) + 1,  # 在队列中的位置
                            "status": "waiting"  # 等待生成
                        }
                        queue_items.append(item_info)
                        await temp_queue.put(project_id)
                        cls._generate_queue.task_done()
                    except asyncio.QueueEmpty:
                        break

                # 将项目从临时队列放回原队列
                while not temp_queue.empty():
                    try:
                        project_id = await temp_queue.get()
                        await cls._generate_queue.put(project_id)
                        temp_queue.task_done()
                    except asyncio.QueueEmpty:
                        break

                # 构建返回的状态信息
                status_info = {
                    # 基础统计信息
                    "queue_size": cls._generate_queue.qsize(),
                    "active_tasks": cls._active_tasks,
                    "max_concurrent": cls._max_concurrent_tasks,

                    # 生成任务统计
                    "generate_stats": {
                        "total": cls._generate_stats["total"],
                        "success": cls._generate_stats["success"],
                        "failed": cls._generate_stats["failed"],
                        "in_progress": cls._generate_stats["in_progress"]
                    },

                    # 队列状态
                    "queue_status": "active" if cls._generate_task else "inactive",

                    # 队列中的详细项目信息
                    "queue_items": queue_items
                }

                logger.info(f"获取生成队列状态成功，当前队列长度: {len(queue_items)}")
                return status_info

        except Exception as e:
            logger.error(f"获取生成队列状态时发生错误: {str(e)}")
            return {
                "error": str(e),
                "queue_size": 0,
                "active_tasks": 0,
                "generate_stats": cls._generate_stats,
                "queue_items": []
            }

    @classmethod
    async def get_generate_queue(cls) -> List[str]:
        """获取当前生成队列中的所有项目ID

        Returns:
            List[str]: 队列中的项目ID列表
        """
        # 创建队列内容的副本
        queue_copy = []
        temp_queue = asyncio.Queue()

        # 获取所有项目ID并保持队列不变
        try:
            while not cls._generate_queue.empty():
                item = await cls._generate_queue.get()
                queue_copy.append(item)
                await temp_queue.put(item)

            # 恢复原队列
            while not temp_queue.empty():
                item = await temp_queue.get()
                await cls._generate_queue.put(item)

            return queue_copy

        except Exception as e:
            logger.error(f"获取生成队列时发生错误: {str(e)}")
            return []

    @classmethod
    async def add_to_generate_queue_priority(cls, project_id: str) -> None:
        """优先添加项目到生成队列的首位

        Args:
            project_id: 项目ID
        """
        async with cls._queue_lock:  # 使用异步锁保护队列操作
            # 创建临时队列
            temp_queue = asyncio.Queue()
            found = False

            try:
                # 遍历原队列
                while not cls._generate_queue.empty():
                    item = await cls._generate_queue.get()
                    if item != project_id:
                        await temp_queue.put(item)
                    else:
                        found = True

                # 首先放入优先项
                await cls._generate_queue.put(project_id)

                # 将其他项目放回原队列
                while not temp_queue.empty():
                    item = await temp_queue.get()
                    await cls._generate_queue.put(item)

                if found:
                    logger.info(f"项目 {project_id} 已存在，已移至队列首位")
                else:
                    logger.info(f"项目 {project_id} 已添加到队列首位")

            except Exception as e:
                logger.error(f"优先添加项目到队列时发生错误: {str(e)}")
                # 发生错误时，确保锁会被释放
                raise

    @classmethod
    async def add_to_generate_queue(cls, project_id: str) -> None:
        """添加项目到分析队列

        Args:
            project_id: 项目ID
        """
        async with cls._queue_lock:  # 原方法也需要加锁
            await cls._generate_queue.put(project_id)
            logger.info(f"项目 {project_id} 已添加到生成队列")

    async def _process_generate_queue(self) -> None:
        """处理分析队列的异步任务"""
        logger.info(f"_process_generate_queue 开始")
        while True:
            try:
                # 从队列获取项目信息
                project_id = await self._generate_queue.get()
                # 使用异步上下文管理器创建新的session 这里真的能行吗

                # 检查项目是否已被取消
                if self.is_project_cancelled(project_id):
                    logger.info(f"项目 {project_id} 已被取消，跳过生成")
                    self._generate_queue.task_done()
                    self._cancelled_projects.discard(project_id)
                    continue

                async with self._async_session() as session:
                    try:
                        # 原来的
                        # await self._process_project(session, project_id)
                        # now
                        task = asyncio.create_task(self._process_project(session, project_id))
                        self._running_tasks[project_id] = task
                        # await here
                        await task
                    except asyncio.CancelledError:
                        logger.info(f"项目 {project_id} 的分析任务被取消")
                        await self._update_project_status(session, project_id, ProjectStatusEnum.CANCELLED.value)
                    except Exception as e:
                        logger.error(f"处理项目 {project_id} 时发生错误: {str(e)}")
                        await self._update_project_status(session, project_id, ProjectStatusEnum.GenerateFail.value)
                    finally:
                        # here
                        await asyncio.sleep(5)
                        self._generate_queue.task_done()
            except Exception as e:
                logger.error(f"分析队列处理发生错误: {str(e)}")
                await asyncio.sleep(5)  # 发生错误时等待一段时间再继续

    async def _process_project(self, session: AsyncSession, project_id: str) -> None:
        # """处理单个项目的生成任务，使用信号量限制并发"""
        # """处理下载队列的异步任务"""
        logger.info(f"_process_project 开始 ，project_id:: " + project_id)
        try:
            # 获取信号量以限制并发数量
            self._generate_stats["in_progress"] += 1
            self._generate_stats["total"] += 1
            self._active_tasks += 1

            # 在关键点检查取消状态
            if self.is_project_cancelled(project_id):
                logger.info(f"项目 {project_id} 在开始分析前被取消")
                return

            await self._update_project_status(session, project_id, ProjectStatusEnum.Generate.value)
            logger.info("调试 project_id:: " + project_id)
            local_path, repository_url = await self._get_project_local_path(session, project_id)
            logger.info("调试 local_path:: " + local_path)
            if not local_path:
                logger.error(f"生成README卡片失败，项目 {project_id} 本地路径未找到")
                await self._update_project_status(session, project_id,
                                                  ProjectStatusEnum.GenerateFail.value)
                self._generate_stats["failed"] += 1
                return

            # 再次检查取消状态
            if self.is_project_cancelled(project_id):
                logger.info(f"项目 {project_id} 在获取本地路径后被取消")
                await self._update_project_status(session, project_id, ProjectStatusEnum.CANCELLED.value)
                return

            # 生成README
            # 分析机处理队列
            if settings.distributed.is_analyzer:
                # 检查是否使用假数据
                if getattr(settings.github, 'USE_FAKE_DATA', False):
                    # 使用智能假数据生成器
                    logger.info(f"使用假数据生成器为项目 {project_id} 生成测试数据")
                    

                    # 生成假数据
                    success, readme_info, architecture, dependency = generate_enhanced_fake_data(
                        project_path=local_path
                    )
                    
                    # 模拟处理时间
                    fake_processing_time = getattr(settings.github, 'FAKE_PROCESSING_TIME', 10)
                    logger.info(f"模拟处理时间: {fake_processing_time}秒")
                    await asyncio.sleep(fake_processing_time)
                    
                else:
                    # 使用真实的项目分析
                    logger.info(f"使用真实分析为项目 {project_id} 生成数据")
                    success, readme_info, architecture, dependency = await self.generate_repository(local_path)
                
                # success, readme_info, architecture, dependency = await self.generate_repository(local_path)

                # fake start
                # success = True
                # 创建假的AnalysisState对象
                # from app.services.reasoning.workflow.models import AnalysisState
                # from app.services.reasoning.analyzers.readme_generator import ReadmeDocument, IntroductionSection
                # 创建假的introduction章节
                # intro_section = IntroductionSection(
                #     title="项目介绍",
                #     slogan="这是一个测试项目的推荐标语",
                #     project_description="这是一个用于测试流程的项目描述",
                #     project_vision="项目愿景：成为优秀的测试项目",
                #     core_features=[
                #         {"title": "功能1", "description": "这是功能1的描述"},
                #         {"title": "功能2", "description": "这是功能2的描述"}
                #     ],
                #     use_cases=["测试场景1", "测试场景2"],
                #     feature_tags=["测试", "示例", "演示"],
                #     order=1
                # )

                # 创建假的README文档
                # readme_doc = ReadmeDocument()
                # readme_doc.sections['introduction'] = intro_section

                # 创建假的AnalysisState
                # readme_info = AnalysisState(
                #     project_path=local_path,
                #     project_name=os.path.basename(local_path)
                # )
                # readme_info.readme = readme_doc
                # architecture = "graph TD\n    A[测试项目] --> B[模块1]\n    A --> C[模块2]"
                # dependency = "graph TD\n    A[测试依赖] --> B[依赖1]\n    A --> C[依赖2]"
                # await asyncio.sleep(65)
                # fake end
                publish_project_id = None
                if success:
                    # 更新项目卡片信息
                    slogan, description, feature_tags, phone_shared_data, cards_data = await self._update_project_card_info(session, project_id, readme_info, architecture, dependency)
                    # 更新项目状态信息获取发布机原项目id
                    publish_project_id = await self._update_project_success_analyzer(session, project_id, architecture, dependency, slogan, description, feature_tags, phone_shared_data)
                    self._generate_stats["success"] += 1
                    logger.info(f"项目 {project_id} README生成成功")
                else:
                    publish_project_id = await self._update_project_status(session, project_id, ProjectStatusEnum.GenerateFail.value)
                    self._generate_stats["failed"] += 1
                    logger.error(f"项目 {project_id} README生成失败")
                # 分析机器不需要发送公众号消息
                # await WechatXmlUtil.send_official_msg_by_project_status(session, project_id, success)
                # 分析机

                ans = await self.reply_publisher(project_id=publish_project_id,
                                           success=success,
                                           slogan=slogan,
                                           description=description,
                                           feature_tags=feature_tags,
                                           phone_shared_data=phone_shared_data,
                                           architecture=architecture,
                                           dependency=dependency,
                                           cards_data=cards_data)
                logger.info(f"项目 {project_id} 处理完成，程序休息5秒, 发布给发布机结果:: {ans}")
                await asyncio.sleep(5)
            # 发布机处理队列
            if settings.distributed.is_publisher:
                # 等待执行完成,等到最后分析机发送post卡片指令 并让项目状态置于生成成功，
                success = await self.send_generate_analyzer(session, repository_url, project_id)

                # 不更新状态，因为项目状态已经由分析机调用更新接口的时候调用过了，而且
                # 项目状态的：：ProjectStatusEnum.GenerateFail.value/ ProjectStatusEnum.GenerateSuccess.value
                # 是send_generate_analyzer结束的条件

                # await self._update_project_status(session, project_id, status)

                # 发送订阅
                await self._send_notifications_to_subscribers(session, project_id, success)
                await WechatXmlUtil.send_official_msg_by_project_status(session, project_id, success)
                await EmailNotificationUtil.send_project_analysis_notification(session, project_id, success)

        except asyncio.CancelledError:
            logger.info(f"项目 {project_id} 的处理任务被取消")
            await self._update_project_status(session, project_id, ProjectStatusEnum.CANCELLED.value)
            self._generate_stats["failed"] += 1

            # 如果是publish就发送给分析机让他取消 - 如果是anly分析机就不可能取消
            # here
            raise
        except Exception as e:
            logger.error(f"处理项目 {project_id} 时发生错误: {str(e)}")
            self._generate_stats["failed"] += 1
            # 确保当前会话仍然有效
            try:
                await self._update_project_status(session, project_id, ProjectStatusEnum.GenerateFail.value)
            except Exception as session_error:
                logger.error(f"更新项目状态失败: {str(session_error)}")
        finally:
            self._generate_stats["in_progress"] -= 1
            self._active_tasks -= 1

    async def _send_notifications_to_subscribers(self, session: AsyncSession, project_id: str, success: bool) -> None:
        """
        向项目创建者和所有订阅者发送通知

        Args:
            session: 数据库会话
            project_id: 项目ID
            success: 分析是否成功
        """
        try:
            # 使用GitHubProjectService获取订阅者
            # 获取所有订阅者（包括创建者）
            subscribers = await self.get_project_subscribers(session,project_id)

            if not subscribers:
                logger.info("没有需要通知的用户", project_id=project_id)
                return

            # 获取项目信息用于通知内容
            project = await session.get(GitHubProjectModel, project_id)
            if not project:
                logger.warning("项目不存在", project_id=project_id)
                return

            logger.info("开始发送通知给订阅者",
                        project_id=project_id,
                        project_name=project.name,
                        subscriber_count=len(subscribers))

            # 创建邮件服务实例
            from app.services.rbac.email import EmailService
            email_service = EmailService()

            # 循环发送通知给所有订阅者
            wechat_success_count = 0
            email_success_count = 0

            for subscriber in subscribers:
                user_id = subscriber["user_id"]
                user_email = subscriber["email"]
                user_wechat = subscriber["wechat_openid"]
                user_nickname = subscriber["nickname"]
                is_creator = subscriber["is_creator"]

                logger.info("发送通知给用户",
                            user_id=user_id,
                            nickname=user_nickname,
                            is_creator=is_creator)

                # 发送微信通知
                if user_wechat:
                    try:
                        wechat_result = await WechatXmlUtil.send_official_msg_by_project_status(
                            session, project_id, success
                        )
                        if wechat_result:
                            wechat_success_count += 1
                            logger.info("微信通知发送成功", user_id=user_id)
                        else:
                            logger.warning("微信通知发送失败", user_id=user_id)
                    except Exception as e:
                        logger.error("发送微信通知异常", user_id=user_id, error=str(e))

                # 发送邮件通知
                if user_email:
                    try:
                        email_result = EmailNotificationUtil.send_project_analysis_notification(session, project_id, success)
                        if email_result:
                            email_success_count += 1
                            logger.info("邮件通知发送成功", user_id=user_id, email=user_email)
                        else:
                            logger.warning("邮件通知发送失败", user_id=user_id, email=user_email)
                    except Exception as e:
                        logger.error("发送邮件通知异常", user_id=user_id, error=str(e))

            logger.info("通知发送完成",
                        project_id=project_id,
                        total_subscribers=len(subscribers),
                        wechat_success=wechat_success_count,
                        email_success=email_success_count)

            # 不清理订阅记录（可选，因为业务上一个项目只分析一次）

        except Exception as e:
            logger.error("发送订阅者通知失败", project_id=project_id, error=str(e))

    async def get_project_subscribers(self, session: AsyncSession, project_id: str) -> List[Dict[str, Any]]:
        """
        获取项目的所有订阅者（包括创建者）

        Args:
            project_id: 项目ID

        Returns:
            List[Dict[str, Any]]: 订阅者列表
        """
        try:
            from sqlalchemy import select
            from app.models.github.github_project import GitHubProjectModel

            subscribers = []

            # 2. 获取订阅者
            subscription_stmt = select(
                GitHubProjectSubscriptionModel, UserModel
            ).join(
                UserModel, GitHubProjectSubscriptionModel.created_by == UserModel.id
            ).where(
                GitHubProjectSubscriptionModel.project_id == project_id
            )

            subscription_result = await session.execute(subscription_stmt)

            for subscription, user in subscription_result:
                subscribers.append({
                    "user_id": user.id,
                    "email": user.email,
                    "phone": user.phone,
                    "wechat_openid": user.wechat_openid,
                    "nickname": user.nickname,
                    "is_creator": False
                })

            logger.info("获取项目订阅者成功", project_id=project_id, subscriber_count=len(subscribers))
            return subscribers

        except Exception as e:
            logger.error("获取项目订阅者失败", project_id=project_id, error=str(e))
            return []

    async def send_generate_analyzer(self,session:AsyncSession, repository_url: str, project_id: str) -> bool:
        """
        发送项目到分析机进行分析，并等待分析完成

        Args:
            repository_url: 项目仓库URL
            project_id: 项目ID

        Returns:
            bool: 分析是否成功完成
        """
        try:
            # 检查是否为发布机模式
            if not settings.distributed.is_publisher:
                logger.error("当前不是发布机模式，无法发送分析请求")
                return False

            # 检查分析机地址是否配置
            if not settings.distributed.AI_ANALYZER_ENDPOINT:
                logger.error("分析机地址未配置")
                return False

            logger.info(f"开始发送项目 {project_id} 到分析机进行分析")

            # 1. 发送分析命令到分析机
            success = await self._send_analysis_command(repository_url, project_id)
            if not success:
                logger.error(f"发送分析命令失败: {project_id}")
                return False

            logger.info(f"分析命令发送成功，开始等待分析完成: {project_id}")

            # 2. 轮询心跳等待分析完成，同时获取日志流
            analysis_success = await self._wait_for_analysis_completion_with_heartbeat(session, project_id)

            if analysis_success:
                logger.info(f"项目 {project_id} 分析完成")
                return True
            else:
                logger.error(f"项目 {project_id} 分析失败或超时")
                return False

        except Exception as e:
            logger.error(f"发送分析请求时发生错误: {str(e)}")
            return False

    async def _send_analysis_command(self, repository_url: str, project_id: str) -> bool:
        """
        发送分析命令到分析机

        Args:
            repository_url: 项目仓库URL
            project_id: 项目ID

        Returns:
            bool: 命令发送是否成功
        """
        try:
            # 构建请求数据
            request_data = {
                "publisher_project_id": project_id,
                "projects": [
                    {
                        "repository_url": repository_url
                    }
                ]
            }

            # 发送POST请求到分析机
            async with aiohttp.ClientSession() as session:
                url = f"{settings.distributed.AI_ANALYZER_ENDPOINT}/api/v1/remote/agent/analyzer-called"

                async with session.post(url, json=request_data) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"url:: {url} 分析命令发送成功: {result}")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"分析命令发送失败，状态码: {response.status}, 错误: {error_text}")
                        return False

        except Exception as e:
            logger.error(f"发送分析命令时发生错误: {str(e)}")
            return False

    async def _get_and_log_analysis_heartbeat(self, project_id: str, log_type: str) -> None:
        """
        获取并记录分析日志

        Args:
            project_id: 项目ID
            log_type: 日志类型 (progress, final, failed)
        """
        try:
            logs_data = await self.get_analysis_logs(project_id)
            if logs_data:
                logger.info(f"项目 {project_id} {log_type} 日志: {logs_data}")
            else:
                logger.debug(f"项目 {project_id} 暂无 {log_type} 日志")
        except Exception as e:
            logger.error(f"获取项目 {project_id} {log_type} 日志时发生错误: {str(e)}")
            # 直接抛出异常，让调用方知道日志获取失败
            raise e

    async def _wait_for_analysis_completion_with_heartbeat(self, session: AsyncSession ,project_id: str) -> bool:
        """
        等待项目分析完成，同时获取分析日志流

        Args:
            project_id: 项目ID

        Returns:
            bool: 分析是否成功完成

        Raises:
            Exception: 当分析机掉线时抛出异常
        """
        try:
            max_wait_time = settings.distributed.HEARTBEAT_TIMEOUT  # 最大等待时间（秒）
            heartbeat_interval = settings.distributed.HEARTBEAT_INTERVAL  # 心跳间隔（秒）

            start_time = asyncio.get_event_loop().time()
            last_log_time = 0
            log_failure_count = 0  # 日志获取失败计数器
            max_log_failures = 3  # 最大日志获取失败次数

            while True:
                # 检查是否超时
                current_time = asyncio.get_event_loop().time()
                # if current_time - start_time > max_wait_time:
                #     logger.error(f"项目 {project_id} 分析超时")
                #     return False

                # 检查项目状态
                # project_status = await self._check_project_status(session, project_id)
                project_status = await self.get_project_phase(session, project_id)
                if project_status == ProjectStatusEnum.GenerateSuccess.value:
                    logger.info(f"项目 {project_id} 分析成功完成")
                    # 获取最终的分析日志
                    try:
                        await self._get_and_log_analysis_heartbeat(project_id, "final")
                        log_failure_count = 0  # 重置失败计数
                    except Exception as e:
                        log_failure_count += 1
                        logger.warning(f"获取最终日志失败，失败次数: {log_failure_count}")
                        if log_failure_count >= max_log_failures:
                            raise Exception(f"分析机掉线：连续 {max_log_failures} 次日志获取失败")
                    return True

                elif project_status == ProjectStatusEnum.GenerateFail.value:
                    logger.error(f"项目 {project_id} 分析失败")
                    # 获取失败的分析日志
                    try:
                        await self._get_and_log_analysis_heartbeat(project_id, "failed")
                        log_failure_count = 0  # 重置失败计数
                    except Exception as e:
                        log_failure_count += 1
                        logger.warning(f"获取失败日志失败，失败次数: {log_failure_count}")
                        if log_failure_count >= max_log_failures:
                            raise Exception(f"分析机掉线：连续 {max_log_failures} 次日志获取失败")
                    return False

                elif project_status == ProjectStatusEnum.CANCELLED.value:
                    logger.info(f"项目 {project_id} 分析被取消")
                    return False

                elif project_status in [ProjectStatusEnum.Generate.value, ProjectStatusEnum.WAIT_GENERATE.value]:
                    # 分析中或等待分析，继续等待
                    logger.debug(f"项目 {project_id} 正在分析中，状态: {project_status}")

                    # 定期获取分析日志（每30秒获取一次）
                    if current_time - last_log_time > 30:
                        try:
                            await self._get_and_log_analysis_heartbeat(project_id, "progress")
                            log_failure_count = 0  # 重置失败计数
                            last_log_time = current_time
                        except Exception as e:
                            log_failure_count += 1
                            logger.warning(f"获取进度日志失败，失败次数: {log_failure_count}")
                            if log_failure_count >= max_log_failures:
                                raise Exception(f"分析机掉线：连续 {max_log_failures} 次日志获取失败")

                    await asyncio.sleep(heartbeat_interval)
                else:
                    # 其他状态，可能是错误
                    logger.warning(f"项目 {project_id} 状态异常: {project_status}")
                    await asyncio.sleep(heartbeat_interval)

        except Exception as e:
            logger.error(f"等待分析完成时发生错误: {str(e)}")
            # 直接抛出异常，让调用方知道分析机掉线
            raise e

    async def get_project_phase(self, session: AsyncSession, project_id: str) -> Optional[str]:
        try:
            from sqlalchemy import select
            from app.models.github.github_project import GitHubProjectModel

            stmt = select(GitHubProjectModel.project_phase).where(GitHubProjectModel.id == project_id)
            result = await session.execute(stmt)
            project_phase = result.scalar_one_or_none()

            if project_phase:
                logger.warning(f"项目状态：： {project_phase}")
                return project_phase
            else:
                logger.warning(f"项目状态不存在")
                return None

        except Exception as e:
            logger.error(f"get_project_phase时发生错误: {str(e)}")
            return None

    async def _check_project_status(self, async_session: AsyncSession, project_id: str) -> Optional[str]:
        """
        通过调用分析机的心跳接口检查项目状态
        
        Args:
            project_id: 项目ID
            
        Returns:
            Optional[str]: 项目状态，失败返回None
        """
        try:
            # 检查分析机地址是否配置
            if not settings.distributed.AI_ANALYZER_ENDPOINT:
                logger.error("分析机地址未配置")
                return None
                
            # 调用分析机的心跳接口
            async with aiohttp.ClientSession() as session:
                url = f"{settings.distributed.AI_ANALYZER_ENDPOINT}/api/v1/remote/agent/health-check"
                
                try:
                    async with session.get(url, timeout=10) as response:
                        if response.status == 200:
                            heartbeat_data = await response.json()
                            
                            # 解析心跳数据
                            if heartbeat_data.get("code") == 200:
                                analyzer_status = heartbeat_data.get("data", {}).get("analyzer_status", {})
                                
                                # 检查分析机是否在线
                                if not analyzer_status.get("is_online", False):
                                    logger.warning("分析机不在线")
                                    return None
                                
                                # 获取正在分析的项目列表
                                analyzing_projects = analyzer_status.get("analyzing_projects", [])
                                
                                # 检查当前项目是否在分析列表中(analyzing_projects中存的是分析机的project_id，在分析机中
                                # 的create_by为发布机的project_id，目前没有修改add的逻辑也没有做详细的同步机制)

                                # if project_id in analyzing_projects:
                                #     logger.info(f"项目 {project_id} 正在分析中")
                                #     return ProjectStatusEnum.Generate.value
                                # else:
                                #     # 项目不在分析列表中，可能已完成或失败
                                #     # 需要进一步检查项目状态
                                #     return await self._check_project_final_status(async_session, project_id)

                            else:
                                logger.error(f"分析机心跳响应错误: {heartbeat_data}")
                                return None
                        else:
                            logger.error(f"分析机心跳请求失败，状态码: {response.status}")
                            return None
                            
                except asyncio.TimeoutError:
                    logger.error("分析机心跳请求超时")
                    return None
                except Exception as e:
                    logger.error(f"调用分析机心跳接口时发生错误: {str(e)}")
                    return None
                    
        except Exception as e:
            logger.error(f"检查项目状态时发生错误: {str(e)}")
            return None

    async def _check_project_final_status(self, session: AsyncSession, project_id: str) -> Optional[str]:
        """
        检查项目的最终状态（当项目不在分析列表中时调用）
        
        Args:
            project_id: 项目ID
            
        Returns:
            Optional[str]: 项目状态
        """
        try:
            from sqlalchemy import select
            from app.models.github.github_project import GitHubProjectModel

            stmt = select(GitHubProjectModel.project_phase).where(GitHubProjectModel.id == project_id)
            result = await session.execute(stmt)
            project = result.scalar_one_or_none()

            if project:
                return project
            else:
                logger.warning(f"项目 {project_id} 不存在")
                return None
                    
        except Exception as e:
            logger.error(f"检查项目最终状态时发生错误: {str(e)}")
            return None

    async def reply_publisher(self, project_id: str, success: bool,
                              slogan: Optional[str] = None,
                              description: Optional[str] = None,
                              feature_tags: Optional[str] = None,
                              phone_shared_data: Optional[str] = None,
                              architecture: Optional[str] = None,
                              dependency: Optional[str] = None,
                              cards_data: Optional[List[Dict]] = None
                              ) -> bool:
        """
        向发布机回复分析结果

        Args:
            project_id: 项目ID
            success: 分析是否成功
            slogan: 项目标语
            description: 项目描述
            feature_tags: 特征标签
            phone_shared_data: 手机分享数据
            architecture: 架构图
            dependency: 依赖图
            cards_data: 卡片数据列表

        Returns:
            bool: 发送是否成功
        """
        try:
            # 检查是否为分析机模式
            if not settings.distributed.is_analyzer:
                logger.error("当前不是分析机模式，无法发送分析结果")
                return False

            # 检查发布机地址是否配置
            if not settings.distributed.PUBLISHER_ENDPOINT:
                logger.error("发布机地址未配置")
                return False

            logger.info(f"开始向发布机发送项目 {project_id} 的分析结果")

            # 构建请求数据
            request_data = {
                "analyzer_project_id": project_id,
                "success": success,
                "project_info": {
                    "slogan": slogan,
                    "description": description,
                    "feature_tags": feature_tags,
                    "phone_shared_data": phone_shared_data,
                    "architecture": architecture,
                    "dependency": dependency
                },
                "cards_data": cards_data or []
            }

            # 发送POST请求到发布机
            async with aiohttp.ClientSession() as session:
                url = f"{settings.distributed.PUBLISHER_ENDPOINT}/api/v1/remote/agent/publisher-called"

                async with session.post(url, json=request_data) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"分析结果发送成功: {result}")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"分析结果发送失败，状态码: {response.status}, 错误: {error_text}")
                        return False

        except Exception as e:
            logger.error(f"发送分析结果时发生错误: {str(e)}")
            return False

    # 未使用-250627
    async def generate_repository_with_cancellation(self, project_path: str, project_id: str) -> tuple:
        """支持取消检查的仓库生成方法"""
        try:
            project_name = os.path.basename(project_path)
            file_name = self.generate_md_filename(project_path)
            generated_path = self.base_dir

            # 在开始分析前检查取消状态
            if self.is_project_cancelled(project_id):
                logger.info(f"项目 {project_id} 在开始分析前被取消")
                return False, None, None, None

            logger.info("开始项目分析", project_path=project_path, project_name=project_name)

            # 这里可以进一步修改 analyze_project 来支持取消检查
            analysis_result = await self.analyze_project_with_cancellation(
                file_name=file_name,
                generated_path=generated_path,
                project_path=project_path,
                project_name=project_name,
                project_id=project_id
            )

            # 再次检查取消状态
            if self.is_project_cancelled(project_id):
                logger.info(f"项目 {project_id} 在分析完成后被取消")
                return False, None, None, None

            # 生成图表部分
            diagram_types = [DiagramType.ARCHITECTURE, DiagramType.DEPENDENCY]
            diagram_formats = [DiagramFormat.MERMAID, DiagramFormat.JSON]

            diagram_result = await generate_project_diagrams(
                project_path=project_path,
                project_name=project_name,
                diagram_types=diagram_types,
                diagram_formats=diagram_formats,
                detail_level=2,
                colorize=True,
                interactive=True
            )

            architecture = diagram_result.get("diagrams", {}).get(DiagramType.ARCHITECTURE, {}).get(
                DiagramFormat.MERMAID, "")
            dependency = diagram_result.get("diagrams", {}).get(DiagramType.DEPENDENCY, {}).get(DiagramFormat.MERMAID,
                                                                                                "")

            return True, analysis_result, architecture, dependency

        except Exception as e:
            logger.error(f"分析过程中发生错误: {str(e)}")
            return False, None, None, None

    async def analyze_project_with_cancellation(self, file_name: str, generated_path: str,
                                                project_path: str, project_name: str, project_id: str):
        """支持取消检查的分析项目方法"""
        from app.services.reasoning.workflow.project_analysis_graph import analyze_project

        # 可以考虑创建一个带取消检查的包装器
        return await analyze_project(
            file_name=file_name,
            generated_path=generated_path,
            project_path=project_path,
            project_name=project_name
        )


    async def _update_project_status(self, session: AsyncSession, project_id: str, status: str) :

        try:
            project = await session.get(GitHubProjectModel, project_id)
            if project:
                project.project_phase = status
                await session.commit()
            return project.created_by
        except Exception as e:
            logger.error(f"更新项目状态失败: {str(e)}")
            await session.rollback()

    async def _get_project_local_path(self, session: AsyncSession, project_id):
        try:
            project = await session.get(GitHubProjectModel, project_id)
            if project:
                logger.info("调试 project:: " + str(project))
                logger.info("调试 local_path:: " + str(project.local_path))
                logger.info("调试 repository_url:: " + str(project.repository_url))
                return project.local_path, project.repository_url
        except Exception as e:
            logger.error(f"更新项目信息失败: {str(e)}")
            await session.rollback()

    async def _update_project_card_info(self, session: AsyncSession, project_id: str,
                                        cards_info: AnalysisState,
                                        architecture: str,
                                        dependency: str,
                                        ):
        """更新项目卡片信息

        Args:
            session: 异步数据库会话feature_tags
            project_id: 项目ID
            project_info: 项目分析状态对象
        """
        try:
            from app.models.github.github_project_card import GitHubProjectCardModel
            from sqlalchemy import delete

            # 先删除该项目所有现有卡片
            delete_stmt = delete(GitHubProjectCardModel).where(GitHubProjectCardModel.project_id == str(project_id))
            await session.execute(delete_stmt)

            # 检查README是否存在
            if not cards_info or not cards_info.readme:
                logger.warning(f"项目 {project_id} 未生成README或README为空")
                return None, None

            # 获取按顺序排序的章节
            ordered_sections = sorted(
                cards_info.readme.sections.values(),
                key=lambda s: s.order
            )
            # logger.info(f"项目 {project_id} 的ordered_sections内容", sections=ordered_sections,
            #             sections_count=len(ordered_sections))

            # 为每个章节创建卡片
            index = 0
            cards_data = []
            for section in ordered_sections:
                index += 1
                # 调用每个章节的 to_markdown() 方法
                logger.info(f"项目 {project_id} 的section内容", section=section)

                section_markdown = section.to_markdown()

                # 如果markdown内容为空，跳过
                if not section_markdown or not section_markdown.strip():
                    continue
                if index == 2:
                    pass

                title = section.title if hasattr(section, 'title') and section.title else f"章节 {index}"
                # 创建卡片 - 使用markdown内容
                section_card = GitHubProjectCardModel(
                    project_id=str(project_id),
                    title=title,
                    content=section_markdown,  # 使用完整的markdown内容
                    like=0,
                    dislike=0,
                    collect=0,
                    sort_order=index
                )
                session.add(section_card)

                # if index == 2:  # 在第二个卡片之后插入 必定是依赖图解
                #     dependency_card = GitHubProjectCardModel(
                #         project_id=str(project_id),
                #         title="依赖关系图解",
                #         content=dependency,  # 使用传入的dependency参数
                #         like=0,
                #         dislike=0,
                #         collect=0,
                #         sort_order=index + 1
                #     )
                #     session.add(dependency_card)
                #     # 更新后续卡片的排序
                #     index += 1

                cards_data.append({
                    "title": title,
                    "content": section_markdown,
                    "sort_order": index
                })
            await session.commit()
            logger.info(f"项目 {project_id} 卡片信息更新成功，共创建 {len(ordered_sections)} 个卡片")


            # 外部推荐
            slogan = cards_info.readme.sections['introduction'].slogan
            # 内部详情
            description = cards_info.readme.sections['introduction'].project_description
            # 特征标签
            feature_tags = cards_info.readme.sections['introduction'].feature_tags

            # 分享
            phone_shared_data = self._create_share_data(cards_info)

            return slogan, description, feature_tags, phone_shared_data, cards_data

        except Exception as e:
            logger.error(f"更新项目卡片信息失败: {str(e)}")
            await session.rollback()

    def _create_share_data(self, cards_info: AnalysisState) -> str:
        """创建手机分享数据

        Args:
            cards_info: 项目分析状态对象

        Returns:
            str: JSON格式的分享数据
        """
        import json

        # 初始化分享数据列表
        shared_data = []

        # 获取introduction章节
        intro_section = cards_info.readme.sections.get('introduction')
        if intro_section:
            # 1. 简介 - 使用project_description
            if intro_section.project_description:
                shared_data.append({
                    "key": "简介",
                    "value": intro_section.project_description
                })

            # 2. 功能特性 - 收集所有核心功能的标题
            if intro_section.core_features:
                feature_titles = []
                for feature in intro_section.core_features:
                    if feature.title:
                        feature_titles.append(feature.title)
                if feature_titles:
                    shared_data.append({
                        "key": "功能特性",
                        "value": "、".join(feature_titles)
                    })

            # 3. 适用场景 - 使用use_cases
            if intro_section.use_cases:
                shared_data.append({
                    "key": "适用场景",
                    "value": "、".join(intro_section.use_cases)
                })

            # 4. 项目愿景 - 使用project_vision
            if intro_section.project_vision:
                shared_data.append({
                    "key": "项目愿景",
                    "value": intro_section.project_vision
                })

        # 将数据转换为JSON字符串
        return json.dumps(shared_data, ensure_ascii=False)

    async def _update_project_success_analyzer(self, session: AsyncSession, project_id: str,
                                    architecture: str,
                                    dependency: str,
                                    slogan: Optional[str] = None,
                                    description: Optional[str] = None,
                                    feature_tags: Optional[str] = None,
                                    phone_shared_data: Optional[str] = None
                                      ):

        try:
            project = await session.get(GitHubProjectModel, project_id)
            if project:
                # project.local_path = local_path
                # project.image_url = local_image_url
                # project.icon_url = local_image_url
                project.description_recommend = slogan
                project.description_project = description
                project.status = ProjectStatusEnum.TRUE.value
                project.project_phase = ProjectStatusEnum.GenerateSuccess.value
                if architecture is not None:
                    project.architecture_mermaid = architecture
                if dependency is not None:
                    project.dependency_mermaid = dependency
                if feature_tags is not None:
                    project.tags = feature_tags
                if phone_shared_data is not None:
                    project.shared_data = phone_shared_data

                await record_user_flow_log(
                    session=session,
                    log_type=FlowLogType.PROJECT_GENERATE,
                    project_id=project.id,
                    project_name=project.name,
                    content=f"{project.repository_url}",
                    created_by=project.created_by
                )
                await record_system_message(
                    session=session,
                    message_type=FlowLogType.PROJECT_GENERATE,
                    message=f"项目{project.name}生成完成",
                    created_by=project.created_by
                )

                await session.commit()
                return project.created_by
        except Exception as e:
            logger.error(f"更新项目信息失败: {str(e)}")
            await session.rollback()

    def generate_md_filename(self,path):
        parent_dir = os.path.basename(os.path.dirname(path))
        current_dir = os.path.basename(path)
        return f"{parent_dir}_{current_dir}.md"

    async def generate_repository(self, project_path: str) -> (bool, AnalysisState, str, str):
        try:
            project_name = os.path.basename(project_path)
            file_name = self.generate_md_filename(project_path)
            generated_path = self.base_dir
            logger.info("开始项目示例:: ", project_path=project_path, project_name=project_name, file_name=file_name, generated_path=generated_path)

            #=======================生成卡片=======================
            analysis_result = await analyze_project(
                file_name=self.generate_md_filename(project_path),
                generated_path=generated_path,
                project_path=project_path,
                project_name=project_name
            )

            # ==================== 详细输出分析结果 ====================
            logger.info("项目分析完成，开始输出所有结果详情", project_name=project_name)

            # 输出 AnalysisState 对象的基本信息
            logger.info(" AnalysisState 对象基本信息",
                        analysis_result_type=type(analysis_result).__name__,
                        analysis_result_str=str(analysis_result),
                        analysis_result_repr=repr(analysis_result))


            #=======================生成图=======================
            # 定义要生成的图表类型
            diagram_types = [
                DiagramType.ARCHITECTURE,  # 架构图
                DiagramType.DEPENDENCY  # 依赖图
            ]

            diagram_formats = [
                DiagramFormat.MERMAID,  # Mermaid 格式
                DiagramFormat.JSON  # JSON 格式
            ]

            # 生成项目架构图和依赖图
            diagram_result = await generate_project_diagrams(
                project_path=project_path,
                project_name=project_name,
                # output_dir=diagrams_dir,
                diagram_types=diagram_types,
                diagram_formats=diagram_formats,
                detail_level=2,  # 中等详细程度
                colorize=True,  # 使用颜色
                interactive=True  # 生成交互式图表
            )
            architecture = diagram_result.get("diagrams", {}).get(DiagramType.ARCHITECTURE, {}).get(DiagramFormat.MERMAID, "")
            dependency = diagram_result.get("diagrams", {}).get(DiagramType.DEPENDENCY, {}).get(DiagramFormat.MERMAID, "")

            return True, analysis_result, architecture, dependency
        # except subprocess.CalledProcessError as e:
        #     logger.error(f"仓库克隆失败: {str(e)}", error=e.stderr.decode() if e.stderr else "")
        #     return False, "", {}
        except Exception as e:
            logger.error(f"分析过程中发生错误: {str(e)}")
            return False, None, None, None


    def _normalize_path(self, path: str) -> str:
        os_type = platform.system()

        if os_type == "Windows":
            # Windows路径处理
            return path.replace('/', '\\')
        else:
            # Linux/Mac路径处理
            return path.replace('\\', '/')

    def _get_repository_info(self, repo_url: str, owner: str, repo_name: str) -> Dict[str, Any]:
        """获取仓库信息

        Args:
            repo_url: 仓库URL
            owner: 仓库所有者
            repo_name: 仓库名称

        Returns:
            Dict[str, Any]: 仓库信息
        """
        # 尝试从GitHub API获取仓库信息
        api_url = f"https://api.github.com/repos/{owner}/{repo_name}"

        try:
            headers = {}
            if settings.github.GITHUB_TOKEN:
                headers["Authorization"] = f"token {settings.github.GITHUB_TOKEN}"

            response = requests.get(api_url, headers=headers)
            response.raise_for_status()

            repo_data = response.json()

            # 提取需要的信息
            tags = []
            if "topics" in repo_data:
                tags = repo_data["topics"]
            elif "language" in repo_data and repo_data["language"]:
                tags = [repo_data["language"]]

            return {
                "repository_url": repo_url,
                "description_recommend": repo_data.get("description", ""),
                "tags": tags,
                "image_url": repo_data.get("owner", {}).get("avatar_url", None),
                "status": ProjectStatusEnum.PUBLISHED.value
            }

        except Exception as e:
            logger.warning(f"无法从GitHub API获取仓库信息: {str(e)}")
            # 返回基本信息
            return {
                "repository_url": repo_url,
                "tags": [],
                "status": ProjectStatusEnum.PUBLISHED.value
            }

    async def get_analysis_logs(self, project_id: str) -> Optional[Dict[str, Any]]:
        """
        获取项目分析日志流

        Args:
            project_id: 项目ID

        Returns:
            Optional[Dict[str, Any]]: 分析日志信息

        Raises:
            Exception: 当分析机掉线或无法获取日志时抛出异常
        """
        try:
            # 检查分析机地址是否配置
            if not settings.distributed.AI_ANALYZER_ENDPOINT:
                logger.error("分析机地址未配置")
                raise Exception("分析机地址未配置")

            # 调用分析机的日志接口
            async with aiohttp.ClientSession() as session:
                url = f"{settings.distributed.AI_ANALYZER_ENDPOINT}/api/v1/remote/agent/health-check"

                try:
                    params = {"publisher_project_id": project_id}
                    async with session.get(url, params=params, timeout=10) as response:
                        if response.status == 200:
                            logs_data = await response.json()

                            if logs_data.get("code") == 200:
                                return logs_data.get("data", {})
                            else:
                                logger.error(f"获取分析状态失败: {logs_data}")
                                raise Exception(f"获取分析状态失败: {logs_data}")
                        else:
                            logger.error(f"获取分析日志状态失败，状态码: {response.status}")
                            raise Exception(f"获取分析日志状态失败，状态码: {response.status}")

                except asyncio.TimeoutError:
                    logger.error("获取分析日志请求超时")
                    raise Exception("获取分析日志请求超时，分析机可能已掉线")
                except Exception as e:
                    logger.error(f"获取分析日志时发生错误: {str(e)}")
                    raise Exception(f"获取分析日志时发生错误: {str(e)}")

        except Exception as e:
            logger.error(f"获取分析日志时发生错误: {str(e)}")
            # 直接抛出异常，停止分析
            raise e

def _infer_project_type(self, project_path: str, project_name: str) -> str:
    """根据项目路径和名称智能推断项目类型"""
    project_name_lower = project_name.lower()
    
    # 根据项目名称关键词判断
    if any(keyword in project_name_lower for keyword in ['ai', 'ml', 'neural', 'deep', 'learning']):
        return 'ai_ml'
    elif any(keyword in project_name_lower for keyword in ['mobile', 'app', 'android', 'ios', 'flutter']):
        return 'mobile_app'
    elif any(keyword in project_name_lower for keyword in ['api', 'service', 'backend', 'server']):
        return 'backend_api'
    elif any(keyword in project_name_lower for keyword in ['data', 'analysis', 'analytics', 'bi', 'dashboard']):
        return 'data_analysis'
    elif any(keyword in project_name_lower for keyword in ['devops', 'ci', 'cd', 'deploy', 'ops']):
        return 'devops'
    else:
        return 'web_app'  # 默认类型