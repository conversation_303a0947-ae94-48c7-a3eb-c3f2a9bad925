"""
结果聚合器

处理跨内容类型的结果聚合、排序和分页逻辑。
"""
import logging
from typing import Dict, Any, List, Optional, Callable
from .types import ContentItem, SearchRequest, ContentType, SortOrder

logger = logging.getLogger(__name__)


class ResultAggregator:
    """结果聚合器"""
    
    def __init__(self):
        """初始化结果聚合器"""
        self.sort_strategies = {
            "_score": self._sort_by_score,
            "title": self._sort_by_title,
            "created_at": self._sort_by_created_at,
            "updated_at": self._sort_by_updated_at,
            "popularity": self._sort_by_popularity,
            "relevance": self._sort_by_relevance
        }
    
    def aggregate_results(self, 
                         results_by_type: Dict[ContentType, List[ContentItem]],
                         request: SearchRequest) -> List[ContentItem]:
        """
        聚合多种内容类型的搜索结果
        
        Args:
            results_by_type: 按内容类型分组的结果
            request: 搜索请求
            
        Returns:
            List[ContentItem]: 聚合后的结果列表
        """
        if not results_by_type:
            return []
        
        # 合并所有结果
        all_items = []
        for content_type, items in results_by_type.items():
            all_items.extend(items)
        
        if not all_items:
            return []
        
        # 应用排序
        sorted_items = self._apply_sorting(all_items, request)
        
        # 应用分页
        paginated_items = self._apply_pagination(sorted_items, request)
        
        return paginated_items
    
    def merge_aggregations(self, 
                          aggregations_by_type: Dict[ContentType, Dict[str, Any]]) -> Dict[str, Any]:
        """
        合并多种内容类型的聚合结果
        
        Args:
            aggregations_by_type: 按内容类型分组的聚合结果
            
        Returns:
            Dict[str, Any]: 合并后的聚合结果
        """
        merged_aggregations = {}
        
        for content_type, aggregations in aggregations_by_type.items():
            type_prefix = content_type.value
            
            for agg_name, agg_data in aggregations.items():
                # 为每个内容类型的聚合添加前缀
                prefixed_name = f"{type_prefix}_{agg_name}"
                merged_aggregations[prefixed_name] = agg_data
        
        # 添加全局统计
        merged_aggregations["global_stats"] = self._calculate_global_stats(aggregations_by_type)
        
        return merged_aggregations
    
    def calculate_content_type_distribution(self, 
                                          items: List[ContentItem]) -> Dict[str, Any]:
        """
        计算内容类型分布
        
        Args:
            items: 内容项列表
            
        Returns:
            Dict[str, Any]: 内容类型分布统计
        """
        distribution = {}
        total_count = len(items)
        
        for item in items:
            content_type = item.content_type.value
            if content_type not in distribution:
                distribution[content_type] = {
                    "count": 0,
                    "percentage": 0.0,
                    "avg_score": 0.0,
                    "max_score": 0.0,
                    "min_score": float('inf')
                }
            
            stats = distribution[content_type]
            stats["count"] += 1
            stats["avg_score"] += item.score
            stats["max_score"] = max(stats["max_score"], item.score)
            stats["min_score"] = min(stats["min_score"], item.score)
        
        # 计算百分比和平均分数
        for content_type, stats in distribution.items():
            if stats["count"] > 0:
                stats["percentage"] = (stats["count"] / total_count) * 100
                stats["avg_score"] = stats["avg_score"] / stats["count"]
                if stats["min_score"] == float('inf'):
                    stats["min_score"] = 0.0
        
        return {
            "distribution": distribution,
            "total_items": total_count,
            "content_types_count": len(distribution)
        }
    
    def _apply_sorting(self, items: List[ContentItem], request: SearchRequest) -> List[ContentItem]:
        """
        应用排序逻辑
        
        Args:
            items: 内容项列表
            request: 搜索请求
            
        Returns:
            List[ContentItem]: 排序后的内容项列表
        """
        if not items:
            return items
        
        sort_by = request.sort_by or "_score"
        sort_strategy = self.sort_strategies.get(sort_by, self._sort_by_score)
        
        try:
            return sort_strategy(items, request.sort_order)
        except Exception as e:
            logger.warning(f"排序失败，使用默认分数排序: {str(e)}")
            return self._sort_by_score(items, request.sort_order)
    
    def _apply_pagination(self, items: List[ContentItem], request: SearchRequest) -> List[ContentItem]:
        """
        应用分页逻辑
        
        Args:
            items: 内容项列表
            request: 搜索请求
            
        Returns:
            List[ContentItem]: 分页后的内容项列表
        """
        start_idx = (request.page - 1) * request.page_size
        end_idx = start_idx + request.page_size
        return items[start_idx:end_idx]
    
    def _sort_by_score(self, items: List[ContentItem], sort_order: SortOrder) -> List[ContentItem]:
        """按分数排序"""
        reverse = sort_order == SortOrder.DESC
        return sorted(items, key=lambda x: x.score, reverse=reverse)
    
    def _sort_by_title(self, items: List[ContentItem], sort_order: SortOrder) -> List[ContentItem]:
        """按标题排序"""
        reverse = sort_order == SortOrder.DESC
        return sorted(items, key=lambda x: x.title.lower(), reverse=reverse)
    
    def _sort_by_created_at(self, items: List[ContentItem], sort_order: SortOrder) -> List[ContentItem]:
        """按创建时间排序"""
        reverse = sort_order == SortOrder.DESC
        return sorted(items, 
                     key=lambda x: x.metadata.get("created_at", ""), 
                     reverse=reverse)
    
    def _sort_by_updated_at(self, items: List[ContentItem], sort_order: SortOrder) -> List[ContentItem]:
        """按更新时间排序"""
        reverse = sort_order == SortOrder.DESC
        return sorted(items, 
                     key=lambda x: x.metadata.get("updated_at", ""), 
                     reverse=reverse)
    
    def _sort_by_popularity(self, items: List[ContentItem], sort_order: SortOrder) -> List[ContentItem]:
        """按热度排序"""
        reverse = sort_order == SortOrder.DESC
        
        def get_popularity_score(item: ContentItem) -> float:
            """计算热度分数"""
            metadata = item.metadata or {}
            
            if item.content_type == ContentType.ARTICLE:
                # 文章热度 = 阅读数 * 0.1 + 点赞数 * 2 + 评论数 * 3 + 收藏数 * 5
                read_count = metadata.get("read_count", 0)
                like_count = metadata.get("like_count", 0)
                comment_count = metadata.get("comment_count", 0)
                collect_count = metadata.get("collect_count", 0)
                
                return read_count * 0.1 + like_count * 2 + comment_count * 3 + collect_count * 5
                
            elif item.content_type == ContentType.PROJECT:
                # 项目热度 = 查看数 * 0.1 + 点赞数 * 3 + 卡片数 * 1
                views_count = metadata.get("views_count", 0)
                likes_count = metadata.get("likes_count", 0)
                cards_count = metadata.get("cards_count", 0)
                
                return views_count * 0.1 + likes_count * 3 + cards_count * 1
            
            return 0.0
        
        return sorted(items, key=get_popularity_score, reverse=reverse)
    
    def _sort_by_relevance(self, items: List[ContentItem], sort_order: SortOrder) -> List[ContentItem]:
        """按相关性排序（综合分数和内容类型权重）"""
        reverse = sort_order == SortOrder.DESC
        
        def get_relevance_score(item: ContentItem) -> float:
            """计算相关性分数"""
            base_score = item.score
            
            # 根据内容类型调整权重
            type_weight = 1.0
            if item.content_type == ContentType.ARTICLE:
                type_weight = 1.2  # 文章权重稍高
            elif item.content_type == ContentType.PROJECT:
                type_weight = 1.0  # 项目基础权重
            
            # 考虑高亮匹配数量
            highlights = item.highlights or {}
            highlight_bonus = highlights.get("total_matches", 0) * 0.1
            
            return base_score * type_weight + highlight_bonus
        
        return sorted(items, key=get_relevance_score, reverse=reverse)
    
    def _calculate_global_stats(self, 
                               aggregations_by_type: Dict[ContentType, Dict[str, Any]]) -> Dict[str, Any]:
        """计算全局统计信息"""
        total_items = 0
        content_types = []
        
        for content_type, aggregations in aggregations_by_type.items():
            content_types.append(content_type.value)
            
            # 尝试从聚合中提取总数
            if "total" in aggregations:
                total_items += aggregations["total"]
        
        return {
            "total_items_across_types": total_items,
            "searched_content_types": content_types,
            "content_types_count": len(content_types)
        }
    
    def register_sort_strategy(self, sort_key: str, strategy: Callable):
        """
        注册自定义排序策略
        
        Args:
            sort_key: 排序键
            strategy: 排序策略函数
        """
        self.sort_strategies[sort_key] = strategy
        logger.info(f"注册自定义排序策略: {sort_key}")
    
    def get_available_sort_strategies(self) -> List[str]:
        """
        获取可用的排序策略列表
        
        Returns:
            List[str]: 排序策略键列表
        """
        return list(self.sort_strategies.keys())
