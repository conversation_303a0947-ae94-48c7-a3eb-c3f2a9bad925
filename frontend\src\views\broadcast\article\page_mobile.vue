<template>
  <div
    class="article-mobile"
    ref="articleContainerRef"
    @click="handleClose($event)">
    <div class="article" ref="articleRef" id="article">
      <div class="title">{{ article.summary }}</div>
      <div style="display: flex; align-items: center; margin-top: 15px"></div>
      <div class="meta">
        <img src="@/assets/images/icon25.png" />
        <span>{{ dayjs(article.created_at).format("YYYY/MM/DD") }}</span>
        <img src="@/assets/images/icon26.png" />
        <span>{{ article.read_count }}</span>
        <img src="@/assets/images/icon27.png" />
        <span>{{ article.comment_count }}</span>
        <div class="meta-favorite" @click.stop="handleCollect">
          <Button1
            class="meta-favorite-icon"
            width="18px"
            height="18px"
            gray
            :filled="article.is_collected"></Button1>
          <span>{{ article.collect_count }}</span>
        </div>
      </div>
      <div class="tag">
        <div
          v-for="(item, index) in article.tags"
          :key="index"
          class="tag-item"
          @click="handleTags(item)">
          {{ item }}
        </div>
      </div>
      <div class="article-detail">
        <MarkdownPreview
          class="content"
          :text="article.content"></MarkdownPreview>
        <div class="comment-container" v-if="msgList.length">
          <div style="font-size: 14px; color: #000000; margin-bottom: 10px">
            评论&nbsp;&nbsp;{{ article.comment_count }}
          </div>
          <comment :data="msgList" :sendName="''" />
        </div>
        <div
          class="send-comment"
          @click="handleShowComment"
          v-if="!userStore.showCommentInput">
          <span>写评论</span>
          <img src="@/assets/images/edit.png" />
        </div>
        <div v-else style="margin-top: 15px; position: relative">
          <el-input
            ref="textareaRef"
            class="textarea"
            v-model="textarea"
            autofocus
            :rows="3"
            maxlength="1000"
            type="textarea"
            show-word-limit
            placeholder="发表评论" />
          <el-popover placement="top" :width="306" popper-class="emoji-popover">
            <div>
              <EmojiPicker
                :hide-search="true"
                :native="true"
                @select="onSelectEmoji"
                :disabled-groups="[
                  'travel_places',
                  'flags',
                  'symbols',
                  'objects',
                  'activities',
                ]"
                :hide-group-names="true" />
            </div>
            <template #reference>
              <img
                class="emoji"
                src="@/assets/images/emoji.png"
                @click.stop="handleEmoji" />
            </template>
          </el-popover>
          <img
            class="emoji"
            src="@/assets/images/guguda.png"
            style="right: 100px"
            @click.stop="handleAiTe" />
          <div class="btns">
            <button class="cancel" @click.stop="handleCancel">取消</button>
            <button @click.stop="handleSend">发送</button>
          </div>
          <!-- <div
            v-if="showEmoji"
            style="position: absolute; top: 110px; right: 0px">
            <EmojiPicker
              :hide-search="true"
              :native="true"
              @select="onSelectEmoji"
              :disabled-groups="[
                'travel_places',
                'flags',
                'symbols',
                'objects',
                'activities',
              ]"
              :hide-group-names="true" />
          </div> -->
        </div>
      </div>

      <div
        class="related-projects"
        v-if="article.related_projects && article.related_projects.length">
        <div class="title">
          <img src="@/assets/images/icon28.png" />
          <span>关联项目</span>
        </div>
        <div class="cont">
          <p
            @click="
              handleLink(
                checkJson(item) ? checkJson(item).url : checkJson(item)
              )
            "
            v-for="(item, index) in article.related_projects"
            :key="index">
            {{ checkJson(item) ? checkJson(item).title : checkJson(item) }}
          </p>
        </div>
      </div>
    </div>
    <HomeFooter style="background-color: transparent"></HomeFooter>
    <el-backtop
      :right="20"
      :bottom="110"
      :visibility-height="300"
      target=".article-mobile"
      style="z-index: 99" />
    <Share
      style="position: fixed; right: 20px; bottom: 60px"
      :project="article"></Share>
    <WxQrCode />
  </div>
</template>

<script setup>
import WxQrCode from "@/components/WxQrCode";
import Share from "@/components/Share/index.vue";
import EmojiPicker from "vue3-emoji-picker";
import "vue3-emoji-picker/css";
import comment from "@/views/broadcast/article/comment.vue";
import HomeFooter from "@/layout/components/HomeFooter/index.vue";
import MarkdownPreview from "@/components/MarkdownPreview";
import Button1 from "@/components/Button/index1.vue";

import dayjs from "dayjs";

import useUserStore from "@/store/modules/user";
import { getToken } from "@/utils/auth";
import { ElMessage } from "element-plus";
import { getArticleList, commentTree, commentCreat } from "@/api/article";
import { addUserCollect, deleteUserCollect } from "@/api/broadcast";
import { getCurrentInstance, useTemplateRef } from "vue";
const { query } = useRoute();
const router = useRouter();
const userStore = useUserStore();
const textareaRef = useTemplateRef("textareaRef");
const { proxy } = getCurrentInstance();
const article = ref({});
const msgList = ref([]);
const initArticle = () => {
  getArticleList({
    article_id: query.id,
  }).then((res) => {
    if (res.code === 200) {
      article.value = res.data;
      console.log(article.value, "============");
    }
  });

  getComment();
};

const getComment = () => {
  commentTree({
    article_id: query.id,
    page: 1,
    page_size: 50,
    depth: 10,
  }).then((res) => {
    if (res.code === 200) {
      msgList.value = res.data.comments;
    }
  });
};

const handleShowComment = () => {
  showEmoji.value = false;
  if (getToken()) {
    userStore.showCommentInput = true;
    userStore.replyName = "";
    textarea.value = "";
    nextTick(() => {
      textareaRef.value.focus();
    });
  } else {
    ElMessage({
      message: "请登录后评论",
      type: "warning",
    });
    router.push("/mobileLogin");
  }
};

const textarea = ref("");
const handleSend = () => {
  showEmoji.value = false;
  commentCreat({
    content: textarea.value,
    parent_id: "",
    project_id: query.id,
  }).then((res) => {
    if (res.code === 200) {
      userStore.showCommentInput = false;
      textarea.value = "";
      userStore.refreshComment = true;
      const timer = setTimeout(() => {
        userStore.refreshComment = false;
        clearTimeout(timer);
      }, 1000);
    }
  });
};
/**
 * 处理json字符串报错
 * @param str json字符串
 */
const checkJson = (str) => {
  try {
    return JSON.parse(str);
  } catch (error) {
    return "";
  }
};
watch(
  () => userStore.refreshComment,
  (newVal) => {
    if (newVal) {
      getComment();
    }
  }
);

const handleCancel = () => {
  userStore.showCommentInput = false;
  showEmoji.value = false;
};
/**
 * 用户收藏
 */
const handleCollect = async () => {
  // 判断用户是否登录，如果为登录，则提示用户去登录
  if (!userStore?.name) {
    proxy.$modal.msgWarning("登录后即可收藏");
    return;
  }
  let res = {};
  if (article.value.is_collected) {
    res = await deleteUserCollect({ article_id: article.value.id });
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已取消收藏");
      article.value.is_collected = !article.value.is_collected;
      article.value.collect_count--;
    }
  } else {
    res = await addUserCollect({ article_id: article.value.id });
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已收藏");
      article.value.is_collected = !article.value.is_collected;
      article.value.collect_count++;
    }
  }
};

const handleLink = (url) => {
  if (url) {
    window.open(url);
  }
};

const showEmoji = ref(false);
const handleEmoji = () => {
  textareaRef.value.focus();
};

const handleClose = (e) => {
  console.dir(e.target, "========");

  if (
    e.target.nodeName === "SPAN" &&
    e.target.parentNode.nodeName === "BUTTON"
  ) {
    showEmoji.value = true;
    return;
  }

  if (
    e.target.className === "v3-body-inner" ||
    e.target.className === "v3-body"
  ) {
    showEmoji.value = true;
    return;
  }

  showEmoji.value = false;
};

const handleAiTe = () => {
  textarea.value = textarea.value + "@咕咕答";
};

const onSelectEmoji = (emoji) => {
  textarea.value = textarea.value + emoji.i;
  textareaRef.value.focus();
};

/**
 * 标签跳转到首页进行搜索
 * @param item
 */
const handleTags = (item) => {
  router.push({
    path: "/broadcast/mobileSearch",
    query: { searchKeyMobile: item, tab: "zixun" },
  });
};
const articleRef = useTemplateRef("articleRef");
const articleContainerRef = useTemplateRef("articleContainerRef");
onMounted(() => {
  initArticle();
  articleRef.value.style.minHeight =
    articleContainerRef.value.clientHeight - 40 + "px";
});
userStore.showCommentInput = false;
</script>
<style scoped lang="scss">
::v-deep(.v3-emoji-picker .v3-footer) {
  display: none;
}
::v-deep(.v3-emoji-picker .v3-header) {
  display: none;
}
::v-deep(.v3-emoji-picker) {
  box-shadow: none;
}
.article-mobile {
  position: fixed;
  top: calc(var(--navbar-height));
  bottom: 0;
  left: 0;
  right: 0;
  overflow: auto;
}
.article {
  padding: 25px 20px 0;
  .title {
    width: 80%;
    white-space: nowrap; /* 禁止换行 */
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis; /* 显示省略号 */
    font-size: 18px;
    color: #4c6d7d;
    font-weight: 800;
  }
  img {
    width: 16px;
    margin-right: 5px;
  }

  span {
    font-size: 14px;
    color: #656464;
  }
  .meta {
    display: flex;
    align-items: center;
    margin-top: 5px;
    font-size: 14px;
    color: #656464;
    img {
      width: 16px;
      margin-right: 5px;
    }

    span {
      margin-right: 25px;
    }
  }

  .tag {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 4px 16px;
    font-size: 12px;
    color: #60848d;

    .tag-item {
      padding: 2px 4px;
      border-radius: 4px 4px 4px 4px;
      font-weight: 400;
      font-size: 14px;
      color: #6e92a4;
      background: #ffffff;
      border: 1px solid #c0d9dd;
    }
  }

  .article-detail {
    margin: 25px 0;
    padding: 5px 15px;
    padding-bottom: 15px;
    background: #f6f6f6;
    border-radius: 8px;

    .content {
      ::v-deep(.md-editor-preview) {
        font-size: 14px;
        color: #656464;
        background-color: #f6f6f6;
      }
    }

    .comment-container {
      margin-top: 15px;
    }
  }

  .send-comment {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    text-align: right;
    font-size: 14px;
    color: #4c6d7d;
    margin-top: 15px;
    cursor: pointer;

    span {
      line-height: 19px;
    }

    img {
      width: 19px;
      height: 19px;
      margin-right: 4px;
    }
  }
  .emoji {
    width: 20px;
    position: absolute;
    top: 45px;
    right: 70px;
    cursor: pointer;
  }
  ::v-deep(.v3-emoji-picker .v3-footer) {
    display: none;
  }
  ::v-deep(.v3-emoji-picker .v3-header) {
    display: none;
  }
  .textarea {
    width: 100%;
    margin-top: 5px;
    border-radius: 5px;
    font-size: 12px;
  }

  .btns {
    display: flex;
    flex-direction: row-reverse;

    button {
      width: 60px;
      height: 32px;
      margin-top: 5px;
      border-radius: 4px;
      background-color: #1e80ff;
      font-size: 12px;
      color: #ffffff;
      text-align: center;
      line-height: 32px;
      border: none;
      cursor: pointer;
    }

    .cancel {
      background-color: #fff;
      color: #1e80ff;
      margin-left: 8px;
      box-shadow: 0 0 0 1px #1e80ff;
    }
  }

  .related-projects {
    width: 100%;
    font-size: 16px;
    color: #000000;
    margin: 20px auto;

    .title {
      display: flex;
      align-items: center;

      img {
        width: 20px;
        margin-right: 5px;
      }
    }

    .cont {
      padding: 20px;
      margin-top: 10px;
      background: #fafbfb;
      border-radius: 4px 4px 4px 4px;
      box-sizing: border-box;

      p {
        margin: 0;
        margin-bottom: 8px;
        font-size: 14px;
        color: #000000;
        font-weight: 500;
        cursor: pointer;
      }

      p:last-of-type {
        margin-bottom: 0;
      }
    }
    // p {
    //   margin: 0;
    //   font-size: 12px;
    //   color: #000000;
    //   font-weight: 500;
    // }

    // p:last-of-type {
    //   margin-top: 5px;
    //   font-weight: 800;
    //   cursor: pointer;
    // }
  }
}

.meta-favorite {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 14px;
  font-weight: normal;
  color: #646464;
  height: 100%;
  width: fit-content;
  cursor: pointer;
  .meta-favorite-icon {
    width: 18px;
    height: 18px;
    margin-right: 4px;
  }
  .meta-favorite-text {
    position: relative;
    top: 1px;
  }
}
</style>
