<template>
  <div
    @click="handleClose($event)"
    style="
      background:
        radial-gradient(at 20% 0%, rgba(202, 229, 245, 0.6), transparent 20%),
        radial-gradient(at 60% -15%, rgba(202, 229, 245, 0.7), transparent 40%),
        radial-gradient(
          at 100% 100%,
          rgba(160, 235, 248, 0.7),
          transparent 40%
        ),
        radial-gradient(at 0% 100%, rgb(243, 252, 202), transparent 30% 30%);
    ">
    <div>
      <div class="article">
        <div class="top">
          <div class="width" style="margin: 0 auto">
            <div class="title">
              {{ article.summary }}
            </div>
            <div class="meta">
              <img src="@/assets/images/icon25.png" />

              <span>
                {{ dayjs(article.created_at).format("YYYY/MM/DD") }}
              </span>
              <img src="@/assets/images/icon26.png" />
              <span>{{ article.read_count }}</span>
              <img src="@/assets/images/icon27.png" />
              <span>{{ article.comment_count }}</span>
              <div class="meta-favorite" @click.stop="handleCollect">
                <Button1
                  class="meta-favorite-icon"
                  width="18px"
                  height="18px"
                  gray
                  :filled="article.is_collected"></Button1>
                <span>{{ article.collect_count }}</span>
              </div>
            </div>
            <div class="tag">
              文章标签:
              <div
                class="tag-item"
                v-for="(item, index) in article.tags"
                :key="index"
                @click="handleTags(item)">
                {{ item }}
              </div>
            </div>
          </div>
        </div>
        <div
          style="
            min-height: calc(
              100vh - var(--navbar-height) - 152px - 40px - 16px
            );
          ">
          <div
            style="
              width: fit-content;
              position: relative;
              margin: 16px auto 0 auto;
            ">
            <div class="article-detail" @scroll="scroll" ref="article-detail">
              <el-backtop
                :right="40"
                :bottom="40"
                :visibility-height="200"
                target=".article-detail"
                @click="backTop" />
              <MarkdownPreview
                class="content"
                :text="article.content"
                style=""></MarkdownPreview>
              <div class="comment-container">
                <comment :data="msgList" :sendName="''" :articleId="query.id" />
              </div>

              <div
                class="send-comment"
                @click="handleShowComment"
                v-if="!userStore.showCommentInput">
                <span>写评论</span>
                <img src="@/assets/images/edit.png" />
              </div>
              <div v-else style="position: relative">
                <el-input
                  ref="textareaRef"
                  class="textarea"
                  v-model="textarea"
                  autofocus
                  :rows="3"
                  maxlength="1000"
                  type="textarea"
                  show-word-limit
                  placeholder="发表评论" />
                <img
                  class="emoji"
                  src="@/assets/images/guguda.png"
                  style="right: 100px"
                  @click.stop="handleAiTe" />
                <el-popover
                  :visible="visible"
                  placement="bottom"
                  :width="180"
                  :offset="30">
                  <p>Are you sure to delete this?</p>
                  <div style="text-align: right; margin: 0">
                    <el-button size="small" text @click="visible = false">
                      cancel
                    </el-button>
                    <el-button
                      size="small"
                      type="primary"
                      @click="visible = false">
                      confirm
                    </el-button>
                  </div>
                  <template #reference>
                    <el-button @click="visible = true">Delete555</el-button>
                  </template>
                </el-popover>
                <img
                  class="emoji"
                  src="@/assets/images/emoji.png"
                  @click.stop="handleEmoji" />
                <div class="btns">
                  <button class="cancel" @click.stop="handleCancel">
                    取消
                  </button>
                  <button @click.stop="handleSend">发送</button>
                </div>
                <div
                  v-if="showEmoji"
                  style="position: absolute; top: 70px; left: 50%">
                  <EmojiPicker
                    :hide-search="true"
                    :native="true"
                    @select="onSelectEmoji"
                    :disabled-groups="[
                      'travel_places',
                      'flags',
                      'symbols',
                      'objects',
                      'activities',
                    ]"
                    :hide-group-names="true" />
                </div>
              </div>
            </div>
          </div>
          <div
            class="related-projects"
            v-if="article.related_projects && article.related_projects.length">
            <div class="title">
              <img src="@/assets/images/icon28.png" />
              <span>关联项目</span>
            </div>
            <div class="cont">
              <p
                @click="
                  handleLink(
                    checkJson(item) ? checkJson(item).url : checkJson(item)
                  )
                "
                v-for="(item, index) in article.related_projects"
                :key="index">
                <el-link underline="never">
                  {{
                    checkJson(item) ? checkJson(item).title : checkJson(item)
                  }}
                </el-link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <HomeFooter style="background-color: transparent"></HomeFooter>
    <Share
      style="position: fixed; right: 40px; bottom: 60px"
      :project="article"></Share>
    <WxQrCode />
  </div>
</template>

<script setup>
import Share from "@/components/Share/index.vue";
import WxQrCode from "@/components/WxQrCode";
import EmojiPicker from "vue3-emoji-picker";
import "vue3-emoji-picker/css";
import Button1 from "@/components/Button/index1.vue";
import HomeFooter from "@/layout/components/HomeFooter/index.vue";
import { getArticleList, commentTree, commentCreat } from "@/api/article";
import { addUserCollect, deleteUserCollect } from "@/api/broadcast";
import useUserStore from "@/store/modules/user";
import dayjs from "dayjs";
import comment from "@/views/broadcast/article/comment.vue";
import { getToken } from "@/utils/auth";
import { ElMessage } from "element-plus";
import MarkdownPreview from "@/components/MarkdownPreview";
import { getCurrentInstance, nextTick, useTemplateRef } from "vue";
import { useThrottleFn } from "@vueuse/core";

const visible = ref(false);
const { query } = useRoute();
const article = ref({});
const userStore = useUserStore();
const textareaRef = useTemplateRef("textareaRef");
const articleDetail = useTemplateRef("article-detail");
const isScrolling = ref(false);
const msgList = ref([]);

const initArticle = () => {
  getArticleList({
    article_id: query.id,
  }).then((res) => {
    if (res.code === 200) {
      article.value = res.data;
    }
  });
  getComment();
};

const getComment = () => {
  commentTree({
    article_id: query.id,
    page: 1,
    page_size: 50,
    depth: 10,
  }).then((res) => {
    if (res.code === 200) {
      msgList.value = res.data.comments;
    }
  });
};

const textarea = ref("");

const handleShowComment = () => {
  showEmoji.value = false;
  if (getToken()) {
    userStore.showCommentInput = true;

    nextTick(() => {
      articleDetail.value.scrollTop = articleDetail.value.scrollHeight;
    });
    userStore.replyName = "";
    textarea.value = "";
    nextTick(() => {
      textareaRef.value.focus();
    });
  } else {
    ElMessage({
      message: "请登录后评论",
      type: "warning",
    });
    userStore.loginDialogVisible = true;
  }
};

const handleCancel = () => {
  userStore.showCommentInput = false;
  showEmoji.value = false;
};

/**
 *
 */
const scroll = useThrottleFn(() => {
  if (!isScrolling.value) {
    document.getElementsByClassName("app-main")[0].scroll({ top: 999 });
  }
}, 1000);

/**
 * 回到顶部
 */
const backTop = () => {
  articleContainer.value.scroll({ top: 0, behavior: "smooth" });
  isScrolling.value = true;
  const timer = setTimeout(() => {
    isScrolling.value = false;
    clearTimeout(timer);
  }, 1000);
};

const handleSend = () => {
  showEmoji.value = false;
  commentCreat({
    content: textarea.value,
    parent_id: "",
    project_id: query.id,
  }).then((res) => {
    if (res.code === 200) {
      userStore.showCommentInput = false;

      textarea.value = "";
      userStore.refreshComment = true;
      const timer = setTimeout(() => {
        userStore.refreshComment = false;
        clearTimeout(timer);
      }, 1000);
    }
  });
};

const handleClose = (e) => {
  console.dir(e.target, "========");

  if (
    e.target.nodeName === "SPAN" &&
    e.target.parentNode.nodeName === "BUTTON"
  ) {
    showEmoji.value = true;
    return;
  }

  if (
    e.target.className === "v3-body-inner" ||
    e.target.className === "v3-body"
  ) {
    showEmoji.value = true;
    return;
  }

  showEmoji.value = false;
};

/**
 * 处理json字符串报错
 * @param str json字符串
 */
const checkJson = (str) => {
  try {
    return JSON.parse(str);
  } catch (error) {
    return "";
  }
};

const { proxy } = getCurrentInstance();
/**
 * 用户收藏
 */
const handleCollect = async () => {
  // 判断用户是否登录，如果为登录，则提示用户去登录
  if (!userStore?.name) {
    proxy.$modal.msgWarning("登录后即可收藏");
    return;
  }
  let res = {};
  if (article.value.is_collected) {
    res = await deleteUserCollect({ article_id: article.value.id });
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已取消收藏");
      article.value.is_collected = !article.value.is_collected;
      article.value.collect_count--;
    }
  } else {
    res = await addUserCollect({ article_id: article.value.id });
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已收藏");
      article.value.is_collected = !article.value.is_collected;
      article.value.collect_count++;
    }
  }
};

const router = useRouter();
/**
 * 标签跳转到首页进行搜索
 * @param item
 */
const handleTags = (item) => {
  router.push({
    path: "/",
    query: { search: item, tab: "zixun" },
  });
};

const handleLink = (url) => {
  if (url) {
    window.open(url);
  }
};

const showEmoji = ref(false);
const handleEmoji = () => {
  showEmoji.value = !showEmoji.value;
};

const handleAiTe = () => {
  textarea.value = textarea.value + "@咕咕答";
};

const onSelectEmoji = (emoji) => {
  textarea.value = textarea.value + emoji.i;
};
watch(
  () => userStore.refreshComment,
  (newVal) => {
    if (newVal) {
      getComment();
    }
  }
);
onMounted(() => {
  initArticle();
});
userStore.showCommentInput = false;

//  初始化位置
</script>
<style scoped lang="scss">
.article {
  .width {
    width: 960px;
  }
  .meta {
    display: flex;
    align-items: center;
    margin-top: 15px;
    font-size: 14px;
    color: #656464;
    img {
      width: 16px;
      margin-right: 5px;
    }

    span {
      margin-right: 25px;
    }

    .meta-favorite {
      margin-left: 20px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      font-size: 14px;
      font-weight: normal;
      color: #646464;
      height: 100%;
      width: fit-content;
      cursor: pointer;
      .meta-favorite-icon {
        width: 18px;
        height: 18px;
        margin-right: 4px;
      }
      .meta-favorite-text {
        position: relative;
        top: 1px;
      }
    }
  }
  .top {
    min-height: 143px;
    padding: 32px 0;
    background: #eef0f1;

    .title {
      width: 80%;
      white-space: nowrap; /* 禁止换行 */
      overflow: hidden; /* 隐藏溢出内容 */
      text-overflow: ellipsis; /* 显示省略号 */
      font-size: 18px;
      color: #4c6d7d;
      font-weight: 800;
    }

    .time {
      margin-top: 12px;
      font-size: 14px;
      color: #656464;
    }

    .tag {
      margin-top: 10px;
      display: flex;
      flex-wrap: wrap;
      gap: 4px 16px;
      font-size: 12px;
      color: #60848d;
      .tag-item {
        padding: 2px 18px;
        background: #d9ecf0;
        border-radius: 4px 4px 4px 4px;
        cursor: pointer;
      }
    }
  }

  .article-detail {
    padding: 15px;
    width: 960px;
    max-height: calc(100vh - var(--navbar-height) - 40px - 40px - 144px - 16px);
    overflow: auto;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 8px 8px 0px 0px;
    border: 1px solid #e6e6e6;

    font-size: 14px;
    color: #000000;
    scrollbar-gutter: stable;

    ::v-deep(.el-backtop) {
      --el-backtop-bg-color: transparent !important;
      position: absolute;
    }

    .comment-container {
      margin-top: 15px;
      background: #f4f4f4;
      border-radius: 4px;
    }
    .send-comment {
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      text-align: right;
      font-size: 14px;
      color: #4c6d7d;
      margin-top: 15px;
      cursor: pointer;

      span {
        line-height: 19px;
      }

      img {
        width: 19px;
        height: 19px;
        margin-right: 4px;
      }
    }
    .emoji {
      width: 20px;
      position: absolute;
      top: 45px;
      right: 70px;
      cursor: pointer;
    }
    ::v-deep(.v3-emoji-picker .v3-footer) {
      display: none;
    }
    ::v-deep(.v3-emoji-picker .v3-header) {
      display: none;
    }
    ::v-deep(.v3-emoji-picker .v3-body span) {
      content: 0;
    }
    .textarea {
      width: 100%;
      margin-top: 5px;
      border-radius: 5px;
      font-size: 12px;
    }

    .btns {
      display: flex;
      flex-direction: row-reverse;

      button {
        width: 60px;
        height: 32px;
        margin-top: 5px;
        border-radius: 4px;
        background-color: #1e80ff;
        font-size: 12px;
        color: #ffffff;
        text-align: center;
        line-height: 32px;
        border: none;
        cursor: pointer;
      }

      .cancel {
        background-color: #fff;
        color: #1e80ff;
        margin-left: 8px;
        box-shadow: 0 0 0 1px #1e80ff;
      }
    }
  }
}

.related-projects {
  width: 960px;
  font-size: 16px;
  color: #000000;
  margin: 20px auto;

  .title {
    display: flex;
    align-items: center;

    img {
      width: 20px;
      margin-right: 5px;
    }
  }

  .cont {
    padding: 20px;
    margin-top: 10px;
    background: #fafbfb;
    border-radius: 4px 4px 4px 4px;
    box-sizing: border-box;
    p {
      margin: 0;
      margin-bottom: 8px;
      font-size: 14px;
      color: #000000;
      font-weight: 500;
      cursor: pointer;
    }

    p:last-of-type {
      margin-bottom: 0;
    }
  }
}
</style>
