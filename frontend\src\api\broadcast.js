import request from "@/utils/request";

// 检验仓库URL是否合法
export function validateRepo(data) {
  return request({
    url: "/github/validate-repo",
    method: "post",
    data: data,
  });
}
// 获取所有项目，首页展示用
export function getProjectlist(data) {
  return request({
    url: "/github/projects",
    method: "get",
    params: data,
  });
}
// 获取所有项目和文章，首页展示用
export function getProjectMixed(data) {
  return request({
    url: "/projects/mixed",
    method: "get",
    params: data,
  });
}
// 创建项目
export function addProject(data) {
  return request({
    url: "/github/project",
    method: "post",
    data: data,
  });
}
// 更新项目
export function updateProject(data) {
  return request({
    url: "/github/project?project_id=" + data.id,
    method: "put",
    data: data,
  });
}
// 删除项目
export function deleteProject(data) {
  return request({
    url: "/github/project?project_id=" + data.id,
    method: "delete",
  });
}
// 获取项目中可选的图片列表
export function getProjectImages(data) {
  return request({
    url: "/github/projects/images",
    method: "get",
    params: data,
  });
}
// 编辑项目中可选的图片列表
export function putProjectImages(data) {
  return request({
    url: "/github/projects/images",
    method: "put",
    data: data,
  });
}
// 获取项目树
export function getProjectTree(data) {
  return request({
    url: "/github/projects/trees",
    method: "get",
    params: data,
  });
}

// 获取卡片列表
export function getProjectCard(data) {
  return request({
    url: "/github/projects/cards",
    method: "get",
    params: data,
  });
}
// 批量更新卡片
export function batchUpdateProjectCard(data) {
  return request({
    url: "/github/projects/cards/batch",
    method: "post",
    data: data,
  });
}
// 获取文件列表
export function getProjectFile(data) {
  return request({
    url: "/github/projects/file",
    method: "get",
    params: data,
  });
}

// 点赞卡片
export function likeCard(data) {
  return request({
    url: "/github/projects/cards/action",
    method: "post",
    data: data,
  });
}
// 首页关键词搜索,只搜索项目
export function searchContent(data) {
  return request({
    url: "/github/search",
    method: "post",
    data: data,
  });
}
// 首页关键词联合搜索，可同时搜项目和文章
export function searchEs(data) {
  return request({
    url: "/universal-search",
    method: "post",
    data: data,
  });
}

// 筛选仓库项目
export function scanProjectOnline(data) {
  return request({
    url: "/githubflow/projects/scan",
    method: "get",
    params: data,
  });
}
// 获取分析队列列表
export function getflowStatus(data) {
  return request({
    url: "/githubflow/projects/getflowstatus",
    method: "get",
    params: data,
  });
}
// 往分析队列添加
export function normaldownload(data) {
  return request({
    url: "/githubflow/projects/normaldownload",
    method: "post",
    data: data,
  });
}
// 判断链接是否有效
export function validatelink(data) {
  return request({
    url: "/githubflow/projects/validatelink",
    method: "post",
    data: data,
  });
}
// 加急分析项目
export function fastsolve(data) {
  return request({
    url: "/githubflow/projects/fastsolve",
    method: "post",
    data: data,
  });
}
// 取消分析项目
export function cancelgeneration(data) {
  return request({
    url: "/githubflow/projects/cancelgeneration",
    method: "post",
    data: data,
  });
}
// 获取用户浏览日志，即历史记录
export function getUserAccessLog(data) {
  return request({
    url: "/github/projects/user-access-log",
    method: "get",
    params: data,
  });
}
// 用户添加收藏
export function addUserCollect(data) {
  return request({
    url: "/github/projects/user-collect",
    method: "post",
    data: data,
  });
}
// 用户取消收藏
export function deleteUserCollect(data) {
  return request({
    url: "/github/projects/user-collect",
    method: "delete",
    params: data,
  });
}
// 获取用户收藏
export function getUserCollect(data) {
  return request({
    url: "/github/projects/user-collect",
    method: "get",
    params: data,
  });
}

// 获取用户任务列表
export function getflowstatusCurrent(data) {
  return request({
    url: "/githubflow/projects/getflowstatus-current",
    method: "get",
    params: data,
  });
}

// 用户取消分析
export function cancelGeneration(data) {
  return request({
    url: "/githubflow/projects/cancelgeneration",
    method: "post",
    data: data,
  });
}

// 服务号绑定发送验证码
export function wxCode(data) {
  return request({
    url: "/wechat/verify-code",
    method: "get",
    data: data,
  });
}

// 获取系统消息
export function sysMsg(data) {
  return request({
    url: "/github/projects/system-message-log",
    method: "get",
    params: data,
  });
}

// 把消息变为已读
export function sysMsgRead(data) {
  return request({
    url: "/github/projects/system-message-log",
    method: "post",
    data: data,
  });
}

// 全部消息变为已读
export function sysMsgReadAll() {
  return request({
    url: "/github/projects/system-message-log",
    method: "put",
  });
}

// 收藏搜索
export function collectSearch(data) {
  return request({
    url: "/current-user/cards-search",
    method: "get",
    params: data,
  });
}
