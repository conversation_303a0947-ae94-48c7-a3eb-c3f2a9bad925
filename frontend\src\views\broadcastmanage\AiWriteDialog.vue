<template>
  <div>
    <el-dialog
      title="AI创作"
      v-model="visible"
      width="90vw"
      append-to-body
      destroy-on-close
      :close-on-press-escape="false"
      align-center>
      <div style="height: calc(100vh - 120px)">
        <el-splitter>
          <el-splitter-panel :size="300" :min="200">
            <div class="demo-panel left">
              <div class="left-top">
                <el-input
                  v-model="searchStr"
                  @keyup.enter="doSearch"
                  style="width: 100%; margin-bottom: 8px"
                  placeholder="搜索"
                  clearable />
                <div style="display: flex; align-items: center; width: 100%">
                  <div
                    style="
                      font-size: 14px;
                      white-space: nowrap;
                      margin-right: 6px;
                    ">
                    搜索源
                  </div>
                  <el-select
                    v-model="searchSources"
                    multiple
                    collapse-tags
                    collapse-tags-tooltip
                    placeholder="选择">
                    <template #header>
                      <el-checkbox
                        v-model="checkAll"
                        :indeterminate="indeterminate"
                        @change="handleCheckAll">
                        全选
                      </el-checkbox>
                    </template>
                    <el-option
                      v-for="item in searchSourcesOptions"
                      :disabled="!item.is_available"
                      :key="item.type"
                      :label="item.type"
                      :value="item.type" />
                  </el-select>
                </div>
              </div>
              <div class="left-bottom" v-loading="loading">
                <div
                  class="item"
                  :class="{ active: tabs.some((e) => e.id === item.id) }"
                  v-for="item in searchList"
                  @click="handleItemClick(item)">
                  <div class="item-left">
                    <div class="item-title" :title="item.title">
                      {{ item.title }}
                    </div>
                    <div class="item-source">{{ item.source_type }}</div>
                  </div>
                  <div class="item-right" title="关联文章">
                    <svg
                      class="icon"
                      :class="{ active: chatTag.some((e) => e.id === item.id) }"
                      viewBox="0 0 1024 1024"
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      @click.stop="handleAt(item)">
                      <path
                        d="M511.99936 1023.99872c-282.303647 0-511.99936-229.695713-511.99936-511.99936s229.695713-511.99936 511.99936-511.99936 511.99936 229.695713 511.99936 511.99936a509.695363 509.695363 0 0 1-37.567953 192.895759 51.199936 51.199936 0 0 1-94.911881-38.591952A410.047487 410.047487 0 0 0 511.99936 102.399872a410.047487 410.047487 0 0 0-409.599488 409.535488 410.047487 410.047487 0 0 0 591.67926 367.039541 51.199936 51.199936 0 0 1 45.631943 91.647886A507.263366 507.263366 0 0 1 511.99936 1023.99872"></path>
                      <path
                        d="M511.99936 341.311573A170.879786 170.879786 0 0 0 341.311573 511.99936 170.687787 170.687787 0 1 0 511.99936 341.311573m0 443.775446A273.407658 273.407658 0 0 1 238.911701 511.99936 273.407658 273.407658 0 0 1 511.99936 238.911701a273.023659 273.023659 0 1 1 0 546.111318"></path>
                      <path
                        d="M815.99898 836.990954c-9.343988 0-18.495977-0.959999-27.263966-2.751997-63.423921-13.119984-106.047867-69.631913-106.047867-140.607824V290.111637a51.199936 51.199936 0 0 1 102.399872 0v403.455496c0 13.183984 3.135996 35.967955 24.383969 40.38395 23.487971 4.799994 59.199926-14.655982 82.559897-68.671915a51.199936 51.199936 0 0 1 93.951883 40.57595c-40.06395 92.799884-109.823863 131.135836-169.983788 131.135836"></path>
                    </svg>
                  </div>
                </div>
                <div class="empty-content" v-if="searchList.length === 0">
                  <div>暂无内容</div>
                </div>
              </div>
            </div>
          </el-splitter-panel>
          <el-splitter-panel :min="500">
            <div class="demo-panel">
              <el-tabs
                v-model="tabsValue"
                type="border-card"
                class="demo-tabs"
                closable
                @tab-remove="removeTab">
                <el-tab-pane
                  class="demo-tab-pane"
                  :class="{
                    article: item.type === 'article',
                    webpage: item.type === 'webpage',
                  }"
                  v-for="item in tabs"
                  :key="item.name"
                  :label="item.title"
                  :name="item.name">
                  <template #label>
                    <span
                      style="
                        display: flex;
                        align-items: center;
                        max-width: 120px;
                        overflow: hidden;
                      "
                      :title="item.title">
                      <el-icon v-if="item.type === 'article'">
                        <Tickets />
                      </el-icon>
                      <el-icon v-if="item.type === 'webpage'">
                        <Reading />
                      </el-icon>
                      <span>{{ item.title }}</span>
                    </span>
                  </template>
                </el-tab-pane>
              </el-tabs>
              <div class="tab-content">
                <iFrame
                  v-if="curTab.type === 'webpage'"
                  :src="curTab.url"></iFrame>
                <div v-if="curTab.type === 'article'" style="height: 100%">
                  <MarkdownEditor
                    v-model="curTab.content"
                    height="calc(100% - 40px)"></MarkdownEditor>
                  <div class="action">
                    <el-button
                      class="link-btn"
                      type="primary"
                      @click="handle135">
                      去135编辑器
                    </el-button>
                    <el-button type="primary" @click="handlePublish">
                      发布文章
                    </el-button>
                  </div>
                </div>
              </div>
              <div class="empty-content" v-if="tabs.length === 0">
                <div>左侧搜索感兴趣的内容，</div>
                <div>右侧AI对话开始创作吧！</div>
              </div>
            </div>
          </el-splitter-panel>
          <el-splitter-panel :size="300" :min="200">
            <div class="demo-panel right">
              <div class="right-head">
                <div
                  style="
                    display: flex;
                    justify-content: flex-end;
                    max-width: calc(100% - 56px);
                    margin-right: 2px;
                  ">
                  <div
                    style="
                      white-space: nowrap;
                      overflow: hidden;
                      text-overflow: ellipsis;
                    ">
                    {{ curSession.title }}
                  </div>
                </div>
                <el-dropdown trigger="click">
                  <el-icon
                    title="切换历史会话"
                    style="height: 20px; cursor: pointer">
                    <ArrowDown />
                  </el-icon>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        style="
                          display: flex;
                          justify-content: space-between;
                          align-items: center;
                        "
                        v-for="item in sessions"
                        :class="{ bluetext: curSession.id === item.id }"
                        @click="handleSelectSession(item)">
                        <div>{{ item.title }}</div>
                        <el-icon
                          title="删除会话"
                          @click.stop="handleDeleteSession(item)"
                          class="delete-icon"
                          color="#ccc">
                          <Delete />
                        </el-icon>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
                <el-divider direction="vertical" />
                <el-button
                  @click="handleNewSession"
                  title="新会话"
                  type=""
                  link
                  :icon="Plus" />
              </div>
              <div class="right-top" ref="chatContainer">
                <div v-for="item in chatRecord" class="chat-item">
                  <div
                    :class="[
                      'chat-content',
                      item.role === 'user' ? 'user' : 'assistant',
                    ]">
                    <MarkdownPreview
                      :autoScroll="false"
                      backgroundColor="transparent"
                      :text="item.content"></MarkdownPreview>
                  </div>
                  <div v-if="item.role === 'assistant'" class="chat-action">
                    <el-icon
                      title="应用"
                      :size="20"
                      class="button"
                      @click="handleApply(item)">
                      <FolderAdd />
                    </el-icon>
                  </div>
                </div>
              </div>
              <div class="right-bottom">
                <div class="chatTagWrap" v-if="chatTag?.length > 0">
                  <div class="chatTag" v-for="(item, index) in chatTag">
                    <div class="text">{{ item.title }}</div>
                    <span class="close" @click="handleChatTagClose(index)">
                      x
                    </span>
                  </div>
                </div>
                <el-mention
                  ref="mentionRef"
                  class="chat-input"
                  v-model="chatStr"
                  type="textarea"
                  :autosize="{ minRows: 3, maxRows: 10 }"
                  :options="options"
                  placeholder="AI对话，输入@关联文章"
                  whole
                  @keyup.enter="handleSend"
                  @select="handleMentionSelect"
                  @whole-remove="handleWholeRemove" />
                <!-- <el-input
                    v-model="chatStr"
                    :rows="2"
                    type="textarea"
                    placeholder="请输入"
                    :autosize="{ minRows: 3, maxRows: 10 }" /> -->
                <div class="chat-option">
                  <div style="display: flex; align-items: center">
                    <div
                      style="
                        margin-right: 2px;
                        word-break: break-all;
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        line-clamp: 2;
                        -webkit-line-clamp: 2;
                        overflow: hidden;
                        text-overflow: ellipsis;
                      ">
                      {{ curModel.name }}
                    </div>
                    <el-dropdown trigger="click" style="flex: 1">
                      <el-icon
                        title="切换AI模型"
                        style="height: 20px; cursor: pointer">
                        <ArrowDown />
                      </el-icon>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item
                            v-for="item in models"
                            :class="{ bluetext: curModel.name === item.name }"
                            @click="handleSelectModel(item)">
                            {{ item.name }}
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                  <el-divider direction="vertical" />
                  <div class="send-btn" @click="handleSend">
                    <el-icon v-if="!isGenerating" :size="24" title="发送">
                      <Promotion />
                    </el-icon>
                    <div v-else title="停止生成">
                      <svg
                        viewBox="0 0 1024 1024"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24">
                        <path
                          d="M258.875 174.5h506.25c46.58203125 0 84.375 37.79296875 84.375 84.375v506.25c0 46.58203125-37.79296875 84.375-84.375 84.375H258.875c-46.58203125 0-84.375-37.79296875-84.375-84.375V258.875c0-46.58203125 37.79296875-84.375 84.375-84.375z"
                          fill="#ee5744"></path>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-splitter-panel>
        </el-splitter>
      </div>
    </el-dialog>
    <el-dialog
      title="发布文章"
      v-model="visible1"
      width="500px"
      append-to-body
      align-center>
      <el-checkbox-group v-model="checkList">
        <el-checkbox label="主站" value="a" />
        <el-checkbox label="服务号" value="b" />
        <el-checkbox label="公众号" value="c" />
      </el-checkbox-group>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm1">确认</el-button>
          <el-button @click="visible1 = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { Plus } from "@element-plus/icons-vue";
import {
  getDataSourcesList,
  queryDataSources,
  createArticle,
  updateArticle,
} from "@/api/article.js";
import {
  getModels,
  getSessions,
  creatSession,
  clearSession,
  deleteSession,
  getSessionById,
  updateSessionConfig,
} from "@/api/ai.js";
import useUserStore from "@/store/modules/user";
import iFrame from "@/components/iFrame";
import MarkdownEditor from "@/components/MarkdownEditor";
import MarkdownPreview from "@/components/MarkdownPreview";
import ClipboardJS from "clipboard";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";
import { useWebSocket } from "@vueuse/core";
import { nextTick, onBeforeMount, onMounted, watch, watchEffect } from "vue";
const { status, data, send, open, close } = useWebSocket(
  import.meta.env.VITE_APP_BASE_WS
);
watch(data, () => {
  let sysdata = JSON.parse(data.value);
  if (isGenerating.value && sysdata.type === "chat_chunk") {
    let tmp = chatRecord.value.find((e) => {
      return e.id === sysdata.data.message_id;
    });
    if (tmp) {
      tmp.content += sysdata.data.delta;
    } else {
      chatRecord.value.push({
        id: sysdata.data.message_id,
        role: "assistant",
        content: sysdata.data.delta,
      });
    }
  }
  if (sysdata.type === "chat_complete") {
    isGenerating.value = false;
  }
});
onBeforeUnmount(() => {
  close();
});
const emit = defineEmits();
const { proxy } = getCurrentInstance();
const userStore = useUserStore();
const visible = defineModel();
const props = defineProps({
  article: {
    type: Object,
    default: () => ({}),
  },
});
let loading = ref(false);
let models = ref([]); //右下角对话框可选的AI模型
let curModel = ref({}); //当前选中的AI模型
let sessions = ref([]); //右边上方可选的会话列表
let curSession = ref({}); //当前选中的会话
let isGenerating = ref(false);
let visible1 = ref(false);
let tabsValue = ref(""); // 中间主区域当前选中的tab选项值
let tabs = ref([]); // 中间主区域上面的tab选项
let curTab = computed(() => {
  return (
    tabs.value.find((e) => {
      return e.name === tabsValue.value;
    }) || {}
  );
});
let searchStr = ref("");
let searchList = ref([]); // 左边搜索出来展示的结果列表
let searchSources = ref([]); // 左边已选中的搜索源
let searchSourcesOptions = ref([]); // 左边所有可选的搜索源列表
let mentionRef = ref(null);
let chatTag = ref([]);
let chatStr = ref("");
let options = computed(() => {
  return (
    searchList.value.map((e) => {
      return { ...e, label: e.title, value: e.title };
    }) || []
  );
});
let chatRecord = ref([]); //右边窗口当前显示的对话内容;
let checkList = ref(["a", "b", "c"]);
let indeterminate = ref(false);
let checkAll = ref(false);
// 根据选择的项改变全选的显示状态
watch(searchSources, (val) => {
  if (val.length === 0) {
    checkAll.value = false;
    indeterminate.value = false;
  } else if (val.length === searchSourcesOptions.value.length) {
    checkAll.value = true;
    indeterminate.value = false;
  } else {
    indeterminate.value = true;
  }
});
watch(
  () => props.article,
  (val) => {
    if (val?.id) {
      tabsValue.value = val.id;
      tabs.value = [
        {
          title: val.title,
          name: val.id,
          content: val.content,
          type: "article",
          id: val.id,
        },
      ];
    } else {
      // 默认显示一个可编辑的内容
      let id = "cc" + uuidv4();
      tabsValue.value = id;
      tabs.value = [
        {
          title: "文章" + dayjs().format("YYYY-MM-DD"),
          name: id,
          content: "咕咕",
          type: "article",
          id: id,
        },
      ];
    }
  },
  { immediate: true }
);
const handleCheckAll = (val) => {
  indeterminate.value = false;
  if (val) {
    searchSources.value = searchSourcesOptions.value
      .filter((e) => e.is_available)
      .map((_) => _.type);
  } else {
    searchSources.value = [];
  }
};
const doSearch = () => {
  _queryDataSources();
};
const handleItemClick = (item) => {
  // 判断在tabs中是否有对应id的项，如果没有，添加一项，然后切换到这个项
  let tab = tabs.value.find((e) => {
    return e.id === item.id;
  });
  if (!tab) {
    tabs.value.push({
      title: item.title,
      name: item.id,
      content: item.title,
      type: "webpage",
      id: item.id,
      url: item.url,
    });
  }
  tabsValue.value = item.id;
};
const handleApply = (item) => {
  let tab = tabs.value.find((e) => {
    return e.id === item.id;
  });
  if (!tab) {
    tabs.value.push({
      title: item.content,
      name: item.id,
      content: item.content,
      type: "article",
      id: item.id,
    });
  }
  tabsValue.value = item.id;
};
const handleAt = (item) => {
  if (chatTag.value.some((e) => e.id === item.id)) {
    proxy.$modal.msgWarning("文章已关联");
    return;
  }
  chatTag.value.push(item);
};
const handleMentionSelect = (item) => {
  // const input =
  //   mentionRef.value.$el?.querySelector("textarea") || mentionRef.value;
  // console.log("aSelect>>", item, input.selectionStart);
  // push之前判断chatTag是否已存在item，如果存在，给个提示
  if (chatTag.value.some((e) => e.id === item.id)) {
    proxy.$modal.msgWarning("文章已关联");
    return;
  }
  chatTag.value.push(item);
  chatStr.value = chatStr.value.replace("@" + item.label + " ", "");
};
const handleWholeRemove = (item) => {
  console.log("aRemove>>", item);
};
const handleChatTagClose = (index) => {
  chatTag.value.splice(index, 1);
};
const handleSend = async () => {
  if (isGenerating.value) {
    isGenerating.value = false;
    send(
      JSON.stringify({
        type: "cancel",
      })
    );
    return;
  }
  if (!chatStr.value) {
    proxy.$modal.msgWarning("请输入内容");
    return;
  }
  if (!curSession.value.id) {
    let res = await _creatSession();
    // 如果没有标题，给一个默认标题
    if (!res.data.title) {
      res.data.title =
        chatStr.value.length > 10
          ? chatStr.value.slice(0, 10) + "..."
          : chatStr.value;
    }
    curSession.value = res.data;
    sessions.value.unshift(res.data);
  }
  chatRecord.value.push({
    id: chatRecord.value.length + 1,
    role: "user",
    content: chatStr.value,
  });
  send(
    JSON.stringify({
      type: "chat",
      data: {
        session_id: curSession.value.id,
        message: chatStr.value,
        config: {
          model_provider: curSession.value.model_provider,
          model_name: curSession.value.model_name,
          temperature: curSession.value.temperature,
          max_tokens: curSession.value.max_tokens,
        },
        user_id: userStore.userInfo.id,
      },
    })
  );
  isGenerating.value = true;
  chatStr.value = "";
  // nextTick(() => {
  //   let dom = document.querySelector(".right-top");
  //   // 获取dom的最大可滚动距离
  //   let maxScrollTop = dom.scrollHeight - dom.clientHeight;
  //   // 滚动到最大可滚动距离
  //   dom.scrollTop = maxScrollTop;
  // });
};
const handlePublish = () => {
  visible1.value = true;
};
const handle135 = () => {
  const clipboard = new ClipboardJS(".link-btn", {
    text: () => curTab.value.content || "",
  });
  clipboard.on("success", () => {
    clipboard.destroy();
    // console.log("11>>", curTab.value.content);
    window.open("https://www.135editor.com/beautify_editor.html", "_blank");
  });
};
const _getDataSourcesList = () => {
  getDataSourcesList().then((res) => {
    if (res.code == 200) {
      searchSourcesOptions.value = res.data?.data_sources || [];
    }
  });
};
const _queryDataSources = () => {
  loading.value = true;
  let params = {
    query: searchStr.value,
    source_types: searchSources.value || [],
    limit: 10,
  };
  queryDataSources(params)
    .then((res) => {
      if (res.code == 200) {
        let results_by_source = res.data?.results_by_source || [];
        let keys = Object.keys(results_by_source);
        let arr = [];
        keys.forEach((key) => {
          arr.push(...results_by_source[key]);
        });
        searchList.value = arr;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};
const removeTab = (targetName) => {
  if (tabsValue.value === targetName) {
    tabs.value.forEach((tab, index) => {
      if (tab.name === targetName) {
        const nextTab = tabs.value[index + 1] || tabs.value[index - 1];
        if (nextTab) {
          tabsValue.value = nextTab.name;
        }
      }
    });
  }

  tabs.value = tabs.value.filter((tab) => tab.name !== targetName);
};
const submitForm1 = () => {
  if (!(curTab.value.content.trim()?.length > 0)) {
    proxy.$modal.msgError("文章内容不能为空");
    return;
  }
  proxy.$modal.loading();
  if (props.article.id) {
    let params = {
      article_id: props.article.id,
      content: curTab.value.content,
    };
    updateArticle(params)
      .then(() => {
        proxy.$modal.msgSuccess("操作成功");
        visible1.value = false;
        visible.value = false;
        emit("refresh");
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  } else {
    let params = {
      title:
        curTab.value.title.length > 10
          ? curTab.value.title.slice(0, 10) + "..."
          : curTab.value.title,
      content: curTab.value.content,
    };
    createArticle(params)
      .then(() => {
        proxy.$modal.msgSuccess("操作成功");
        visible1.value = false;
        visible.value = false;
        emit("refresh");
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }
};
onMounted(async () => {
  _getDataSourcesList();
  await _getSessions();
  _getModels();
  // 当right-top加载完成的时候，让其滚动到最底部
  // nextTick(() => {
  //   let dom = document.querySelector(".right-top");
  //   let maxScrollTop = dom.scrollHeight - dom.clientHeight;
  //   dom.scrollTop = maxScrollTop;
  // });
});
const _getSessions = () => {
  getSessions({ user_id: userStore.userInfo.id }).then((res) => {
    if (res.code == 200) {
      sessions.value = res.data?.sessions || [];
      curSession.value = sessions.value[sessions.value.length - 1] || {};
      _getSessionById(curSession.value.id);
    }
  });
};
const _creatSession = () => {
  let params = {
    user_id: userStore.userInfo.id,
    system_prompt: "你是一个创意写作助手，请用中文回答问题。",
    config: {
      model_provider: curModel.value.provider,
      model_name: curModel.value.name,
      temperature: curModel.value.temperature,
      max_tokens: curModel.value.max_tokens,
      streaming: curModel.value.streaming,
    },
  };
  return creatSession(params).then((res) => {
    if (res.code == 200) {
      return res;
    }
  });
};
const _getSessionById = (id) => {
  getSessionById({ id }).then((res) => {
    if (res.code == 200) {
      chatRecord.value = res.data?.messages || [];
    }
  });
};
const _deleteSession = (id) => {
  deleteSession({ id }).then((res) => {
    if (res.code == 200) {
      sessions.value = sessions.value.filter((e) => e.id !== id);
      // 如果删除的session是当前选中的，清空当前选中
      if (curSession.value.id === id) {
        curSession.value = {};
        chatRecord.value = [];
      }
    }
  });
};
const _getModels = () => {
  getModels().then((res) => {
    if (res.code == 200) {
      models.value = res.data?.models || [];
      curModel.value = models.value[0] || {};
    }
  });
};
const _updateSessionConfig = () => {
  if (!curSession.value.id) {
    return;
  }
  updateSessionConfig({
    id: curSession.value.id,
    config: {
      config: {
        model_provider: curModel.value.provider,
        model_name: curModel.value.name,
        temperature: curModel.value.temperature,
        max_tokens: curModel.value.max_tokens,
      },
    },
  });
};
// 新会话，实际上并不会立即创建新会话，等用户输入第一次对话内容时再创建新会话
const handleNewSession = () => {
  curSession.value = {};
  chatRecord.value = [];
};
const handleSelectSession = (item) => {
  curSession.value = item;
  _getSessionById(item.id);
};
const handleDeleteSession = (item) => {
  proxy.$modal
    .confirm("确认删除?")
    .then(function () {
      return _deleteSession(item.id);
    })
    .then(() => {
      // _getSessions();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
};
const handleSelectModel = (item) => {
  curModel.value = item;
  _updateSessionConfig();
};

// ------------------开始自动滚到底的逻辑------------------
let chatContainer = ref(null);
let isAutoScrolling = ref(true);
let lastScrollTop = ref(0);
// 记录最大高度
let maxHeight = ref(0);
let interval = null;
let observer = null;
watch(
  chatRecord,
  () => {
    nextTick(() => {
      if (isAutoScrolling.value) {
        scrollToBottom();
      }
    });
  },
  { deep: true }
);
watchEffect(() => {
  if (chatContainer.value) {
    observer = observeContentChanges();
    chatContainer.value.addEventListener("scroll", handleScroll, {
      passive: true,
    });
  }
});
onUnmounted(() => {
  observer && observer.disconnect();
  interval && clearInterval(interval);
  chatContainer.value &&
    chatContainer.value.removeEventListener("scroll", handleScroll);
});
const handleScroll = () => {
  const { scrollTop, scrollHeight, clientHeight } = chatContainer.value;
  if (scrollTop > lastScrollTop.value - 1) {
    // 向下滚动，滚动到最底部开启自动滚动
    if (scrollTop + clientHeight > scrollHeight - 20) {
      isAutoScrolling.value = true;
    }
  } else if (scrollTop < lastScrollTop.value) {
    // 向上滚动，因为内容变短造成的向上滚动不关闭自动滚动
    if (scrollHeight >= maxHeight.value - 1) {
      isAutoScrolling.value = false;
    }
  }
  lastScrollTop.value = scrollTop;
};
const scrollToBottom = () => {
  if (isAutoScrolling.value && chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
  }
};
// 监听内容变化
const observeContentChanges = () => {
  const observer = new MutationObserver(() => {
    const currentHeight = chatContainer.value.scrollHeight;
    // 如果当前高度大于最大高度，更新最大高度并设置容器高度
    if (currentHeight > maxHeight.value) {
      maxHeight.value = currentHeight;
    }
  });
  // 监听内容变化
  observer.observe(chatContainer.value, {
    childList: true, // 监听子节点变化
    subtree: true, // 监听所有后代节点
  });
  // 返回 observer，以便在组件卸载时断开监听
  return observer;
};
// ------------------结束自动滚到底的逻辑------------------
</script>

<style scoped lang="scss">
.demo-panel {
  border: solid 1px #e4e7ed;
  height: 100%;
  position: relative;
  .tab-content {
    height: calc(100% - 40px);
    overflow: hidden;
    .action {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0 10px;
    }
  }
  &.left {
    display: flex;
    flex-direction: column;
  }
  .left-top {
    // height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 8px 10px;
    border-bottom: solid 1px #e4e7ed;
  }
  .left-bottom {
    overflow: auto;
    padding: 10px 0;
    flex: 1;
    position: relative;
    .item {
      padding: 2px 10px;
      border-top: solid 4px transparent;
      border-bottom: solid 4px transparent;
      border-left: solid 4px transparent;
      cursor: pointer;
      display: flex;
      align-items: center;
      &:hover {
        background-color: #f5f7fa;
        .item-left .item-title {
          color: #409eff;
        }
      }
      &.active {
        border-left: solid 4px #409eff;
        background-color: #ecf5ff;
      }
      & + .item {
        margin-top: 2px;
      }
      .item-left {
        flex: 1;
        .item-title {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          line-clamp: 2;
          -webkit-line-clamp: 2; /* 限制显示的行数 */
          overflow: hidden;
          text-overflow: ellipsis;
          position: relative;
          text-decoration: underline;
          color: #000;
        }
        .item-source {
          font-size: 12px;
          color: #999;
        }
      }
      .item-right {
        margin-left: 6px;
        padding: 2px;
        display: flex;
        align-items: center;
        .icon {
          fill: #999;
          &:hover,
          &.active {
            fill: #409eff;
          }
        }
      }
    }
  }
  &.right {
    display: flex;
    flex-direction: column;
  }
  .right-head {
    height: 40px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background-color: #fcfbfb;
    position: sticky;
    top: 0;
    border-bottom: solid 1px #e4e7ed;
    padding: 0 8px;
  }
  .right-top {
    overflow: auto;
    flex: 1;
    padding: 10px 0;
    scroll-behavior: smooth;
    .chat-item {
      & + .chat-item {
        margin-top: 10px;
      }
      .chat-content {
        border-radius: 8px;
        padding: 4px 6px;
        text-wrap: pretty;
        &.user {
          background-color: #409eff;
          color: #fff;
          margin-left: 40px;
          margin-right: 10px;
        }
        &.assistant {
          background-color: #eee;
          color: #000;
          margin-left: 10px;
          margin-right: 40px;
        }
      }
      .chat-action {
        margin: 4px 10px;
        .button {
          cursor: pointer;
          &:hover {
            color: #409eff;
          }
        }
      }
    }
  }
  .right-bottom {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px 10px 4px;
    border-top: solid 1px #e4e7ed;
    .chatTagWrap {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      width: 100%;
      margin-bottom: 4px;
      .chatTag {
        padding: 4px;
        background-color: #f4f4f5;
        border-radius: 4px;
        font-size: 12px;
        color: #999;
        cursor: pointer;
        display: flex;
        align-items: center;
        max-width: 100%;
        .text {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: calc(100% - 20px);
        }
        .close {
          font-size: 12px;
          width: 14px;
          height: 14px;
          line-height: 12px;
          border-radius: 50%;
          text-align: center;
          margin-left: 6px;
          &:hover {
            background-color: #999;
            color: #eee;
          }
        }
      }
    }
    .chat-option {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-top: solid 1px #e4e7ed;
      padding-top: 4px;
    }
    .send-btn {
      position: relative;
      left: 2px;
      top: 2px;
      background-color: #fff;
      cursor: pointer;
      &:hover {
        color: #409eff;
      }
    }
  }
  .empty-content {
    color: #999;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
  }
}
.delete-icon {
  cursor: pointer;
  margin-left: 4px;
  margin-right: 0;
  &:hover {
    color: #f56c6c;
  }
}
:deep(.bluetext.el-dropdown-menu__item) {
  color: #409eff;
}
:deep(.chat-input .el-textarea__inner) {
  box-shadow: none !important;
  padding: 0;
  min-height: 63px !important;
  resize: none;
}
:deep(.el-tabs__nav-scroll) {
  margin-top: 0;
}
:deep(.el-tabs__item) {
  padding: 0 10px;
  padding-left: 10px !important;
  padding-right: 10px !important;
}
:deep(.el-tabs__content) {
  padding: 0;
}
:deep(.el-tabs--border-card) {
  border: none;
}
</style>
