"""
通用搜索配置

定义搜索引擎的配置参数和常量。
"""
from typing import Dict, Any, List
from dataclasses import dataclass


@dataclass
class SearchEngineConfig:
    """搜索引擎配置"""
    
    # 基础配置
    max_page_size: int = 100
    default_page_size: int = 10
    max_query_length: int = 200
    
    # 分数阈值配置
    min_score_thresholds: Dict[str, float] = None
    
    # 高亮配置
    highlight_fragment_size: int = 150
    highlight_number_of_fragments: int = 3
    highlight_pre_tags: List[str] = None
    highlight_post_tags: List[str] = None
    
    # 权重配置
    boost_configs: Dict[str, Dict[str, float]] = None
    
    # 超时配置
    search_timeout_seconds: int = 30
    
    # 并发配置
    max_concurrent_searches: int = 5
    
    def __post_init__(self):
        """初始化默认值"""
        if self.min_score_thresholds is None:
            self.min_score_thresholds = {
                "article": 30.0,
                "project": 0.1
            }
        
        if self.highlight_pre_tags is None:
            self.highlight_pre_tags = ["<mark>"]
        
        if self.highlight_post_tags is None:
            self.highlight_post_tags = ["</mark>"]
        
        if self.boost_configs is None:
            self.boost_configs = {
                "article": {
                    "title": 3.0,
                    "summary": 2.5,
                    "content": 2.0,
                    "tags": 2.5,
                    "keywords": 1.5
                },
                "project": {
                    "name": 3.0,
                    "description_project": 2.0,
                    "description_recommend": 2.0,
                    "tags": 2.5
                }
            }


# 默认搜索引擎配置
DEFAULT_SEARCH_CONFIG = SearchEngineConfig()

# 内容类型映射
CONTENT_TYPE_MAPPINGS = {
    "article": {
        "index_name": "articles-*",
        "primary_fields": ["title", "content", "summary"],
        "searchable_fields": ["title", "content", "summary", "tags", "keywords"],
        "highlight_fields": ["title", "content", "summary", "tags", "keywords"],
        "sort_fields": [
            "_score", "title", "created_at", "updated_at", "published_at",
            "read_count", "like_count", "comment_count", "collect_count",
            "popularity_score", "interaction_rate", "word_count", "estimated_reading_time"
        ],
        "filter_fields": [
            "status", "tags", "is_public", "is_top", "date_range", "published_date_range",
            "read_count_range", "like_count_range", "comment_count_range",
            "collect_count_range", "popularity_score_range", "word_count_range",
            "reading_time_range", "related_projects"
        ]
    },
    "project": {
        "index_name": "projects-*",
        "primary_fields": ["name", "description_project", "description_recommend"],
        "searchable_fields": ["name", "description_project", "description_recommend", "tags"],
        "highlight_fields": ["name", "description_project", "description_recommend", "tags"],
        "nested_fields": {
            "cards": {
                "searchable_fields": ["cards.title", "cards.content"],
                "highlight_fields": ["cards.title", "cards.content"]
            }
        },
        "sort_fields": [
            "_score", "name", "created_at", "updated_at", "cards_count",
            "likes_count", "views_count"
        ],
        "filter_fields": [
            "status", "tags", "date_range", "cards_count_range",
            "likes_count_range", "views_count_range", "card_tags", "card_type", "card_status"
        ]
    }
}

# 搜索策略配置
SEARCH_STRATEGIES = {
    "default": {
        "description": "默认搜索策略",
        "text_matching": {
            "exact_match_boost": 3.0,
            "phrase_match_boost": 2.0,
            "fuzzy_match_boost": 1.0,
            "ngram_match_boost": 0.5
        },
        "language_detection": True,
        "auto_completion": True,
        "fuzzy_matching": True
    },
    "precise": {
        "description": "精确搜索策略",
        "text_matching": {
            "exact_match_boost": 5.0,
            "phrase_match_boost": 3.0,
            "fuzzy_match_boost": 0.5,
            "ngram_match_boost": 0.1
        },
        "language_detection": True,
        "auto_completion": False,
        "fuzzy_matching": False
    },
    "fuzzy": {
        "description": "模糊搜索策略",
        "text_matching": {
            "exact_match_boost": 2.0,
            "phrase_match_boost": 1.5,
            "fuzzy_match_boost": 2.0,
            "ngram_match_boost": 1.0
        },
        "language_detection": True,
        "auto_completion": True,
        "fuzzy_matching": True
    }
}

# 聚合配置
AGGREGATION_CONFIGS = {
    "article": {
        "popular_tags": {
            "terms": {"field": "tags", "size": 20}
        },
        "status_distribution": {
            "terms": {"field": "status", "size": 10}
        },
        "read_count_stats": {
            "stats": {"field": "read_count"}
        },
        "like_count_stats": {
            "stats": {"field": "like_count"}
        },
        "comment_count_stats": {
            "stats": {"field": "comment_count"}
        },
        "collect_count_stats": {
            "stats": {"field": "collect_count"}
        },
        "popularity_score_stats": {
            "stats": {"field": "popularity_score"}
        },
        "word_count_stats": {
            "stats": {"field": "word_count"}
        },
        "reading_time_stats": {
            "stats": {"field": "estimated_reading_time"}
        },
        "interaction_rate_stats": {
            "stats": {"field": "interaction_rate"}
        },
        "public_articles": {
            "filter": {"term": {"is_public": True}}
        },
        "top_articles": {
            "filter": {"term": {"is_top": True}}
        },
        "published_articles": {
            "filter": {"term": {"status": "published"}}
        },
        "monthly_distribution": {
            "date_histogram": {
                "field": "published_at",
                "calendar_interval": "month",
                "format": "yyyy-MM"
            }
        }
    },
    "project": {
        "popular_tags": {
            "terms": {"field": "tags", "size": 20}
        },
        "status_distribution": {
            "terms": {"field": "status", "size": 10}
        },
        "cards_count_stats": {
            "stats": {"field": "cards_count"}
        },
        "likes_count_stats": {
            "stats": {"field": "likes_count"}
        },
        "views_count_stats": {
            "stats": {"field": "views_count"}
        },
        "nested_cards_stats": {
            "nested": {"path": "cards"},
            "aggs": {
                "likes": {"sum": {"field": "cards.like"}},
                "collects": {"sum": {"field": "cards.collect"}},
                "dislikes": {"sum": {"field": "cards.dislike"}},
                "types_distribution": {"terms": {"field": "cards.card_type", "size": 10}}
            }
        }
    }
}

# 排序策略配置
SORT_STRATEGIES = {
    "_score": {
        "description": "按相关性分数排序",
        "default_order": "desc"
    },
    "title": {
        "description": "按标题排序",
        "default_order": "asc",
        "field_suffix": ".keyword"
    },
    "name": {
        "description": "按名称排序",
        "default_order": "asc",
        "field_suffix": ".keyword"
    },
    "created_at": {
        "description": "按创建时间排序",
        "default_order": "desc"
    },
    "updated_at": {
        "description": "按更新时间排序",
        "default_order": "desc"
    },
    "published_at": {
        "description": "按发布时间排序",
        "default_order": "desc"
    },
    "popularity": {
        "description": "按热度排序",
        "default_order": "desc",
        "custom_logic": True
    },
    "relevance": {
        "description": "按相关性排序",
        "default_order": "desc",
        "custom_logic": True
    }
}

# 验证规则
VALIDATION_RULES = {
    "query": {
        "max_length": 200,
        "min_length": 1,
        "allowed_chars": r'[\u4e00-\u9fff\w\s\-_.,!?()[\]{}"]'
    },
    "page": {
        "min_value": 1,
        "max_value": 1000
    },
    "page_size": {
        "min_value": 1,
        "max_value": 100,
        "default_value": 10
    },
    "filters": {
        "max_tags": 20,
        "max_string_length": 100
    }
}

# 性能配置
PERFORMANCE_CONFIG = {
    "cache": {
        "enabled": True,
        "ttl_seconds": 300,  # 5分钟
        "max_size": 1000
    },
    "timeout": {
        "search_timeout": 30,
        "aggregation_timeout": 60
    },
    "concurrency": {
        "max_concurrent_searches": 5,
        "max_concurrent_aggregations": 3
    }
}
